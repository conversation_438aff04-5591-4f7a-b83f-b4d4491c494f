#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 نظام البحث التعاوني عن الأسرار
===================================

نظام متقدم يستخدم ثلاثة مساعدين ذكيين للبحث عن الأسرار والمفاتيح:
- 𓅃 فريق حورس (Horus AI Team)
- 🤖 Gemini CLI 
- 🧠 Qwen CLI

📅 تاريخ الإنشاء: 2025-07-29
🔧 الإصدار: 1.0.0
"""

import os
import sys
import subprocess
import json
import re
from datetime import datetime
from pathlib import Path
import threading
import time

class CollaborativeSecretHunter:
    def __init__(self):
        self.project_root = os.getcwd()
        self.results = {
            "horus": {"status": "pending", "findings": [], "errors": []},
            "gemini": {"status": "pending", "findings": [], "errors": []},
            "qwen": {"status": "pending", "findings": [], "errors": []}
        }
        self.start_time = datetime.now()
        
        # أنماط البحث عن الأسرار
        self.secret_patterns = {
            "api_keys": [
                r"sk-[a-zA-Z0-9]{48}",  # OpenAI API keys
                r"AIza[0-9A-Za-z_-]{35}",  # Google API keys
                r"ya29\.[0-9A-Za-z_-]+",  # Google OAuth tokens
                r"AKIA[0-9A-Z]{16}",  # AWS Access Key IDs
                r"[0-9a-f]{32}",  # Generic 32-char hex keys
                r"[0-9a-f]{40}",  # Generic 40-char hex keys
            ],
            "passwords": [
                r"password\s*[:=]\s*['\"][^'\"]{8,}['\"]",
                r"passwd\s*[:=]\s*['\"][^'\"]{8,}['\"]",
                r"pwd\s*[:=]\s*['\"][^'\"]{8,}['\"]",
            ],
            "tokens": [
                r"token\s*[:=]\s*['\"][^'\"]{20,}['\"]",
                r"access_token\s*[:=]\s*['\"][^'\"]{20,}['\"]",
                r"refresh_token\s*[:=]\s*['\"][^'\"]{20,}['\"]",
            ],
            "secrets": [
                r"secret\s*[:=]\s*['\"][^'\"]{16,}['\"]",
                r"client_secret\s*[:=]\s*['\"][^'\"]{16,}['\"]",
                r"api_secret\s*[:=]\s*['\"][^'\"]{16,}['\"]",
            ]
        }
    
    def print_header(self):
        """طباعة رأس النظام"""
        print("=" * 80)
        print("🔍 نظام البحث التعاوني عن الأسرار")
        print("=" * 80)
        print(f"📅 الوقت: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📁 المجلد: {self.project_root}")
        print("👥 المساعدين: حورس + Gemini CLI + Qwen CLI")
        print("=" * 80)
    
    def create_search_prompts(self):
        """إنشاء البرومبتات للمساعدين الثلاثة"""
        
        # برومبت حورس - البحث في الملفات البرمجية
        horus_prompt = """
🔍 مهمة البحث عن الأسرار - فريق حورس

أحتاج منك البحث عن الأسرار والمفاتيح في الملفات البرمجية:

📋 مهمتك:
1. فحص جميع ملفات Python (.py)
2. فحص ملفات JavaScript (.js)
3. فحص ملفات JSON (.json)
4. البحث عن:
   - مفاتيح OpenAI (sk-...)
   - مفاتيح Google (AIza...)
   - كلمات المرور
   - رموز الوصول (tokens)

🎯 ركز على:
- ANUBIS_SYSTEM/
- HORUS_AI_TEAM/
- ANUBIS_HORUS_MCP/

⚠️ أبلغ عن أي ملف يحتوي على أسرار فوراً!
"""

        # برومبت Gemini - البحث في ملفات التكوين
        gemini_prompt = """
أحتاج مساعدتك في البحث عن الأسرار في مشروع Universal AI Assistants.

مهمتك المحددة:
1. فحص ملفات التكوين (.env, .json, .yaml, .yml)
2. فحص ملفات قواعد البيانات (.sql, .db)
3. فحص ملفات النصوص (.txt, .md)
4. البحث عن أنماط الأسرار:
   - API Keys: sk-*, AIza*, AKIA*
   - Passwords: password=*, passwd=*
   - Tokens: token=*, access_token=*
   - Secrets: secret=*, client_secret=*

أبلغني عن أي ملف يحتوي على هذه الأنماط مع اسم الملف والسطر المحدد.
"""

        # برومبت Qwen - البحث في الملفات المتنوعة
        qwen_prompt = """
مهمة أمنية عاجلة: البحث عن الأسرار في مشروع البرمجة

المطلوب:
1. فحص ملفات Docker (Dockerfile, docker-compose.yml)
2. فحص ملفات Shell (.sh, .ps1, .bat)
3. فحص ملفات HTML/CSS (.html, .css)
4. فحص أي ملفات أخرى قد تحتوي على:
   - مفاتيح API
   - كلمات مرور
   - رموز أمنية
   - معرفات سرية

أريد تقرير مفصل بكل ملف يحتوي على معلومات حساسة.
"""
        
        return {
            "horus": horus_prompt,
            "gemini": gemini_prompt,
            "qwen": qwen_prompt
        }
    
    def search_with_horus(self, prompt):
        """البحث باستخدام فريق حورس"""
        print("\n🔍 بدء البحث مع فريق حورس...")
        try:
            # محاولة تشغيل فريق حورس
            result = subprocess.run([
                "python", "HORUS_AI_TEAM/quick_start.py"
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                self.results["horus"]["status"] = "completed"
                self.results["horus"]["findings"].append("تم تشغيل فريق حورس بنجاح")
                print("✅ فريق حورس: متصل ونشط")
            else:
                self.results["horus"]["status"] = "error"
                self.results["horus"]["errors"].append(f"خطأ في تشغيل حورس: {result.stderr}")
                print("❌ فريق حورس: خطأ في الاتصال")
                
        except Exception as e:
            self.results["horus"]["status"] = "error"
            self.results["horus"]["errors"].append(str(e))
            print(f"❌ فريق حورس: {str(e)}")
    
    def search_with_gemini(self, prompt):
        """البحث باستخدام Gemini CLI"""
        print("\n🤖 بدء البحث مع Gemini CLI...")
        try:
            # تشغيل Gemini CLI
            process = subprocess.Popen([
                "gemini", prompt
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            stdout, stderr = process.communicate(timeout=90)
            
            if process.returncode == 0:
                self.results["gemini"]["status"] = "completed"
                self.results["gemini"]["findings"].append(stdout)
                print("✅ Gemini CLI: تم البحث بنجاح")
            else:
                self.results["gemini"]["status"] = "error"
                self.results["gemini"]["errors"].append(stderr)
                print("❌ Gemini CLI: خطأ في البحث")
                
        except Exception as e:
            self.results["gemini"]["status"] = "error"
            self.results["gemini"]["errors"].append(str(e))
            print(f"❌ Gemini CLI: {str(e)}")
    
    def search_with_qwen(self, prompt):
        """البحث باستخدام Qwen CLI"""
        print("\n🧠 بدء البحث مع Qwen CLI...")
        try:
            # محاولة تشغيل Qwen CLI
            process = subprocess.Popen([
                "qwen", prompt
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            stdout, stderr = process.communicate(timeout=90)
            
            if process.returncode == 0:
                self.results["qwen"]["status"] = "completed"
                self.results["qwen"]["findings"].append(stdout)
                print("✅ Qwen CLI: تم البحث بنجاح")
            else:
                self.results["qwen"]["status"] = "error"
                self.results["qwen"]["errors"].append(stderr)
                print("❌ Qwen CLI: خطأ في البحث")
                
        except Exception as e:
            self.results["qwen"]["status"] = "error"
            self.results["qwen"]["errors"].append(str(e))
            print(f"❌ Qwen CLI: {str(e)}")
    
    def manual_pattern_search(self):
        """البحث اليدوي باستخدام الأنماط"""
        print("\n🔍 بدء البحث اليدوي بالأنماط...")
        findings = []
        
        # البحث في جميع الملفات
        for root, dirs, files in os.walk(self.project_root):
            # تجاهل مجلدات معينة
            dirs[:] = [d for d in dirs if d not in ['.git', '__pycache__', 'node_modules', '.venv']]
            
            for file in files:
                file_path = os.path.join(root, file)
                rel_path = os.path.relpath(file_path, self.project_root)
                
                # فحص الملفات النصية فقط
                if self.is_text_file(file_path):
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read()
                            
                        # البحث عن الأنماط
                        for category, patterns in self.secret_patterns.items():
                            for pattern in patterns:
                                matches = re.finditer(pattern, content, re.IGNORECASE)
                                for match in matches:
                                    line_num = content[:match.start()].count('\n') + 1
                                    findings.append({
                                        "file": rel_path,
                                        "line": line_num,
                                        "category": category,
                                        "pattern": pattern,
                                        "match": match.group()[:50] + "..." if len(match.group()) > 50 else match.group()
                                    })
                                    
                    except Exception as e:
                        continue
        
        return findings
    
    def is_text_file(self, file_path):
        """فحص إذا كان الملف نصي"""
        text_extensions = {'.py', '.js', '.json', '.txt', '.md', '.yml', '.yaml', 
                          '.env', '.sql', '.sh', '.ps1', '.bat', '.html', '.css', 
                          '.xml', '.ini', '.cfg', '.conf'}
        
        return any(file_path.lower().endswith(ext) for ext in text_extensions)
    
    def run_collaborative_search(self):
        """تشغيل البحث التعاوني"""
        self.print_header()
        
        # إنشاء البرومبتات
        prompts = self.create_search_prompts()
        
        # تشغيل البحث بالتوازي
        threads = []
        
        # تشغيل حورس
        horus_thread = threading.Thread(target=self.search_with_horus, args=(prompts["horus"],))
        threads.append(horus_thread)
        
        # تشغيل Gemini
        gemini_thread = threading.Thread(target=self.search_with_gemini, args=(prompts["gemini"],))
        threads.append(gemini_thread)
        
        # تشغيل Qwen
        qwen_thread = threading.Thread(target=self.search_with_qwen, args=(prompts["qwen"],))
        threads.append(qwen_thread)
        
        # بدء جميع الخيوط
        for thread in threads:
            thread.start()
        
        # البحث اليدوي أثناء انتظار المساعدين
        print("\n🔍 تشغيل البحث اليدوي المتوازي...")
        manual_findings = self.manual_pattern_search()
        
        # انتظار انتهاء جميع المساعدين
        for thread in threads:
            thread.join(timeout=120)
        
        # إنشاء التقرير النهائي
        self.generate_final_report(manual_findings)
    
    def generate_final_report(self, manual_findings):
        """إنشاء التقرير النهائي"""
        print("\n" + "=" * 80)
        print("📊 تقرير البحث التعاوني عن الأسرار")
        print("=" * 80)
        
        # ملخص حالة المساعدين
        print("\n👥 حالة المساعدين:")
        for assistant, result in self.results.items():
            status_icon = "✅" if result["status"] == "completed" else "❌" if result["status"] == "error" else "⏳"
            print(f"   {status_icon} {assistant.upper()}: {result['status']}")
        
        # النتائج اليدوية
        print(f"\n🔍 البحث اليدوي: {len(manual_findings)} نتيجة")
        
        if manual_findings:
            print("\n⚠️ الأسرار المكتشفة:")
            for finding in manual_findings[:10]:  # أول 10 نتائج
                print(f"   📁 {finding['file']}:{finding['line']} - {finding['category']}")
                print(f"      🔍 {finding['match']}")
        else:
            print("✅ لم يتم العثور على أسرار في البحث اليدوي")
        
        # حفظ التقرير
        report_data = {
            "timestamp": self.start_time.isoformat(),
            "assistants_results": self.results,
            "manual_findings": manual_findings,
            "summary": {
                "total_secrets_found": len(manual_findings),
                "assistants_completed": sum(1 for r in self.results.values() if r["status"] == "completed"),
                "search_duration": str(datetime.now() - self.start_time)
            }
        }
        
        report_file = f"secret_hunt_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 تم حفظ التقرير في: {report_file}")
        print("=" * 80)
        
        return manual_findings

def main():
    """الدالة الرئيسية"""
    hunter = CollaborativeSecretHunter()
    findings = hunter.run_collaborative_search()
    
    if findings:
        print(f"\n🚨 تم العثور على {len(findings)} سر محتمل!")
        print("🔧 يجب إزالة هذه الأسرار قبل رفع المشروع إلى GitHub")
        return 1
    else:
        print("\n✅ لم يتم العثور على أسرار - المشروع آمن للرفع!")
        return 0

if __name__ == "__main__":
    sys.exit(main())
