#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔗 مدير تكامل جميع مشاريع أنوبيس
All Projects Integration Manager
"""

import os
import sys
import time
import json
import subprocess
import requests
from pathlib import Path

# ألوان للإخراج
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    NC = '\033[0m'

def print_header():
    print(f"{Colors.PURPLE}{'='*70}{Colors.NC}")
    print(f"{Colors.PURPLE}🔗 مدير تكامل جميع مشاريع أنوبيس{Colors.NC}")
    print(f"{Colors.PURPLE}All Projects Integration Manager{Colors.NC}")
    print(f"{Colors.PURPLE}{'='*70}{Colors.NC}")

def print_step(message):
    print(f"{Colors.BLUE}📋 {message}{Colors.NC}")

def print_success(message):
    print(f"{Colors.GREEN}✅ {message}{Colors.NC}")

def print_warning(message):
    print(f"{Colors.YELLOW}⚠️ {message}{Colors.NC}")

def print_error(message):
    print(f"{Colors.RED}❌ {message}{Colors.NC}")

class ProjectIntegrator:
    """مدير تكامل المشاريع"""
    
    def __init__(self):
        self.projects = {
            "ANUBIS_SYSTEM": {
                "name": "🏺 نظام أنوبيس الأساسي",
                "port": 8000,
                "health_endpoint": "/health",
                "startup_script": "main.py",
                "docker_image": "anubis-core"
            },
            "HORUS_AI_TEAM": {
                "name": "𓅃 فريق حورس",
                "port": 7000,
                "health_endpoint": "/health",
                "startup_script": "01_core/interfaces/horus_interface.py",
                "docker_image": "horus-team"
            },
            "ANUBIS_HORUS_MCP": {
                "name": "🔗 نظام MCP",
                "port": 3000,
                "health_endpoint": "/health",
                "startup_script": "src/index.js",
                "docker_image": "anubis-mcp"
            }
        }
        
        self.integration_status = {}
    
    def check_project_health(self, project_name):
        """فحص صحة مشروع معين"""
        project = self.projects.get(project_name)
        if not project:
            return False
        
        try:
            url = f"http://localhost:{project['port']}{project['health_endpoint']}"
            response = requests.get(url, timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def check_all_projects_health(self):
        """فحص صحة جميع المشاريع"""
        print_step("فحص صحة جميع المشاريع...")
        
        for project_name, project in self.projects.items():
            is_healthy = self.check_project_health(project_name)
            self.integration_status[project_name] = is_healthy
            
            if is_healthy:
                print_success(f"{project['name']}: متاح ✅")
            else:
                print_warning(f"{project['name']}: غير متاح ⚠️")
        
        return self.integration_status
    
    def test_inter_project_communication(self):
        """اختبار التواصل بين المشاريع"""
        print_step("اختبار التواصل بين المشاريع...")
        
        communication_tests = [
            {
                "from": "ANUBIS_SYSTEM",
                "to": "HORUS_AI_TEAM",
                "test": "API call from Anubis to Horus",
                "endpoint": "/team"
            },
            {
                "from": "HORUS_AI_TEAM", 
                "to": "ANUBIS_HORUS_MCP",
                "test": "MCP protocol communication",
                "endpoint": "/mcp/status"
            },
            {
                "from": "ANUBIS_HORUS_MCP",
                "to": "ANUBIS_SYSTEM",
                "test": "MCP to Anubis bridge",
                "endpoint": "/api/status"
            }
        ]
        
        results = []
        for test in communication_tests:
            try:
                from_project = self.projects[test["from"]]
                to_project = self.projects[test["to"]]
                
                # محاولة الاتصال
                url = f"http://localhost:{to_project['port']}{test['endpoint']}"
                response = requests.get(url, timeout=5)
                
                success = response.status_code == 200
                results.append({
                    "test": test["test"],
                    "success": success,
                    "from": from_project["name"],
                    "to": to_project["name"]
                })
                
                if success:
                    print_success(f"✅ {test['test']}")
                else:
                    print_warning(f"⚠️ {test['test']} - فشل الاتصال")
                    
            except Exception as e:
                print_error(f"❌ {test['test']} - خطأ: {e}")
                results.append({
                    "test": test["test"],
                    "success": False,
                    "error": str(e)
                })
        
        return results
    
    def create_integration_bridges(self):
        """إنشاء جسور التكامل بين المشاريع"""
        print_step("إنشاء جسور التكامل...")
        
        # جسر أنوبيس - حورس
        anubis_horus_bridge = """
import requests
import json

class AnubisHorusBridge:
    def __init__(self):
        self.anubis_url = "http://localhost:8000"
        self.horus_url = "http://localhost:7000"
    
    def send_task_to_horus(self, task_data):
        try:
            response = requests.post(f"{self.horus_url}/process_task", json=task_data)
            return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    def get_horus_team_status(self):
        try:
            response = requests.get(f"{self.horus_url}/team")
            return response.json()
        except Exception as e:
            return {"error": str(e)}
"""
        
        with open("ANUBIS_SYSTEM/anubis_horus_bridge.py", "w", encoding="utf-8") as f:
            f.write(anubis_horus_bridge)
        
        # جسر MCP
        mcp_bridge = """
import requests
import json

class MCPBridge:
    def __init__(self):
        self.mcp_url = "http://localhost:3000"
        self.anubis_url = "http://localhost:8000"
        self.horus_url = "http://localhost:7000"
    
    def route_message(self, message, target):
        if target == "anubis":
            return self.send_to_anubis(message)
        elif target == "horus":
            return self.send_to_horus(message)
        else:
            return {"error": "Unknown target"}
    
    def send_to_anubis(self, message):
        try:
            response = requests.post(f"{self.anubis_url}/api/process", json=message)
            return response.json()
        except Exception as e:
            return {"error": str(e)}
    
    def send_to_horus(self, message):
        try:
            response = requests.post(f"{self.horus_url}/process", json=message)
            return response.json()
        except Exception as e:
            return {"error": str(e)}
"""
        
        with open("ANUBIS_HORUS_MCP/mcp_bridge.py", "w", encoding="utf-8") as f:
            f.write(mcp_bridge)
        
        print_success("تم إنشاء جسور التكامل")
    
    def generate_integration_report(self):
        """إنشاء تقرير التكامل"""
        print_step("إنشاء تقرير التكامل...")
        
        report = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "projects_status": self.integration_status,
            "integration_health": {
                "total_projects": len(self.projects),
                "healthy_projects": sum(1 for status in self.integration_status.values() if status),
                "integration_score": (sum(1 for status in self.integration_status.values() if status) / len(self.projects)) * 100
            },
            "recommendations": []
        }
        
        # إضافة توصيات
        for project_name, is_healthy in self.integration_status.items():
            if not is_healthy:
                project = self.projects[project_name]
                report["recommendations"].append(f"إعادة تشغيل {project['name']}")
        
        # حفظ التقرير
        with open("integration_report.json", "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print_success("تم إنشاء تقرير التكامل: integration_report.json")
        return report
    
    def show_integration_dashboard(self):
        """عرض لوحة تحكم التكامل"""
        print(f"\n{Colors.CYAN}📊 لوحة تحكم التكامل:{Colors.NC}")
        print(f"{Colors.CYAN}{'='*50}{Colors.NC}")
        
        for project_name, project in self.projects.items():
            status = self.integration_status.get(project_name, False)
            status_icon = "✅" if status else "❌"
            status_text = "متاح" if status else "غير متاح"
            
            print(f"{status_icon} {project['name']}: {status_text}")
            print(f"   🌐 http://localhost:{project['port']}")
        
        # إحصائيات عامة
        total = len(self.projects)
        healthy = sum(1 for status in self.integration_status.values() if status)
        score = (healthy / total) * 100 if total > 0 else 0
        
        print(f"\n{Colors.CYAN}📈 الإحصائيات:{Colors.NC}")
        print(f"إجمالي المشاريع: {total}")
        print(f"المشاريع النشطة: {healthy}")
        print(f"نسبة التكامل: {score:.1f}%")

def main():
    """الدالة الرئيسية"""
    print_header()
    
    integrator = ProjectIntegrator()
    
    # فحص صحة المشاريع
    integrator.check_all_projects_health()
    
    # اختبار التواصل بين المشاريع
    communication_results = integrator.test_inter_project_communication()
    
    # إنشاء جسور التكامل
    integrator.create_integration_bridges()
    
    # إنشاء تقرير التكامل
    report = integrator.generate_integration_report()
    
    # عرض لوحة التحكم
    integrator.show_integration_dashboard()
    
    print(f"\n{Colors.GREEN}✅ تم إكمال عملية التكامل بنجاح!{Colors.NC}")
    print(f"{Colors.CYAN}📄 تقرير التكامل: integration_report.json{Colors.NC}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print_warning("\nتم إيقاف التشغيل بواسطة المستخدم")
    except Exception as e:
        print_error(f"خطأ غير متوقع: {e}")
