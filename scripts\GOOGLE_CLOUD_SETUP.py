#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
☁️ إعداد Google Cloud للمشروع
===============================

هذا السكريبت يقوم بإعداد المشروع للنشر على Google Cloud Platform

📅 تاريخ الإنشاء: 2025-07-29
🔧 الإصدار: 1.0.0
"""

import os
import sys
import subprocess
import json
import yaml
from datetime import datetime
from pathlib import Path

class GoogleCloudSetup:
    def __init__(self):
        self.project_id = "universal-ai-assistants"
        self.region = "us-central1"
        self.service_name = "universal-ai-assistants"
        
    def print_header(self):
        """طباعة رأس السكريبت"""
        print("=" * 80)
        print("☁️ إعداد Google Cloud للمشروع")
        print("=" * 80)
        print(f"📅 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🆔 معرف المشروع: {self.project_id}")
        print(f"🌍 المنطقة: {self.region}")
        print("=" * 80)
    
    def check_gcloud_cli(self):
        """فحص Google Cloud CLI"""
        print("\n🔍 فحص Google Cloud CLI...")
        
        try:
            result = subprocess.run(["gcloud", "version"], capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ Google Cloud CLI متوفر")
                return True
            else:
                print("❌ Google Cloud CLI غير متوفر")
                print("📥 يرجى تثبيته من: https://cloud.google.com/sdk/docs/install")
                return False
        except Exception as e:
            print(f"❌ خطأ في فحص gcloud: {str(e)}")
            return False
    
    def create_app_yaml(self):
        """إنشاء ملف app.yaml لـ App Engine"""
        print("\n📝 إنشاء ملف app.yaml...")
        
        app_yaml_content = """runtime: python39

env_variables:
  PYTHONPATH: /srv
  
automatic_scaling:
  min_instances: 1
  max_instances: 10
  target_cpu_utilization: 0.6

resources:
  cpu: 1
  memory_gb: 2
  disk_size_gb: 10

handlers:
- url: /static
  static_dir: static
  
- url: /.*
  script: auto
  
skip_files:
- ^(.*/)?#.*#$
- ^(.*/)?.*~$
- ^(.*/)?.*\.py[co]$
- ^(.*/)?.*/RCS/.*$
- ^(.*/)?\..*$
- ^(.*/)?tests/.*$
- ^(.*/)?test_.*$
- node_modules/
- __pycache__/
"""
        
        try:
            with open('app.yaml', 'w', encoding='utf-8') as f:
                f.write(app_yaml_content)
            print("✅ تم إنشاء ملف app.yaml")
            return True
        except Exception as e:
            print(f"❌ خطأ في إنشاء app.yaml: {str(e)}")
            return False
    
    def create_cloudbuild_yaml(self):
        """إنشاء ملف cloudbuild.yaml"""
        print("\n📝 إنشاء ملف cloudbuild.yaml...")
        
        cloudbuild_content = {
            "steps": [
                {
                    "name": "gcr.io/cloud-builders/docker",
                    "args": [
                        "build",
                        "-t", f"gcr.io/$PROJECT_ID/{self.service_name}:$COMMIT_SHA",
                        "."
                    ]
                },
                {
                    "name": "gcr.io/cloud-builders/docker",
                    "args": [
                        "push",
                        f"gcr.io/$PROJECT_ID/{self.service_name}:$COMMIT_SHA"
                    ]
                },
                {
                    "name": "gcr.io/google.com/cloudsdktool/cloud-sdk",
                    "entrypoint": "gcloud",
                    "args": [
                        "run", "deploy", self.service_name,
                        "--image", f"gcr.io/$PROJECT_ID/{self.service_name}:$COMMIT_SHA",
                        "--region", self.region,
                        "--platform", "managed",
                        "--allow-unauthenticated"
                    ]
                }
            ],
            "images": [f"gcr.io/$PROJECT_ID/{self.service_name}:$COMMIT_SHA"]
        }
        
        try:
            with open('cloudbuild.yaml', 'w', encoding='utf-8') as f:
                yaml.dump(cloudbuild_content, f, default_flow_style=False)
            print("✅ تم إنشاء ملف cloudbuild.yaml")
            return True
        except Exception as e:
            print(f"❌ خطأ في إنشاء cloudbuild.yaml: {str(e)}")
            return False
    
    def create_dockerfile_production(self):
        """إنشاء Dockerfile محسن للإنتاج"""
        print("\n🐳 إنشاء Dockerfile للإنتاج...")
        
        dockerfile_content = """# Multi-stage build للتحسين
FROM python:3.11-slim as builder

# تثبيت المتطلبات الأساسية
RUN apt-get update && apt-get install -y \\
    gcc \\
    g++ \\
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# نسخ ملفات المتطلبات
COPY requirements.txt ANUBIS_SYSTEM/requirements.txt ./
COPY SHARED_REQUIREMENTS/data/requirements_*.txt ./

# تثبيت المكتبات
RUN pip install --no-cache-dir --upgrade pip
RUN pip install --no-cache-dir -r requirements.txt

# المرحلة الثانية - الإنتاج
FROM python:3.11-slim

# إنشاء مستخدم غير root للأمان
RUN useradd --create-home --shell /bin/bash anubis

WORKDIR /app

# نسخ المكتبات المثبتة
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# نسخ ملفات المشروع
COPY --chown=anubis:anubis . .

# إعداد متغيرات البيئة
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV PORT=8080

# التبديل للمستخدم غير root
USER anubis

# فتح المنفذ
EXPOSE 8080

# نقطة الدخول
CMD ["python", "ANUBIS_SYSTEM/main.py"]
"""
        
        try:
            with open('Dockerfile.production', 'w', encoding='utf-8') as f:
                f.write(dockerfile_content)
            print("✅ تم إنشاء Dockerfile.production")
            return True
        except Exception as e:
            print(f"❌ خطأ في إنشاء Dockerfile: {str(e)}")
            return False
    
    def create_requirements_production(self):
        """إنشاء ملف متطلبات الإنتاج"""
        print("\n📦 إنشاء ملف متطلبات الإنتاج...")
        
        # قراءة المتطلبات الموجودة
        requirements = set()
        
        req_files = [
            "ANUBIS_SYSTEM/requirements.txt",
            "SHARED_REQUIREMENTS/data/requirements_anubis_horus_unified.txt"
        ]
        
        for req_file in req_files:
            if os.path.exists(req_file):
                try:
                    with open(req_file, 'r', encoding='utf-8') as f:
                        for line in f:
                            line = line.strip()
                            if line and not line.startswith('#'):
                                requirements.add(line)
                except Exception as e:
                    print(f"⚠️ تحذير: لا يمكن قراءة {req_file}: {str(e)}")
        
        # إضافة متطلبات الإنتاج الأساسية
        production_requirements = [
            "fastapi>=0.104.0",
            "uvicorn[standard]>=0.24.0",
            "gunicorn>=21.2.0",
            "python-multipart>=0.0.6",
            "python-dotenv>=1.0.0",
            "pydantic>=2.5.0",
            "sqlalchemy>=2.0.0",
            "alembic>=1.13.0",
            "redis>=5.0.0",
            "celery>=5.3.0",
            "psutil>=5.9.0"
        ]
        
        requirements.update(production_requirements)
        
        try:
            with open('requirements-production.txt', 'w', encoding='utf-8') as f:
                for req in sorted(requirements):
                    f.write(f"{req}\n")
            print("✅ تم إنشاء ملف requirements-production.txt")
            return True
        except Exception as e:
            print(f"❌ خطأ في إنشاء متطلبات الإنتاج: {str(e)}")
            return False
    
    def create_deployment_script(self):
        """إنشاء سكريبت النشر"""
        print("\n🚀 إنشاء سكريبت النشر...")
        
        deploy_script = f"""#!/bin/bash
# سكريبت نشر المشروع على Google Cloud

set -e

echo "🚀 بدء نشر Universal AI Assistants على Google Cloud..."

# تعيين معرف المشروع
export PROJECT_ID="{self.project_id}"
export REGION="{self.region}"
export SERVICE_NAME="{self.service_name}"

echo "📋 إعداد المشروع..."
gcloud config set project $PROJECT_ID

echo "🔧 تفعيل الخدمات المطلوبة..."
gcloud services enable run.googleapis.com
gcloud services enable cloudbuild.googleapis.com
gcloud services enable containerregistry.googleapis.com
gcloud services enable sql-component.googleapis.com
gcloud services enable secretmanager.googleapis.com

echo "🐳 بناء ورفع الصورة..."
gcloud builds submit --tag gcr.io/$PROJECT_ID/$SERVICE_NAME

echo "🚀 نشر على Cloud Run..."
gcloud run deploy $SERVICE_NAME \\
    --image gcr.io/$PROJECT_ID/$SERVICE_NAME \\
    --platform managed \\
    --region $REGION \\
    --allow-unauthenticated \\
    --memory 2Gi \\
    --cpu 1 \\
    --max-instances 10 \\
    --set-env-vars="ENVIRONMENT=production"

echo "✅ تم النشر بنجاح!"
echo "🔗 رابط الخدمة:"
gcloud run services describe $SERVICE_NAME --region=$REGION --format="value(status.url)"
"""
        
        try:
            with open('deploy.sh', 'w', encoding='utf-8') as f:
                f.write(deploy_script)
            
            # جعل الملف قابل للتنفيذ
            os.chmod('deploy.sh', 0o755)
            print("✅ تم إنشاء سكريبت deploy.sh")
            return True
        except Exception as e:
            print(f"❌ خطأ في إنشاء سكريبت النشر: {str(e)}")
            return False
    
    def create_environment_config(self):
        """إنشاء ملف إعداد البيئة"""
        print("\n⚙️ إنشاء ملف إعداد البيئة...")
        
        env_template = """# متغيرات البيئة للإنتاج
# انسخ هذا الملف إلى .env وأضف القيم الفعلية

# إعدادات التطبيق
ENVIRONMENT=production
DEBUG=false
PORT=8080

# قاعدة البيانات
DATABASE_URL=************************************/database

# Redis
REDIS_URL=redis://localhost:6379/0

# مفاتيح API (استخدم Google Secret Manager في الإنتاج)
OPENAI_API_KEY=your_openai_key_here
ANTHROPIC_API_KEY=your_anthropic_key_here
GOOGLE_API_KEY=your_google_key_here

# إعدادات الأمان
SECRET_KEY=your_secret_key_here
JWT_SECRET=your_jwt_secret_here

# إعدادات Google Cloud
GOOGLE_CLOUD_PROJECT={self.project_id}
GOOGLE_CLOUD_REGION={self.region}

# إعدادات التسجيل
LOG_LEVEL=INFO
LOG_FORMAT=json
"""
        
        try:
            with open('.env.template', 'w', encoding='utf-8') as f:
                f.write(env_template)
            print("✅ تم إنشاء ملف .env.template")
            return True
        except Exception as e:
            print(f"❌ خطأ في إنشاء ملف البيئة: {str(e)}")
            return False
    
    def setup_all(self):
        """تنفيذ جميع خطوات الإعداد"""
        self.print_header()
        
        steps = [
            ("فحص Google Cloud CLI", self.check_gcloud_cli),
            ("إنشاء app.yaml", self.create_app_yaml),
            ("إنشاء cloudbuild.yaml", self.create_cloudbuild_yaml),
            ("إنشاء Dockerfile للإنتاج", self.create_dockerfile_production),
            ("إنشاء متطلبات الإنتاج", self.create_requirements_production),
            ("إنشاء سكريبت النشر", self.create_deployment_script),
            ("إنشاء إعداد البيئة", self.create_environment_config)
        ]
        
        success_count = 0
        for step_name, step_func in steps:
            if step_func():
                success_count += 1
            else:
                print(f"⚠️ تحذير: فشل في خطوة {step_name}")
        
        print("\n" + "=" * 80)
        print("📊 ملخص الإعداد")
        print("=" * 80)
        print(f"✅ خطوات ناجحة: {success_count}/{len(steps)}")
        
        if success_count == len(steps):
            print("🎉 تم إعداد المشروع بنجاح لـ Google Cloud!")
            print("\n📋 الخطوات التالية:")
            print("1. قم بإنشاء مشروع Google Cloud")
            print("2. فعل الفوترة للمشروع")
            print("3. نسخ .env.template إلى .env وإضافة القيم الفعلية")
            print("4. تشغيل: ./deploy.sh")
        else:
            print("⚠️ بعض الخطوات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        
        print("=" * 80)
        
        return success_count == len(steps)

def main():
    """الدالة الرئيسية"""
    setup = GoogleCloudSetup()
    success = setup.setup_all()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
