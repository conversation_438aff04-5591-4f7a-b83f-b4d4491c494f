#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 نشر مشروع Universal AI Assistants من GitHub إلى Google Cloud
Deploy Universal AI Assistants from GitHub to Google Cloud
"""

import os
import subprocess
import json
import time
from datetime import datetime

class GitHubToGoogleCloudDeployer:
    """مُنشر المشروع من GitHub إلى Google Cloud"""
    
    def __init__(self):
        self.project_id = "universal-ai-assistants-2025"
        self.service_name = "universal-ai-assistants"
        self.region = "us-central1"
        self.github_repo = "https://github.com/amrashour1/universal-ai-assistants-agent"
        self.api_keys = {
            'gemini': 'AIzaSyBc4zdWiwluNW-2_aScTNY3ZA9k4k15g9k',
            'qwen': '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'
        }
        
    def run_command(self, command, check=True):
        """تشغيل أمر shell"""
        print(f"🔄 تشغيل: {command}")
        try:
            result = subprocess.run(
                command, 
                shell=True, 
                capture_output=True, 
                text=True, 
                check=check
            )
            if result.stdout:
                print(f"✅ النتيجة: {result.stdout.strip()}")
            return result
        except subprocess.CalledProcessError as e:
            print(f"❌ خطأ: {e}")
            if e.stderr:
                print(f"❌ تفاصيل الخطأ: {e.stderr}")
            return None
            
    def check_gcloud_auth(self):
        """التحقق من تسجيل الدخول في gcloud"""
        print("🔐 التحقق من تسجيل الدخول في Google Cloud...")
        
        result = self.run_command("gcloud auth list --format='value(account)'", check=False)
        if result and result.stdout.strip():
            print(f"✅ مسجل الدخول كـ: {result.stdout.strip()}")
            return True
        else:
            print("❌ غير مسجل الدخول في Google Cloud")
            print("🔑 يرجى تسجيل الدخول باستخدام: gcloud auth login")
            return False
            
    def setup_project(self):
        """إعداد مشروع Google Cloud"""
        print(f"🏗️ إعداد مشروع Google Cloud: {self.project_id}")
        
        # إنشاء المشروع (إذا لم يكن موجوداً)
        print("📋 إنشاء المشروع...")
        self.run_command(f"gcloud projects create {self.project_id} --name='Universal AI Assistants'", check=False)
        
        # تعيين المشروع النشط
        print("🎯 تعيين المشروع النشط...")
        self.run_command(f"gcloud config set project {self.project_id}")
        
        # تفعيل APIs المطلوبة
        print("🔌 تفعيل APIs المطلوبة...")
        apis = [
            "cloudbuild.googleapis.com",
            "run.googleapis.com",
            "containerregistry.googleapis.com",
            "secretmanager.googleapis.com"
        ]
        
        for api in apis:
            print(f"   🔌 تفعيل {api}...")
            self.run_command(f"gcloud services enable {api}")
            
    def setup_secrets(self):
        """إعداد المفاتيح في Secret Manager"""
        print("🔐 إعداد المفاتيح في Secret Manager...")
        
        # إنشاء secret لمفتاح Gemini
        print("   🤖 إنشاء secret لمفتاح Gemini...")
        self.run_command(f"echo '{self.api_keys['gemini']}' | gcloud secrets create gemini-api-key --data-file=-", check=False)
        
        # إنشاء secret لمفتاح Qwen
        print("   🧠 إنشاء secret لمفتاح Qwen...")
        self.run_command(f"echo '{self.api_keys['qwen']}' | gcloud secrets create qwen-api-key --data-file=-", check=False)
        
        print("✅ تم إعداد المفاتيح بنجاح")
        
    def deploy_to_cloud_run(self):
        """نشر التطبيق على Cloud Run"""
        print("🚀 نشر التطبيق على Cloud Run من GitHub...")
        
        deploy_command = f"""
        gcloud run deploy {self.service_name} \
          --source {self.github_repo} \
          --platform managed \
          --region {self.region} \
          --allow-unauthenticated \
          --set-env-vars GEMINI_API_KEY="{self.api_keys['gemini']}" \
          --set-env-vars QWEN_API_KEY="{self.api_keys['qwen']}" \
          --memory 2Gi \
          --cpu 2 \
          --max-instances 10 \
          --timeout 3600
        """.strip().replace('\n        ', ' ')
        
        result = self.run_command(deploy_command)
        
        if result and result.returncode == 0:
            print("✅ تم نشر التطبيق بنجاح!")
            return True
        else:
            print("❌ فشل في نشر التطبيق")
            return False
            
    def get_service_url(self):
        """الحصول على URL الخدمة"""
        print("🌐 الحصول على URL الخدمة...")
        
        result = self.run_command(f"""
        gcloud run services describe {self.service_name} \
          --region {self.region} \
          --format="value(status.url)"
        """.strip())
        
        if result and result.stdout.strip():
            url = result.stdout.strip()
            print(f"🌐 URL التطبيق: {url}")
            return url
        else:
            print("❌ لم يتم العثور على URL الخدمة")
            return None
            
    def setup_monitoring(self):
        """إعداد المراقبة"""
        print("📊 إعداد المراقبة...")
        
        # إنشاء تنبيه للأخطاء
        alert_policy = {
            "displayName": "Universal AI Assistants - High Error Rate",
            "conditions": [{
                "displayName": "Error rate condition",
                "conditionThreshold": {
                    "filter": f'resource.type="cloud_run_revision" AND resource.labels.service_name="{self.service_name}"',
                    "comparison": "COMPARISON_GREATER_THAN",
                    "thresholdValue": 0.1
                }
            }]
        }
        
        print("   📈 إنشاء تنبيهات المراقبة...")
        # يمكن إضافة المزيد من إعدادات المراقبة هنا
        
    def test_deployment(self, url):
        """اختبار النشر"""
        print("🧪 اختبار النشر...")
        
        if not url:
            print("❌ لا يوجد URL للاختبار")
            return False
            
        try:
            import requests
            response = requests.get(url, timeout=30)
            if response.status_code == 200:
                print("✅ التطبيق يعمل بنجاح!")
                return True
            else:
                print(f"⚠️ التطبيق يرد بكود: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
            return False
            
    def create_deployment_report(self, url, success):
        """إنشاء تقرير النشر"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        report = {
            "deployment_info": {
                "timestamp": timestamp,
                "project_id": self.project_id,
                "service_name": self.service_name,
                "region": self.region,
                "github_repo": self.github_repo,
                "success": success
            },
            "service_details": {
                "url": url,
                "memory": "2Gi",
                "cpu": "2",
                "max_instances": 10
            },
            "api_keys": {
                "gemini_configured": bool(self.api_keys['gemini']),
                "qwen_configured": bool(self.api_keys['qwen'])
            }
        }
        
        report_file = f"deployment_report_{timestamp}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
            
        print(f"📄 تم حفظ تقرير النشر: {report_file}")
        return report_file
        
    def deploy(self):
        """تنفيذ عملية النشر الكاملة"""
        print("🏺 بدء نشر Universal AI Assistants من GitHub إلى Google Cloud")
        print("=" * 70)
        
        start_time = time.time()
        
        # التحقق من تسجيل الدخول
        if not self.check_gcloud_auth():
            print("❌ يرجى تسجيل الدخول في Google Cloud أولاً")
            return False
            
        try:
            # إعداد المشروع
            self.setup_project()
            
            # إعداد المفاتيح
            self.setup_secrets()
            
            # النشر على Cloud Run
            deployment_success = self.deploy_to_cloud_run()
            
            if deployment_success:
                # الحصول على URL
                url = self.get_service_url()
                
                # إعداد المراقبة
                self.setup_monitoring()
                
                # اختبار النشر
                test_success = self.test_deployment(url)
                
                # إنشاء تقرير
                report_file = self.create_deployment_report(url, test_success)
                
                end_time = time.time()
                duration = end_time - start_time
                
                print("\n" + "=" * 70)
                print("🎉 تم إكمال عملية النشر!")
                print(f"⏱️ المدة: {duration:.2f} ثانية")
                print(f"🌐 URL التطبيق: {url}")
                print(f"📄 تقرير النشر: {report_file}")
                print("=" * 70)
                
                return True
            else:
                print("❌ فشل في النشر")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في عملية النشر: {e}")
            return False
            
    def show_management_commands(self):
        """عرض أوامر الإدارة"""
        print("\n📋 أوامر إدارة التطبيق:")
        print("-" * 50)
        
        commands = [
            ("عرض حالة الخدمة", f"gcloud run services describe {self.service_name} --region {self.region}"),
            ("عرض السجلات", f"gcloud logging read 'resource.type=cloud_run_revision AND resource.labels.service_name={self.service_name}' --limit 50"),
            ("تحديث متغيرات البيئة", f"gcloud run services update {self.service_name} --region {self.region} --set-env-vars KEY=VALUE"),
            ("حذف الخدمة", f"gcloud run services delete {self.service_name} --region {self.region}"),
            ("عرض URL الخدمة", f"gcloud run services describe {self.service_name} --region {self.region} --format='value(status.url)'")
        ]
        
        for desc, cmd in commands:
            print(f"• {desc}:")
            print(f"  {cmd}")
            print()

def main():
    """الدالة الرئيسية"""
    deployer = GitHubToGoogleCloudDeployer()
    
    print("🏺 مُنشر Universal AI Assistants إلى Google Cloud")
    print("المستودع: **************:amrashour1/universal-ai-assistants-agent.git")
    print("=" * 70)
    
    # تنفيذ النشر
    success = deployer.deploy()
    
    if success:
        # عرض أوامر الإدارة
        deployer.show_management_commands()
        
        print("\n🎯 الخطوات التالية:")
        print("1. اختبار التطبيق المنشور")
        print("2. إعداد domain مخصص (اختياري)")
        print("3. إعداد CI/CD للنشر التلقائي")
        print("4. مراقبة الأداء والتكاليف")
    else:
        print("\n❌ فشل في النشر. يرجى مراجعة الأخطاء أعلاه.")
        
    return success

if __name__ == "__main__":
    main()
