#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 إصلاح مشكلة numpy و pandas
=============================
"""

import subprocess
import sys
import os

def fix_numpy_issue():
    """إصلاح مشكلة numpy"""
    print("🔧 إصلاح مشكلة numpy و pandas...")
    
    try:
        # إلغاء تثبيت المكتبات المتعارضة
        print("1️⃣ إلغاء تثبيت numpy و pandas...")
        subprocess.run([
            sys.executable, '-m', 'pip', 'uninstall', 
            'numpy', 'pandas', '-y'
        ], check=True)
        
        # تنظيف cache
        print("2️⃣ تنظيف pip cache...")
        subprocess.run([
            sys.executable, '-m', 'pip', 'cache', 'purge'
        ], check=True)
        
        # إعادة تثبيت بإصدارات مستقرة
        print("3️⃣ إعادة تثبيت numpy...")
        subprocess.run([
            sys.executable, '-m', 'pip', 'install', 
            'numpy==1.24.3'
        ], check=True)
        
        print("4️⃣ إعادة تثبيت pandas...")
        subprocess.run([
            sys.executable, '-m', 'pip', 'install', 
            'pandas==2.0.3'
        ], check=True)
        
        # تثبيت plotly
        print("5️⃣ تثبيت plotly...")
        subprocess.run([
            sys.executable, '-m', 'pip', 'install', 
            'plotly==5.15.0'
        ], check=True)
        
        print("✅ تم إصلاح المشكلة بنجاح!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في الإصلاح: {e}")
        return False

def test_imports():
    """اختبار استيراد المكتبات"""
    print("🧪 اختبار استيراد المكتبات...")
    
    try:
        import numpy as np
        print("✅ numpy يعمل بنجاح")
        
        import pandas as pd
        print("✅ pandas يعمل بنجاح")
        
        import plotly.graph_objects as go
        print("✅ plotly يعمل بنجاح")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 أداة إصلاح مشكلة numpy")
    print("=" * 40)
    
    # إصلاح المشكلة
    if fix_numpy_issue():
        # اختبار الإصلاح
        if test_imports():
            print("\n🎉 تم الإصلاح بنجاح!")
            print("يمكنك الآن تشغيل الداشبورد:")
            print("streamlit run ANUBIS_CLOUD_DASHBOARD.py --server.port 8502")
        else:
            print("\n❌ فشل في اختبار الإصلاح")
    else:
        print("\n❌ فشل في الإصلاح")

if __name__ == "__main__":
    main()
