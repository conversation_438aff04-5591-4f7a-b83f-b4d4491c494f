#!/usr/bin/env python3
"""
🚀 اختبار سريع لأدوات المشروع
"""

import os
import json
from pathlib import Path
from datetime import datetime

def test_project_tools():
    """اختبار سريع للأدوات"""
    print("🧪 اختبار سريع لأدوات Universal AI Assistants")
    print("=" * 50)
    
    project_root = Path.cwd()
    results = {
        "timestamp": datetime.now().isoformat(),
        "tests": {},
        "summary": {"total": 0, "passed": 0, "failed": 0}
    }
    
    # قائمة الأدوات المهمة للاختبار
    tools_to_test = {
        "HORUS Team": {
            "path": "HORUS_AI_TEAM",
            "key_files": ["summon_horus_assistant.py", "README.md"]
        },
        "ANUBIS System": {
            "path": "ANUBIS_SYSTEM", 
            "key_files": ["main.py", "quick_start_anubis.py"]
        },
        "MCP System": {
            "path": "ANUBIS_HORUS_MCP",
            "key_files": ["package.json", "collaborative_ai_system.py"]
        },
        "Scripts": {
            "path": "scripts",
            "key_files": ["QUICK_START.py", "INTEGRATE_ALL_PROJECTS.py"]
        },
        "Documentation": {
            "path": "PROJECT_DOCUMENTATION",
            "key_files": ["README.md"]
        }
    }
    
    for tool_name, tool_info in tools_to_test.items():
        print(f"\n🔍 اختبار {tool_name}...")
        
        tool_path = project_root / tool_info["path"]
        tool_results = {"status": "UNKNOWN", "details": []}
        
        # فحص وجود المجلد
        if tool_path.exists() and tool_path.is_dir():
            tool_results["status"] = "PASSED"
            file_count = len(list(tool_path.rglob("*")))
            tool_results["details"].append(f"المجلد موجود مع {file_count} ملف")
            print(f"  ✅ المجلد موجود ({file_count} ملف)")
            
            # فحص الملفات المهمة
            for key_file in tool_info["key_files"]:
                file_path = tool_path / key_file
                if file_path.exists():
                    print(f"  ✅ {key_file} موجود")
                    tool_results["details"].append(f"{key_file}: موجود")
                else:
                    print(f"  ⚠️ {key_file} مفقود")
                    tool_results["details"].append(f"{key_file}: مفقود")
            
            results["summary"]["passed"] += 1
        else:
            tool_results["status"] = "FAILED"
            tool_results["details"].append("المجلد غير موجود")
            print(f"  ❌ المجلد غير موجود")
            results["summary"]["failed"] += 1
        
        results["tests"][tool_name] = tool_results
        results["summary"]["total"] += 1
    
    # اختبار ملفات إضافية مهمة
    print(f"\n🔍 اختبار الملفات الأساسية...")
    important_files = [
        "README.md",
        "LICENSE", 
        "DEVELOPMENT_RULES.md",
        "PROJECT_STRUCTURE_DETAILED.md",
        "PROJECT_PATHS_DIRECTORY.md"
    ]
    
    for file_name in important_files:
        file_path = project_root / file_name
        if file_path.exists():
            print(f"  ✅ {file_name} موجود")
            results["summary"]["passed"] += 1
        else:
            print(f"  ❌ {file_name} مفقود")
            results["summary"]["failed"] += 1
        results["summary"]["total"] += 1
    
    # حساب النتائج النهائية
    success_rate = (results["summary"]["passed"] / results["summary"]["total"]) * 100
    
    print("\n" + "=" * 50)
    print("📊 النتائج النهائية:")
    print("=" * 50)
    print(f"🧪 إجمالي الاختبارات: {results['summary']['total']}")
    print(f"✅ اختبارات ناجحة: {results['summary']['passed']}")
    print(f"❌ اختبارات فاشلة: {results['summary']['failed']}")
    print(f"📈 معدل النجاح: {success_rate:.1f}%")
    
    # تقييم الحالة
    if success_rate >= 90:
        print("🏆 التقييم: ممتاز - جميع الأدوات تعمل")
        status = "EXCELLENT"
    elif success_rate >= 75:
        print("👍 التقييم: جيد - معظم الأدوات تعمل")
        status = "GOOD"
    elif success_rate >= 50:
        print("⚠️ التقييم: متوسط - بعض الأدوات تحتاج إصلاح")
        status = "AVERAGE"
    else:
        print("❌ التقييم: ضعيف - الأدوات تحتاج عمل كبير")
        status = "POOR"
    
    results["overall_status"] = status
    results["success_rate"] = success_rate
    
    # حفظ التقرير
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"quick_tools_test_report_{timestamp}.json"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 تم حفظ التقرير في: {report_file}")
    
    return results

if __name__ == "__main__":
    test_project_tools()
