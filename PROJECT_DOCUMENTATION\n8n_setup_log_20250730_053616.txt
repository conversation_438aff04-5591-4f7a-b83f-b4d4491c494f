[05:36:10] INFO: 🚀 بدء إعداد n8n للأتمتة
[05:36:10] INFO: ============================================================
[05:36:10] INFO: 🔍 فحص متطلبات n8n...
[05:36:12] INFO: ✅ Google Cloud CLI متوفر
[05:36:12] INFO: ✅ Docker متوفر
[05:36:12] INFO: 📝 إنشاء Dockerfile لـ n8n...
[05:36:12] INFO: ✅ تم إنشاء Dockerfile.n8n
[05:36:12] INFO: ⚙️ إنشاء ملفات تكوين n8n...
[05:36:12] INFO: ✅ تم إنشاء ملفات التكوين
[05:36:12] INFO: 🔄 إنشاء workflows أساسية...
[05:36:12] INFO: ✅ تم إنشاء workflows أساسية
[05:36:12] INFO: 🐳 نشر n8n محلياً...
[05:36:12] ERROR: ❌ فشل تشغيل n8n: 'docker-compose' is not recognized as an internal or external command,
operable program or batch file.

[05:36:12] INFO: ☁️ نشر n8n في Google Cloud...
[05:36:16] ERROR: ❌ فشل نشر n8n في Cloud: ERROR: (gcloud.app.deploy) An error occurred while parsing file: [C:\Users\<USER>\Universal-AI-Assistants\app.n8n.yaml]
Unexpected attribute 'cool_down_period' for object of type AutomaticScaling.
  in "C:\Users\<USER>\Universal-AI-Assistants\app.n8n.yaml", line 9, column 21

[05:36:16] INFO: 
============================================================
[05:36:16] INFO: 📊 ملخص إعداد n8n
[05:36:16] INFO: ============================================================
[05:36:16] INFO: ⏱️ المدة: 0.1 دقيقة
[05:36:16] INFO: 🏠 النشر المحلي: ❌ فشل
[05:36:16] INFO: ☁️ النشر السحابي: ⚠️ تخطي