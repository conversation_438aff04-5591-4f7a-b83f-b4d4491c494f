#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 نشر Universal AI Assistants على Google Cloud
==============================================
"""

import os
import subprocess
import json
from datetime import datetime

class GoogleCloudDeployer:
    def __init__(self):
        self.project_id = "anubis-467210"
        self.project_number = "726154816710"
        self.region = "us-central1"
        self.zone = "us-central1-a"
        self.github_repo = "https://github.com/amrashour1/universal-ai-assistants-agent.git"
        
    def main(self):
        print("🚀 نشر Universal AI Assistants على Google Cloud")
        print("=" * 70)
        print(f"📅 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🆔 Project ID: {self.project_id}")
        print(f"🔢 Project Number: {self.project_number}")
        print(f"🌍 Region: {self.region}")
        print("=" * 70)
        
        try:
            # التحقق من المتطلبات
            self.check_prerequisites()
            
            # عرض خيارات النشر
            deployment_option = self.show_deployment_options()
            
            if deployment_option == "1":
                self.deploy_full_ollama()
            elif deployment_option == "2":
                self.deploy_hybrid_approach()
            elif deployment_option == "3":
                self.deploy_cloud_only()
            else:
                print("❌ خيار غير صحيح!")
                return
                
        except Exception as e:
            print(f"❌ خطأ في النشر: {str(e)}")
    
    def check_prerequisites(self):
        """التحقق من المتطلبات الأساسية"""
        print("\n🔍 التحقق من المتطلبات...")
        
        # التحقق من gcloud
        try:
            result = subprocess.run(['gcloud', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ Google Cloud SDK متوفر")
            else:
                raise Exception("Google Cloud SDK غير متوفر")
        except:
            raise Exception("Google Cloud SDK غير مثبت")
        
        # التحقق من المصادقة
        try:
            result = subprocess.run(['gcloud', 'auth', 'list', '--filter=status:ACTIVE'], 
                                  capture_output=True, text=True)
            if "<EMAIL>" in result.stdout:
                print("✅ المصادقة متوفرة")
            else:
                print("⚠️ يجب تسجيل الدخول أولاً")
                subprocess.run(['gcloud', 'auth', 'login'])
        except:
            raise Exception("مشكلة في المصادقة")
        
        # التحقق من المشروع
        try:
            subprocess.run(['gcloud', 'config', 'set', 'project', self.project_id], check=True)
            print(f"✅ تم تعيين المشروع: {self.project_id}")
        except:
            raise Exception(f"لا يمكن الوصول للمشروع {self.project_id}")
    
    def show_deployment_options(self):
        """عرض خيارات النشر"""
        print("\n🎯 اختر استراتيجية النشر:")
        print("=" * 50)
        print("1. 🤖 Ollama محلي كامل على VM")
        print("   - جميع النماذج على VM واحد")
        print("   - تكلفة: ~$227/شهر")
        print("   - أداء: ممتاز للنماذج المحلية")
        print()
        print("2. ⚡ نهج مختلط (موصى به)")
        print("   - نماذج أساسية على Ollama")
        print("   - نماذج متقدمة على Vertex AI")
        print("   - تكلفة: ~$200-350/شهر")
        print("   - أداء: متوازن وفعال")
        print()
        print("3. ☁️ Cloud فقط")
        print("   - جميع النماذج عبر Google Cloud APIs")
        print("   - تكلفة: ~$100-200/شهر")
        print("   - أداء: يعتمد على الشبكة")
        print()
        
        while True:
            choice = input("🎯 اختر رقم (1-3): ").strip()
            if choice in ["1", "2", "3"]:
                return choice
            print("❌ يرجى اختيار رقم صحيح (1-3)")
    
    def deploy_full_ollama(self):
        """نشر Ollama كامل على VM"""
        print("\n🤖 نشر Ollama كامل على VM...")
        
        # إنشاء VM للـ Ollama
        self.create_ollama_vm()
        
        # إعداد قاعدة البيانات
        self.setup_cloud_sql()
        
        # نشر التطبيق الرئيسي
        self.deploy_main_app()
        
        # تكوين الشبكة
        self.configure_networking()
        
        print("✅ تم نشر Ollama كامل بنجاح!")
    
    def deploy_hybrid_approach(self):
        """نشر النهج المختلط"""
        print("\n⚡ نشر النهج المختلط...")
        
        # إنشاء VM أصغر للنماذج الأساسية
        self.create_small_ollama_vm()
        
        # تفعيل Vertex AI APIs
        self.enable_vertex_ai()
        
        # إعداد قاعدة البيانات
        self.setup_cloud_sql()
        
        # نشر التطبيق مع تكوين مختلط
        self.deploy_hybrid_app()
        
        print("✅ تم نشر النهج المختلط بنجاح!")
    
    def deploy_cloud_only(self):
        """نشر Cloud فقط"""
        print("\n☁️ نشر Cloud فقط...")
        
        # تفعيل جميع APIs المطلوبة
        self.enable_all_apis()
        
        # إعداد قاعدة البيانات
        self.setup_cloud_sql()
        
        # نشر التطبيق مع Cloud APIs فقط
        self.deploy_cloud_app()
        
        print("✅ تم نشر Cloud فقط بنجاح!")
    
    def create_ollama_vm(self):
        """إنشاء VM للـ Ollama"""
        print("🖥️ إنشاء VM للـ Ollama...")
        
        vm_name = "ollama-vm"
        machine_type = "e2-standard-8"
        
        cmd = [
            'gcloud', 'compute', 'instances', 'create', vm_name,
            '--zone', self.zone,
            '--machine-type', machine_type,
            '--boot-disk-size', '100GB',
            '--boot-disk-type', 'pd-ssd',
            '--image-family', 'ubuntu-2204-lts',
            '--image-project', 'ubuntu-os-cloud',
            '--tags', 'ollama-server',
            '--metadata', 'startup-script=#!/bin/bash\n' +
                         'apt-get update\n' +
                         'apt-get install -y docker.io\n' +
                         'systemctl start docker\n' +
                         'systemctl enable docker\n' +
                         'curl -fsSL https://ollama.ai/install.sh | sh\n' +
                         'systemctl start ollama\n' +
                         'systemctl enable ollama'
        ]
        
        try:
            subprocess.run(cmd, check=True)
            print(f"✅ تم إنشاء VM: {vm_name}")
        except subprocess.CalledProcessError:
            print(f"⚠️ VM {vm_name} موجود مسبقاً أو حدث خطأ")
    
    def create_small_ollama_vm(self):
        """إنشاء VM أصغر للنماذج الأساسية"""
        print("🖥️ إنشاء VM صغير للنماذج الأساسية...")
        
        vm_name = "ollama-basic-vm"
        machine_type = "e2-standard-4"
        
        cmd = [
            'gcloud', 'compute', 'instances', 'create', vm_name,
            '--zone', self.zone,
            '--machine-type', machine_type,
            '--boot-disk-size', '50GB',
            '--boot-disk-type', 'pd-ssd',
            '--image-family', 'ubuntu-2204-lts',
            '--image-project', 'ubuntu-os-cloud',
            '--tags', 'ollama-basic',
            '--metadata', 'startup-script=#!/bin/bash\n' +
                         'apt-get update\n' +
                         'curl -fsSL https://ollama.ai/install.sh | sh\n' +
                         'systemctl start ollama\n' +
                         'ollama pull phi3:mini\n' +
                         'ollama pull mistral:7b'
        ]
        
        try:
            subprocess.run(cmd, check=True)
            print(f"✅ تم إنشاء VM صغير: {vm_name}")
        except subprocess.CalledProcessError:
            print(f"⚠️ VM {vm_name} موجود مسبقاً أو حدث خطأ")
    
    def setup_cloud_sql(self):
        """إعداد Cloud SQL"""
        print("🗄️ إعداد Cloud SQL...")
        
        instance_name = "anubis-db"
        
        cmd = [
            'gcloud', 'sql', 'instances', 'create', instance_name,
            '--database-version', 'MYSQL_8_0',
            '--tier', 'db-f1-micro',
            '--region', self.region,
            '--root-password', 'anubis_secure_2024'
        ]
        
        try:
            subprocess.run(cmd, check=True)
            print(f"✅ تم إنشاء Cloud SQL: {instance_name}")
        except subprocess.CalledProcessError:
            print(f"⚠️ Cloud SQL {instance_name} موجود مسبقاً أو حدث خطأ")
    
    def enable_vertex_ai(self):
        """تفعيل Vertex AI APIs"""
        print("🧠 تفعيل Vertex AI...")
        
        apis = [
            'aiplatform.googleapis.com',
            'ml.googleapis.com',
            'compute.googleapis.com',
            'cloudbuild.googleapis.com'
        ]
        
        for api in apis:
            try:
                subprocess.run(['gcloud', 'services', 'enable', api], check=True)
                print(f"✅ تم تفعيل: {api}")
            except:
                print(f"⚠️ مشكلة في تفعيل: {api}")
    
    def enable_all_apis(self):
        """تفعيل جميع APIs المطلوبة"""
        print("🔧 تفعيل جميع APIs...")
        
        apis = [
            'aiplatform.googleapis.com',
            'ml.googleapis.com',
            'compute.googleapis.com',
            'cloudbuild.googleapis.com',
            'run.googleapis.com',
            'sqladmin.googleapis.com',
            'storage.googleapis.com'
        ]
        
        for api in apis:
            try:
                subprocess.run(['gcloud', 'services', 'enable', api], check=True)
                print(f"✅ تم تفعيل: {api}")
            except:
                print(f"⚠️ مشكلة في تفعيل: {api}")
    
    def deploy_main_app(self):
        """نشر التطبيق الرئيسي"""
        print("🚀 نشر التطبيق الرئيسي...")
        
        # إنشاء ملفات التكوين
        self.create_deployment_files()
        
        # نشر على Cloud Run
        try:
            subprocess.run([
                'gcloud', 'run', 'deploy', 'anubis-system',
                '--source', '.',
                '--region', self.region,
                '--allow-unauthenticated'
            ], check=True)
            print("✅ تم نشر التطبيق على Cloud Run")
        except:
            print("⚠️ مشكلة في نشر التطبيق")
    
    def deploy_hybrid_app(self):
        """نشر التطبيق المختلط"""
        print("🚀 نشر التطبيق المختلط...")
        self.create_hybrid_deployment_files()
        self.deploy_main_app()
    
    def deploy_cloud_app(self):
        """نشر تطبيق Cloud فقط"""
        print("🚀 نشر تطبيق Cloud فقط...")
        self.create_cloud_deployment_files()
        self.deploy_main_app()
    
    def create_deployment_files(self):
        """إنشاء ملفات النشر"""
        print("📝 إنشاء ملفات النشر...")
        
        # إنشاء Dockerfile
        dockerfile_content = """
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8080
CMD ["python", "QUICK_START.py"]
"""
        
        with open("Dockerfile", "w") as f:
            f.write(dockerfile_content)
        
        print("✅ تم إنشاء ملفات النشر")
    
    def create_hybrid_deployment_files(self):
        """إنشاء ملفات النشر المختلط"""
        print("📝 إنشاء ملفات النشر المختلط...")
        self.create_deployment_files()
        # إضافة تكوين Vertex AI
    
    def create_cloud_deployment_files(self):
        """إنشاء ملفات نشر Cloud فقط"""
        print("📝 إنشاء ملفات نشر Cloud فقط...")
        self.create_deployment_files()
        # إضافة تكوين Cloud APIs فقط
    
    def configure_networking(self):
        """تكوين الشبكة"""
        print("🌐 تكوين الشبكة...")
        
        # إنشاء firewall rules
        try:
            subprocess.run([
                'gcloud', 'compute', 'firewall-rules', 'create', 'allow-ollama',
                '--allow', 'tcp:11434',
                '--source-ranges', '10.0.0.0/8',
                '--target-tags', 'ollama-server'
            ], check=True)
            print("✅ تم تكوين firewall للـ Ollama")
        except:
            print("⚠️ Firewall rule موجود مسبقاً")

if __name__ == "__main__":
    deployer = GoogleCloudDeployer()
    deployer.main()
