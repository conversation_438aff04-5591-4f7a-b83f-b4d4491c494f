#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import subprocess
import json
import time
from datetime import datetime

class AnubisServicesTest:
    def __init__(self):
        self.vm_ip = "**************"
        self.mysql_ip = "************"
        self.project_id = "anubis-467210"
        self.results = {}
        
    def test_vm_status(self):
        """اختبار حالة VM"""
        print("🖥️ اختبار Virtual Machine...")
        try:
            result = subprocess.run([
                'gcloud', 'compute', 'instances', 'describe', 
                'anubis-n8n-ollama-vm', '--zone=us-central1-a', 
                '--format=value(status)'
            ], capture_output=True, text=True, timeout=30)
            
            status = result.stdout.strip()
            self.results['vm_status'] = status
            print(f"   ✅ VM Status: {status}")
            return status == "RUNNING"
        except Exception as e:
            print(f"   ❌ VM Test Failed: {e}")
            self.results['vm_status'] = "ERROR"
            return False
    
    def test_mysql_database(self):
        """اختبار قاعدة البيانات MySQL"""
        print("🗄️ اختبار MySQL Database...")
        try:
            result = subprocess.run([
                'gcloud', 'sql', 'instances', 'describe', 
                'anubis-mysql-db', '--format=value(state)'
            ], capture_output=True, text=True, timeout=30)
            
            status = result.stdout.strip()
            self.results['mysql_status'] = status
            print(f"   ✅ MySQL Status: {status}")
            return status == "RUNNABLE"
        except Exception as e:
            print(f"   ❌ MySQL Test Failed: {e}")
            self.results['mysql_status'] = "ERROR"
            return False
    
    def test_n8n_service(self):
        """اختبار خدمة n8n"""
        print("🤖 اختبار n8n Service...")
        try:
            response = requests.get(f"http://{self.vm_ip}:5678", timeout=10)
            self.results['n8n_status'] = response.status_code
            if response.status_code == 200:
                print(f"   ✅ n8n Service: Running (Status: {response.status_code})")
                return True
            else:
                print(f"   ⚠️ n8n Service: Responding but status {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"   ❌ n8n Service: Not accessible ({e})")
            self.results['n8n_status'] = "UNREACHABLE"
            return False
    
    def test_ollama_service(self):
        """اختبار خدمة Ollama"""
        print("🧠 اختبار Ollama AI Service...")
        try:
            response = requests.get(f"http://{self.vm_ip}:11434", timeout=10)
            self.results['ollama_status'] = response.status_code
            if response.status_code == 200:
                print(f"   ✅ Ollama Service: Running (Status: {response.status_code})")
                return True
            else:
                print(f"   ⚠️ Ollama Service: Responding but status {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"   ❌ Ollama Service: Not accessible ({e})")
            self.results['ollama_status'] = "UNREACHABLE"
            return False
    
    def test_cloud_storage(self):
        """اختبار Cloud Storage"""
        print("📦 اختبار Cloud Storage...")
        try:
            result = subprocess.run([
                'gcloud', 'storage', 'buckets', 'describe', 
                'gs://anubis-storage-bucket-unique-467210'
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print("   ✅ Cloud Storage: Accessible")
                self.results['storage_status'] = "ACCESSIBLE"
                return True
            else:
                print("   ❌ Cloud Storage: Not accessible")
                self.results['storage_status'] = "ERROR"
                return False
        except Exception as e:
            print(f"   ❌ Cloud Storage Test Failed: {e}")
            self.results['storage_status'] = "ERROR"
            return False
    
    def test_firewall_rules(self):
        """اختبار قواعد Firewall"""
        print("🔥 اختبار Firewall Rules...")
        try:
            result = subprocess.run([
                'gcloud', 'compute', 'firewall-rules', 'list', 
                '--filter=name:(allow-n8n OR allow-ollama)', 
                '--format=value(name,allowed[].ports)'
            ], capture_output=True, text=True, timeout=30)
            
            if "allow-n8n" in result.stdout and "allow-ollama" in result.stdout:
                print("   ✅ Firewall Rules: Configured correctly")
                self.results['firewall_status'] = "CONFIGURED"
                return True
            else:
                print("   ❌ Firewall Rules: Missing rules")
                self.results['firewall_status'] = "INCOMPLETE"
                return False
        except Exception as e:
            print(f"   ❌ Firewall Test Failed: {e}")
            self.results['firewall_status'] = "ERROR"
            return False
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🧪 بدء اختبار جميع خدمات أنوبيس...")
        print("=" * 50)
        
        tests = [
            ("VM Status", self.test_vm_status),
            ("MySQL Database", self.test_mysql_database),
            ("Cloud Storage", self.test_cloud_storage),
            ("Firewall Rules", self.test_firewall_rules),
            ("n8n Service", self.test_n8n_service),
            ("Ollama Service", self.test_ollama_service)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            if test_func():
                passed += 1
            time.sleep(2)  # انتظار بين الاختبارات
        
        print("\n" + "=" * 50)
        print(f"📊 نتائج الاختبار: {passed}/{total} اختبار نجح")
        
        # حفظ النتائج
        self.results['timestamp'] = datetime.now().isoformat()
        self.results['summary'] = {
            'total_tests': total,
            'passed_tests': passed,
            'success_rate': f"{(passed/total)*100:.1f}%"
        }
        
        with open('services_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        print(f"💾 تم حفظ النتائج في: services_test_results.json")
        
        if passed == total:
            print("🎉 جميع الخدمات تعمل بنجاح!")
        elif passed >= total * 0.8:
            print("⚠️ معظم الخدمات تعمل - يحتاج بعض الإصلاحات")
        else:
            print("❌ عدة خدمات لا تعمل - يحتاج فحص شامل")
        
        return self.results

if __name__ == "__main__":
    tester = AnubisServicesTest()
    results = tester.run_all_tests()
