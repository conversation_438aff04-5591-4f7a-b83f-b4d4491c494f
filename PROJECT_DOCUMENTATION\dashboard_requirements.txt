# 🎛️ متطلبات داشبورد إدارة Anubis Cloud

# Streamlit Framework
streamlit>=1.28.0
streamlit-option-menu>=0.3.6

# Data Visualization
plotly>=5.15.0
pandas>=2.0.0
numpy>=1.24.0

# Google Cloud Libraries
google-cloud-compute>=1.14.0
google-cloud-run>=0.10.0
google-cloud-sql>=3.4.0
google-cloud-storage>=2.10.0
google-cloud-monitoring>=2.15.0
google-cloud-logging>=3.8.0
google-cloud-secret-manager>=2.16.0

# HTTP Requests
requests>=2.31.0
aiohttp>=3.8.0

# Utilities
python-dotenv>=1.0.0
pydantic>=2.0.0
PyYAML>=6.0

# Authentication
google-auth>=2.22.0
google-auth-oauthlib>=1.0.0

# CLI Tools
click>=8.1.0
rich>=13.5.0

# Development Tools
pytest>=7.4.0
black>=23.7.0
flake8>=6.0.0