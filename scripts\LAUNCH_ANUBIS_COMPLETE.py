#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 مشغل نظام أنوبيس المتكامل الكامل
Complete Anubis System Launcher
"""

import os
import sys
import time
import subprocess
import webbrowser
from pathlib import Path

# ألوان للإخراج
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    NC = '\033[0m'

def print_header():
    print(f"{Colors.PURPLE}{'='*70}{Colors.NC}")
    print(f"{Colors.PURPLE}🏺 مشغل نظام أنوبيس المتكامل الكامل{Colors.NC}")
    print(f"{Colors.PURPLE}Complete Anubis Integrated System Launcher{Colors.NC}")
    print(f"{Colors.PURPLE}{'='*70}{Colors.NC}")

def print_step(message):
    print(f"{Colors.BLUE}📋 {message}{Colors.NC}")

def print_success(message):
    print(f"{Colors.GREEN}✅ {message}{Colors.NC}")

def print_warning(message):
    print(f"{Colors.YELLOW}⚠️ {message}{Colors.NC}")

def print_error(message):
    print(f"{Colors.RED}❌ {message}{Colors.NC}")

def show_menu():
    """عرض قائمة الخيارات"""
    print(f"\n{Colors.CYAN}🎯 اختر طريقة التشغيل:{Colors.NC}")
    print(f"{Colors.GREEN}1. 🚀 تشغيل النظام الكامل (موصى به){Colors.NC}")
    print(f"{Colors.GREEN}2. 🏺 تشغيل نظام أنوبيس فقط{Colors.NC}")
    print(f"{Colors.GREEN}3. 𓅃 تشغيل فريق حورس فقط{Colors.NC}")
    print(f"{Colors.GREEN}4. 🔗 تشغيل نظام MCP فقط{Colors.NC}")
    print(f"{Colors.GREEN}5. 🌐 تشغيل الواجهة الموحدة فقط{Colors.NC}")
    print(f"{Colors.GREEN}6. 🔧 إعدادات متقدمة{Colors.NC}")
    print(f"{Colors.RED}0. ❌ خروج{Colors.NC}")

def run_command(cmd, description=""):
    """تشغيل أمر مع معالجة الأخطاء"""
    try:
        print_step(f"تشغيل: {description}")
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print_success(f"نجح: {description}")
            return True, result.stdout
        else:
            print_error(f"فشل: {description} - {result.stderr}")
            return False, result.stderr
    except Exception as e:
        print_error(f"خطأ: {description} - {e}")
        return False, str(e)

def launch_complete_system():
    """تشغيل النظام الكامل"""
    print_step("تشغيل النظام المتكامل الكامل...")
    
    # تشغيل النظام الكامل
    success, _ = run_command("python start_complete_anubis_system.py", "النظام المتكامل")
    
    if success:
        time.sleep(5)
        print_step("فتح الواجهات في المتصفح...")
        
        # فتح الواجهات
        interfaces = [
            ("http://localhost:5000", "الواجهة الموحدة"),
            ("http://localhost:8000", "نظام أنوبيس"),
            ("http://localhost:7000", "فريق حورس"),
        ]
        
        for url, name in interfaces:
            try:
                webbrowser.open(url)
                print_success(f"تم فتح {name}: {url}")
                time.sleep(1)
            except:
                print_warning(f"لا يمكن فتح {name} تلقائياً: {url}")
        
        show_system_info()
    
    return success

def launch_anubis_only():
    """تشغيل نظام أنوبيس فقط"""
    print_step("تشغيل نظام أنوبيس الأساسي...")
    
    os.chdir("ANUBIS_SYSTEM")
    success, _ = run_command("python main.py", "نظام أنوبيس")
    
    if success:
        webbrowser.open("http://localhost:8000")
        print_success("تم تشغيل نظام أنوبيس: http://localhost:8000")
    
    os.chdir("..")
    return success

def launch_horus_only():
    """تشغيل فريق حورس فقط"""
    print_step("تشغيل فريق حورس...")
    
    os.chdir("HORUS_AI_TEAM")
    success, _ = run_command("python 01_core/interfaces/horus_interface.py", "فريق حورس")
    
    if success:
        webbrowser.open("http://localhost:7000")
        print_success("تم تشغيل فريق حورس: http://localhost:7000")
    
    os.chdir("..")
    return success

def launch_mcp_only():
    """تشغيل نظام MCP فقط"""
    print_step("تشغيل نظام MCP...")
    
    os.chdir("ANUBIS_HORUS_MCP")
    success, _ = run_command("npm start", "نظام MCP")
    
    if success:
        webbrowser.open("http://localhost:3000")
        print_success("تم تشغيل نظام MCP: http://localhost:3000")
    
    os.chdir("..")
    return success

def launch_web_interface_only():
    """تشغيل الواجهة الموحدة فقط"""
    print_step("تشغيل الواجهة الموحدة...")
    
    success, _ = run_command("docker run -d --name anubis-web-client-server -p 5000:5000 anubis-web-client", "الواجهة الموحدة")
    
    if success:
        webbrowser.open("http://localhost:5000")
        print_success("تم تشغيل الواجهة الموحدة: http://localhost:5000")
    
    return success

def show_advanced_settings():
    """عرض الإعدادات المتقدمة"""
    print(f"\n{Colors.CYAN}🔧 الإعدادات المتقدمة:{Colors.NC}")
    print(f"{Colors.GREEN}1. 🔍 فحص حالة النظام{Colors.NC}")
    print(f"{Colors.GREEN}2. 🗄️ اختبار قاعدة البيانات{Colors.NC}")
    print(f"{Colors.GREEN}3. 🐳 إدارة حاويات Docker{Colors.NC}")
    print(f"{Colors.GREEN}4. 📊 عرض السجلات{Colors.NC}")
    print(f"{Colors.GREEN}5. 🧹 تنظيف النظام{Colors.NC}")
    print(f"{Colors.RED}0. ↩️ العودة للقائمة الرئيسية{Colors.NC}")

def show_system_info():
    """عرض معلومات النظام"""
    print(f"\n{Colors.CYAN}🌐 معلومات النظام المتكامل:{Colors.NC}")
    print(f"{Colors.GREEN}🏺 نظام أنوبيس: http://localhost:8000{Colors.NC}")
    print(f"{Colors.GREEN}𓅃 فريق حورس: http://localhost:7000{Colors.NC}")
    print(f"{Colors.GREEN}🔗 نظام MCP: http://localhost:3000{Colors.NC}")
    print(f"{Colors.GREEN}🌐 الواجهة الموحدة: http://localhost:5000{Colors.NC}")
    print(f"{Colors.GREEN}🗄️ قاعدة البيانات: MySQL محلية{Colors.NC}")
    print(f"{Colors.GREEN}💾 Redis: localhost:6379{Colors.NC}")
    
    print(f"\n{Colors.CYAN}🔧 أوامر مفيدة:{Colors.NC}")
    print(f"{Colors.YELLOW}عرض الحاويات: docker ps{Colors.NC}")
    print(f"{Colors.YELLOW}إيقاف النظام: docker stop anubis-core-server anubis-mcp-server horus-team-server{Colors.NC}")

def main():
    """الدالة الرئيسية"""
    print_header()
    
    while True:
        show_menu()
        
        try:
            choice = input(f"\n{Colors.CYAN}اختر رقم (0-6): {Colors.NC}").strip()
            
            if choice == "0":
                print_success("شكراً لاستخدام نظام أنوبيس! 👋")
                break
            elif choice == "1":
                launch_complete_system()
            elif choice == "2":
                launch_anubis_only()
            elif choice == "3":
                launch_horus_only()
            elif choice == "4":
                launch_mcp_only()
            elif choice == "5":
                launch_web_interface_only()
            elif choice == "6":
                show_advanced_settings()
                adv_choice = input(f"\n{Colors.CYAN}اختر رقم (0-5): {Colors.NC}").strip()
                
                if adv_choice == "1":
                    run_command("docker ps", "فحص حالة الحاويات")
                elif adv_choice == "2":
                    run_command("python start_anubis_with_local_mysql.py", "اختبار قاعدة البيانات")
                elif adv_choice == "3":
                    run_command("docker ps -a", "عرض جميع الحاويات")
                elif adv_choice == "4":
                    container = input("اسم الحاوية: ")
                    run_command(f"docker logs {container}", f"سجلات {container}")
                elif adv_choice == "5":
                    run_command("docker system prune -f", "تنظيف النظام")
            else:
                print_warning("اختيار غير صحيح! يرجى اختيار رقم من 0 إلى 6")
                
        except KeyboardInterrupt:
            print_warning("\nتم إيقاف التشغيل بواسطة المستخدم")
            break
        except Exception as e:
            print_error(f"خطأ غير متوقع: {e}")

if __name__ == "__main__":
    main()
