@echo off
echo 🏺 نشر Universal AI Assistants إلى Google Cloud
echo ================================================

echo 📋 الخطوة 1: إنشاء المشروع
gcloud projects create universal-ai-assistants-2025 --name="Universal AI Assistants"

echo 📋 الخطوة 2: تعيين المشروع النشط
gcloud config set project universal-ai-assistants-2025

echo 📋 الخطوة 3: تفعيل APIs المطلوبة
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com

echo 📋 الخطوة 4: النشر على Cloud Run
gcloud run deploy universal-ai-assistants --source https://github.com/amrashour1/universal-ai-assistants-agent --platform managed --region us-central1 --allow-unauthenticated --set-env-vars GEMINI_API_KEY="AIzaSyDJADkYbl6Hmhg76mfpPSl1yTE_jIVvo54" --memory 2Gi --cpu 2 --max-instances 10

echo 📋 الخطوة 5: الحصول على URL التطبيق
gcloud run services describe universal-ai-assistants --region us-central1 --format="value(status.url)"

echo ✅ تم إكمال النشر!
pause
