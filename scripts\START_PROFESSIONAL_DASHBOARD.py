#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎛️ مشغل الداشبورد الاحترافي مع n8n
=====================================
"""

import subprocess
import sys
import os
import time
import json
import requests
from datetime import datetime
import threading

class ProfessionalDashboardLauncher:
    def __init__(self):
        self.project_root = os.getcwd()
        self.n8n_process = None
        self.dashboard_process = None
        self.services_status = {
            'n8n': False,
            'dashboard': False,
            'workflows': False
        }
    
    def print_banner(self):
        """طباعة شعار النظام"""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                🎛️ Anubis Professional Dashboard              ║
║                     with n8n Integration                    ║
╠══════════════════════════════════════════════════════════════╣
║  🔄 n8n Automation Engine                                   ║
║  🌐 React Professional Dashboard                            ║
║  🏗️ Google Cloud Integration                                ║
║  📊 Real-time Monitoring                                    ║
║  💰 Cost Management                                         ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
    
    def check_requirements(self):
        """التحقق من المتطلبات"""
        print("🔍 التحقق من المتطلبات...")
        
        requirements = {
            'node': 'node --version',
            'npm': 'npm --version',
            'python': 'python --version'
        }
        
        missing = []
        
        for req, cmd in requirements.items():
            try:
                result = subprocess.run(cmd.split(), capture_output=True, text=True, shell=True)
                if result.returncode == 0:
                    version = result.stdout.strip()
                    print(f"✅ {req}: {version}")
                else:
                    missing.append(req)
                    print(f"❌ {req}: غير متوفر")
            except:
                missing.append(req)
                print(f"❌ {req}: غير متوفر")
        
        if missing:
            print(f"\n❌ المتطلبات المفقودة: {', '.join(missing)}")
            return False
        
        print("✅ جميع المتطلبات متوفرة!")
        return True
    
    def install_n8n(self):
        """تثبيت n8n"""
        print("\n📦 تثبيت n8n...")
        
        try:
            # التحقق من وجود n8n
            result = subprocess.run(['n8n', '--version'], capture_output=True, text=True, shell=True)
            if result.returncode == 0:
                print(f"✅ n8n متوفر: {result.stdout.strip()}")
                return True
        except:
            pass
        
        # تثبيت n8n
        print("📥 تثبيت n8n...")
        try:
            subprocess.run(['npm', 'install', '-g', 'n8n'], check=True, shell=True)
            print("✅ تم تثبيت n8n بنجاح!")
            return True
        except subprocess.CalledProcessError:
            print("❌ فشل في تثبيت n8n")
            return False
    
    def setup_n8n_environment(self):
        """إعداد بيئة n8n"""
        print("\n⚙️ إعداد بيئة n8n...")
        
        # إنشاء مجلد n8n
        n8n_dir = os.path.expanduser("~/.n8n")
        os.makedirs(n8n_dir, exist_ok=True)
        
        # إنشاء مجلد workflows
        workflows_dir = os.path.join(n8n_dir, "workflows")
        os.makedirs(workflows_dir, exist_ok=True)
        
        # نسخ workflows
        workflows_source = os.path.join(self.project_root, "n8n_workflows")
        if os.path.exists(workflows_source):
            import shutil
            for file in os.listdir(workflows_source):
                if file.endswith('.json'):
                    shutil.copy2(
                        os.path.join(workflows_source, file),
                        os.path.join(workflows_dir, file)
                    )
                    print(f"✅ تم نسخ workflow: {file}")
        
        # إعداد متغيرات البيئة
        env_vars = {
            'N8N_BASIC_AUTH_ACTIVE': 'true',
            'N8N_BASIC_AUTH_USER': 'admin',
            'N8N_BASIC_AUTH_PASSWORD': 'anubis123',
            'N8N_HOST': '0.0.0.0',
            'N8N_PORT': '5678',
            'N8N_PROTOCOL': 'http',
            'WEBHOOK_URL': 'http://localhost:5678',
            'N8N_EDITOR_BASE_URL': 'http://localhost:5678',
            'EXECUTIONS_DATA_SAVE_ON_ERROR': 'all',
            'EXECUTIONS_DATA_SAVE_ON_SUCCESS': 'all'
        }
        
        for key, value in env_vars.items():
            os.environ[key] = value
        
        print("✅ تم إعداد بيئة n8n!")
        return True
    
    def start_n8n(self):
        """تشغيل n8n"""
        print("\n🚀 تشغيل n8n...")
        
        try:
            # تشغيل n8n في الخلفية
            self.n8n_process = subprocess.Popen(
                ['n8n', 'start'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                shell=True
            )
            
            # انتظار تشغيل n8n
            print("⏳ انتظار تشغيل n8n...")
            time.sleep(10)
            
            # التحقق من تشغيل n8n
            try:
                response = requests.get('http://localhost:5678', timeout=5)
                if response.status_code == 200 or response.status_code == 401:
                    print("✅ n8n يعمل على http://localhost:5678")
                    self.services_status['n8n'] = True
                    return True
            except:
                pass
            
            print("⚠️ n8n قيد التشغيل...")
            return True
            
        except Exception as e:
            print(f"❌ فشل في تشغيل n8n: {e}")
            return False
    
    def setup_dashboard(self):
        """إعداد الداشبورد"""
        print("\n📱 إعداد الداشبورد...")
        
        dashboard_dir = os.path.join(self.project_root, "professional_dashboard")
        
        if not os.path.exists(dashboard_dir):
            print("❌ مجلد الداشبورد غير موجود")
            return False
        
        # الانتقال لمجلد الداشبورد
        os.chdir(dashboard_dir)
        
        # تثبيت dependencies
        if not os.path.exists("node_modules"):
            print("📦 تثبيت dependencies...")
            try:
                subprocess.run(['npm', 'install'], check=True, shell=True)
                print("✅ تم تثبيت dependencies!")
            except subprocess.CalledProcessError:
                print("❌ فشل في تثبيت dependencies")
                return False
        
        return True
    
    def start_dashboard(self):
        """تشغيل الداشبورد"""
        print("\n🌐 تشغيل الداشبورد...")
        
        try:
            # تشغيل React dashboard
            self.dashboard_process = subprocess.Popen(
                ['npm', 'start'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                shell=True
            )
            
            # انتظار تشغيل الداشبورد
            print("⏳ انتظار تشغيل الداشبورد...")
            time.sleep(15)
            
            # التحقق من تشغيل الداشبورد
            try:
                response = requests.get('http://localhost:3000', timeout=5)
                if response.status_code == 200:
                    print("✅ الداشبورد يعمل على http://localhost:3000")
                    self.services_status['dashboard'] = True
                    return True
            except:
                pass
            
            print("⚠️ الداشبورد قيد التشغيل...")
            return True
            
        except Exception as e:
            print(f"❌ فشل في تشغيل الداشبورد: {e}")
            return False
    
    def import_workflows(self):
        """استيراد workflows إلى n8n"""
        print("\n🔄 استيراد workflows...")
        
        workflows_dir = os.path.join(self.project_root, "n8n_workflows")
        
        if not os.path.exists(workflows_dir):
            print("❌ مجلد workflows غير موجود")
            return False
        
        # قائمة workflows
        workflows = [
            "anubis_deployment_workflow.json",
            "anubis_monitoring_workflow.json"
        ]
        
        imported_count = 0
        
        for workflow_file in workflows:
            workflow_path = os.path.join(workflows_dir, workflow_file)
            
            if os.path.exists(workflow_path):
                try:
                    with open(workflow_path, 'r', encoding='utf-8') as f:
                        workflow_data = json.load(f)
                    
                    # استيراد workflow عبر API
                    response = requests.post(
                        'http://localhost:5678/rest/workflows',
                        json=workflow_data,
                        auth=('admin', 'anubis123'),
                        timeout=10
                    )
                    
                    if response.status_code in [200, 201]:
                        print(f"✅ تم استيراد: {workflow_file}")
                        imported_count += 1
                    else:
                        print(f"⚠️ فشل في استيراد: {workflow_file}")
                        
                except Exception as e:
                    print(f"❌ خطأ في استيراد {workflow_file}: {e}")
            else:
                print(f"❌ ملف غير موجود: {workflow_file}")
        
        if imported_count > 0:
            print(f"✅ تم استيراد {imported_count} workflows!")
            self.services_status['workflows'] = True
            return True
        else:
            print("❌ لم يتم استيراد أي workflows")
            return False
    
    def open_browser(self):
        """فتح المتصفح"""
        print("\n🌐 فتح المتصفح...")
        
        urls = [
            ("الداشبورد الاحترافي", "http://localhost:3000"),
            ("n8n Editor", "http://localhost:5678")
        ]
        
        for name, url in urls:
            try:
                import webbrowser
                webbrowser.open(url)
                print(f"✅ تم فتح {name}: {url}")
                time.sleep(2)
            except:
                print(f"⚠️ لم يتم فتح {name}: {url}")
    
    def show_status(self):
        """عرض حالة الخدمات"""
        print("\n" + "="*60)
        print("📊 حالة الخدمات:")
        print("="*60)
        
        for service, status in self.services_status.items():
            status_icon = "✅" if status else "❌"
            print(f"{status_icon} {service}: {'يعمل' if status else 'متوقف'}")
        
        print("\n🌐 الروابط المتاحة:")
        print("• الداشبورد الاحترافي: http://localhost:3000")
        print("• n8n Editor: http://localhost:5678 (admin/anubis123)")
        print("• n8n Webhooks: http://localhost:5678/webhook/")
        
        print("\n💡 نصائح:")
        print("• استخدم الداشبورد للتحكم في النظام")
        print("• استخدم n8n Editor لتعديل workflows")
        print("• اضغط Ctrl+C لإيقاف النظام")
    
    def cleanup(self):
        """تنظيف العمليات"""
        print("\n🧹 إيقاف الخدمات...")
        
        if self.dashboard_process:
            self.dashboard_process.terminate()
            print("✅ تم إيقاف الداشبورد")
        
        if self.n8n_process:
            self.n8n_process.terminate()
            print("✅ تم إيقاف n8n")
        
        print("👋 تم إيقاف النظام بنجاح!")
    
    def run(self):
        """تشغيل النظام الكامل"""
        try:
            self.print_banner()
            
            # التحقق من المتطلبات
            if not self.check_requirements():
                return
            
            # تثبيت n8n
            if not self.install_n8n():
                return
            
            # إعداد n8n
            if not self.setup_n8n_environment():
                return
            
            # تشغيل n8n
            if not self.start_n8n():
                return
            
            # إعداد الداشبورد
            if not self.setup_dashboard():
                return
            
            # تشغيل الداشبورد
            if not self.start_dashboard():
                return
            
            # استيراد workflows
            time.sleep(5)  # انتظار حتى يصبح n8n جاهز
            self.import_workflows()
            
            # فتح المتصفح
            self.open_browser()
            
            # عرض الحالة
            self.show_status()
            
            # انتظار إيقاف المستخدم
            print("\n⏳ النظام يعمل... اضغط Ctrl+C للإيقاف")
            while True:
                time.sleep(1)
                
        except KeyboardInterrupt:
            print("\n⏹️ تم طلب إيقاف النظام...")
        except Exception as e:
            print(f"\n❌ خطأ في تشغيل النظام: {e}")
        finally:
            self.cleanup()

def main():
    """الدالة الرئيسية"""
    launcher = ProfessionalDashboardLauncher()
    launcher.run()

if __name__ == "__main__":
    main()
