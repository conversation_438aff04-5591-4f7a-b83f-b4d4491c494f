# 🏺 نشر مشروع Universal AI Assistants على Google Cloud

## 📋 معلومات المشروع
- **المستودع:** `**************:amrashour1/universal-ai-assistants-agent.git`
- **الهدف:** نشر المشروع على Google Cloud مباشرة من GitHub
- **النوع:** تطبيق ذكاء اصطناعي متعدد الوكلاء

## 🚀 خيارات النشر على Google Cloud

### 1. Google Cloud Run (الأسهل والأسرع)
```bash
# ربط GitHub مع Cloud Run
gcloud run deploy universal-ai-assistants \
  --source https://github.com/amrashour1/universal-ai-assistants-agent \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated
```

### 2. Google App Engine
```bash
# نشر مباشر من GitHub
gcloud app deploy \
  --source-url https://github.com/amrashour1/universal-ai-assistants-agent
```

### 3. Google Kubernetes Engine (GKE)
```bash
# إنشاء cluster
gcloud container clusters create ai-assistants-cluster \
  --zone us-central1-a

# نشر من GitHub
kubectl apply -f https://raw.githubusercontent.com/amrashour1/universal-ai-assistants-agent/main/k8s-deployment.yaml
```

### 4. Google Cloud Build (CI/CD)
```yaml
# cloudbuild.yaml
steps:
- name: 'gcr.io/cloud-builders/git'
  args: ['clone', 'https://github.com/amrashour1/universal-ai-assistants-agent.git']
- name: 'gcr.io/cloud-builders/docker'
  args: ['build', '-t', 'gcr.io/$PROJECT_ID/universal-ai-assistants', '.']
- name: 'gcr.io/cloud-builders/docker'
  args: ['push', 'gcr.io/$PROJECT_ID/universal-ai-assistants']
- name: 'gcr.io/cloud-builders/gcloud'
  args: ['run', 'deploy', 'universal-ai-assistants', 
         '--image', 'gcr.io/$PROJECT_ID/universal-ai-assistants',
         '--platform', 'managed', '--region', 'us-central1']
```

## 🔧 الخطوات المطلوبة

### الخطوة 1: إعداد Google Cloud Project
```bash
# إنشاء مشروع جديد
gcloud projects create universal-ai-assistants-2025

# تعيين المشروع النشط
gcloud config set project universal-ai-assistants-2025

# تفعيل APIs المطلوبة
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com
```

### الخطوة 2: إعداد الصلاحيات
```bash
# إنشاء service account
gcloud iam service-accounts create ai-assistants-sa \
  --display-name="AI Assistants Service Account"

# إعطاء الصلاحيات المطلوبة
gcloud projects add-iam-policy-binding universal-ai-assistants-2025 \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/run.admin"
```

### الخطوة 3: إعداد متغيرات البيئة
```bash
# إعداد مفاتيح API
gcloud secrets create gemini-api-key --data-file=- <<< "AIzaSyDJADkYbl6Hmhg76mfpPSl1yTE_jIVvo54"
gcloud secrets create qwen-api-key --data-file=- <<< "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"
```

### الخطوة 4: النشر المباشر
```bash
# النشر على Cloud Run من GitHub
gcloud run deploy universal-ai-assistants \
  --source https://github.com/amrashour1/universal-ai-assistants-agent \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --set-env-vars GEMINI_API_KEY="AIzaSyDJADkYbl6Hmhg76mfpPSl1yTE_jIVvo54" \
  --set-env-vars QWEN_API_KEY="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" \
  --memory 2Gi \
  --cpu 2 \
  --max-instances 10
```

## 🔐 إعداد الأمان

### 1. إدارة المفاتيح بـ Secret Manager
```bash
# إنشاء secrets للمفاتيح
gcloud secrets create api-keys --data-file=keys.json

# ربط secrets مع Cloud Run
gcloud run services update universal-ai-assistants \
  --update-secrets GEMINI_API_KEY=gemini-api-key:latest \
  --update-secrets QWEN_API_KEY=qwen-api-key:latest
```

### 2. إعداد IAM والصلاحيات
```bash
# تقييد الوصول
gcloud run services add-iam-policy-binding universal-ai-assistants \
  --member="allUsers" \
  --role="roles/run.invoker" \
  --region us-central1
```

## 📊 مراقبة ومتابعة النشر

### 1. مراقبة السجلات
```bash
# عرض سجلات التطبيق
gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=universal-ai-assistants" \
  --limit 50 \
  --format="table(timestamp,textPayload)"
```

### 2. مراقبة الأداء
```bash
# عرض مقاييس الأداء
gcloud monitoring metrics list --filter="metric.type:run.googleapis.com"
```

## 💰 تقدير التكاليف

### Cloud Run التكاليف المتوقعة:
- **CPU:** $0.000024 per vCPU-second
- **Memory:** $0.0000025 per GiB-second  
- **Requests:** $0.40 per million requests
- **التقدير الشهري:** $20-100 (حسب الاستخدام)

### Cloud Storage للنماذج:
- **Standard Storage:** $0.020 per GB/month
- **Network Egress:** $0.12 per GB
- **التقدير الشهري:** $10-50 (حسب حجم النماذج)

## 🔄 إعداد CI/CD التلقائي

### 1. إنشاء Cloud Build Trigger
```bash
# ربط GitHub مع Cloud Build
gcloud builds triggers create github \
  --repo-name=universal-ai-assistants-agent \
  --repo-owner=amrashour1 \
  --branch-pattern="^main$" \
  --build-config=cloudbuild.yaml
```

### 2. إعداد النشر التلقائي
```yaml
# cloudbuild.yaml في المستودع
steps:
  # بناء الصورة
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/universal-ai-assistants:$COMMIT_SHA', '.']
  
  # رفع الصورة
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/universal-ai-assistants:$COMMIT_SHA']
  
  # نشر على Cloud Run
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
    - 'run'
    - 'deploy'
    - 'universal-ai-assistants'
    - '--image'
    - 'gcr.io/$PROJECT_ID/universal-ai-assistants:$COMMIT_SHA'
    - '--region'
    - 'us-central1'
    - '--platform'
    - 'managed'
    - '--allow-unauthenticated'
```

## 🌐 الوصول للتطبيق

بعد النشر الناجح، ستحصل على:
- **URL التطبيق:** `https://universal-ai-assistants-[hash]-uc.a.run.app`
- **لوحة التحكم:** Google Cloud Console
- **المراقبة:** Cloud Monitoring & Logging

## 📋 قائمة التحقق

- [ ] إنشاء Google Cloud Project
- [ ] تفعيل APIs المطلوبة
- [ ] إعداد الصلاحيات والـ Service Accounts
- [ ] إنشاء Secrets للمفاتيح
- [ ] نشر التطبيق من GitHub
- [ ] اختبار التطبيق المنشور
- [ ] إعداد المراقبة والتنبيهات
- [ ] إعداد النسخ الاحتياطية
- [ ] توثيق URLs والوصول

## 🚀 الأوامر السريعة للنشر

```bash
# نشر سريع (أمر واحد)
gcloud run deploy universal-ai-assistants \
  --source https://github.com/amrashour1/universal-ai-assistants-agent \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --set-env-vars GEMINI_API_KEY="AIzaSyDJADkYbl6Hmhg76mfpPSl1yTE_jIVvo54"

# التحقق من الحالة
gcloud run services describe universal-ai-assistants --region us-central1

# عرض URL التطبيق
gcloud run services describe universal-ai-assistants \
  --region us-central1 \
  --format="value(status.url)"
```

---

## 🎯 الخلاصة

هذا الدليل يوفر طرق متعددة لنشر مشروع Universal AI Assistants من GitHub مباشرة إلى Google Cloud. الطريقة الأسرع هي استخدام Cloud Run مع النشر المباشر من المستودع.

**الخطوة التالية:** اختر الطريقة المناسبة وابدأ النشر!
