#!/usr/bin/env python3
"""
🎯 التشغيل السريع النهائي - Universal AI Assistants
معرف العملية: b6115c08-d625-4f21-b88c-fb641e45b0c8

هذا السكريبت يوفر تشغيل سريع وسهل لجميع مكونات النظام
"""

import os
import sys
import subprocess
import time
from pathlib import Path

class FinalQuickStart:
    def __init__(self):
        self.project_root = Path.cwd()
        self.operation_id = "b6115c08-d625-4f21-b88c-fb641e45b0c8"
        
    def print_header(self):
        """طباعة رأس البرنامج"""
        print("🏺" + "=" * 58 + "🏺")
        print("🎯 Universal AI Assistants - التشغيل السريع النهائي")
        print(f"📋 معرف العملية: {self.operation_id}")
        print("🌟 جميع العمليات مكتملة - النظام جاهز للاستخدام!")
        print("🏺" + "=" * 58 + "🏺")
        
    def show_menu(self):
        """عرض القائمة الرئيسية"""
        print("\n🎮 اختر ما تريد تشغيله:")
        print("=" * 40)
        print("1. 🚀 تشغيل سريع للنظام الأساسي")
        print("2. 𓅃 تشغيل فريق حورس (8 وكلاء ذكيين)")
        print("3. 🔗 تشغيل نظام MCP")
        print("4. 🌐 تشغيل جميع الأنظمة معاً")
        print("5. ☁️ نشر على Google Cloud")
        print("6. 🔄 تشغيل n8n للأتمتة")
        print("7. 📊 فحص حالة النظام")
        print("8. 📚 عرض التوثيق")
        print("9. 🧪 تشغيل الاختبارات")
        print("0. 🚪 خروج")
        print("=" * 40)
        
    def run_command(self, command, description=""):
        """تشغيل أمر مع عرض الحالة"""
        if description:
            print(f"\n🔄 {description}...")
            
        try:
            result = subprocess.run(
                command,
                shell=True,
                cwd=self.project_root,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                print(f"✅ {description} - نجح!")
                if result.stdout:
                    print(f"📤 المخرجات: {result.stdout[:200]}...")
                return True
            else:
                print(f"❌ {description} - فشل!")
                if result.stderr:
                    print(f"🚨 الخطأ: {result.stderr[:200]}...")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تشغيل {description}: {e}")
            return False
    
    def quick_system_start(self):
        """تشغيل سريع للنظام الأساسي"""
        print("\n🚀 تشغيل النظام الأساسي...")
        
        # تحقق من وجود الملفات
        if (self.project_root / "ANUBIS_SYSTEM" / "main.py").exists():
            return self.run_command(
                "python ANUBIS_SYSTEM/main.py",
                "تشغيل نظام أنوبيس الأساسي"
            )
        elif (self.project_root / "QUICK_START.py").exists():
            return self.run_command(
                "python QUICK_START.py",
                "تشغيل النظام عبر QUICK_START"
            )
        else:
            print("❌ لم يتم العثور على ملفات التشغيل الأساسية")
            return False
    
    def start_horus_team(self):
        """تشغيل فريق حورس"""
        print("\n𓅃 تشغيل فريق حورس...")
        
        horus_files = [
            "HORUS_AI_TEAM/quick_start_fixed.py",
            "HORUS_AI_TEAM/summon_horus_assistant.py",
            "HORUS_AI_TEAM/01_core/interfaces/horus_interface.py"
        ]
        
        for file_path in horus_files:
            if (self.project_root / file_path).exists():
                return self.run_command(
                    f"python {file_path}",
                    "تشغيل فريق حورس (8 وكلاء ذكيين)"
                )
        
        print("❌ لم يتم العثور على ملفات فريق حورس")
        return False
    
    def start_mcp_system(self):
        """تشغيل نظام MCP"""
        print("\n🔗 تشغيل نظام MCP...")
        
        mcp_path = self.project_root / "ANUBIS_HORUS_MCP"
        if mcp_path.exists():
            # تحقق من Node.js
            node_check = self.run_command("node --version", "فحص Node.js")
            if node_check:
                os.chdir(mcp_path)
                success = self.run_command("npm start", "تشغيل خادم MCP")
                os.chdir(self.project_root)
                return success
            else:
                print("❌ Node.js غير متاح - يرجى تثبيته أولاً")
                return False
        else:
            print("❌ مجلد ANUBIS_HORUS_MCP غير موجود")
            return False
    
    def start_all_systems(self):
        """تشغيل جميع الأنظمة"""
        print("\n🌐 تشغيل جميع الأنظمة...")
        
        if (self.project_root / "LAUNCH_ANUBIS_COMPLETE.py").exists():
            return self.run_command(
                "python LAUNCH_ANUBIS_COMPLETE.py",
                "تشغيل جميع الأنظمة معاً"
            )
        else:
            print("🔄 تشغيل الأنظمة واحداً تلو الآخر...")
            results = []
            results.append(self.quick_system_start())
            results.append(self.start_horus_team())
            results.append(self.start_mcp_system())
            
            success_count = sum(results)
            print(f"\n📊 النتيجة: {success_count}/3 أنظمة تعمل بنجاح")
            return success_count > 0
    
    def deploy_to_cloud(self):
        """نشر على Google Cloud"""
        print("\n☁️ نشر على Google Cloud...")
        
        # تحقق من Google Cloud CLI
        gcloud_check = self.run_command("gcloud --version", "فحص Google Cloud CLI")
        if not gcloud_check:
            print("❌ Google Cloud CLI غير متاح")
            return False
        
        # تحقق من ملف النشر
        if (self.project_root / "deploy_after_billing.py").exists():
            print("⚠️ تأكد من تفعيل الفوترة في Google Cloud أولاً!")
            print("🔗 https://console.cloud.google.com/billing")
            
            confirm = input("\n❓ هل تم تفعيل الفوترة؟ (y/n): ")
            if confirm.lower() == 'y':
                return self.run_command(
                    "python deploy_after_billing.py",
                    "نشر المشروع على Google Cloud"
                )
            else:
                print("⏸️ تم إلغاء النشر - فعل الفوترة أولاً")
                return False
        else:
            print("❌ ملف النشر غير موجود")
            return False
    
    def start_n8n(self):
        """تشغيل n8n للأتمتة"""
        print("\n🔄 تشغيل n8n للأتمتة...")
        
        # تحقق من Docker
        docker_check = self.run_command("docker --version", "فحص Docker")
        if not docker_check:
            print("❌ Docker غير متاح")
            return False
        
        # تشغيل n8n
        if (self.project_root / "docker-compose.n8n.yml").exists():
            return self.run_command(
                "docker-compose -f docker-compose.n8n.yml up -d",
                "تشغيل n8n في الخلفية"
            )
        else:
            return self.run_command(
                "docker run -d --name n8n -p 5678:5678 n8nio/n8n",
                "تشغيل n8n مباشرة"
            )
    
    def check_system_status(self):
        """فحص حالة النظام"""
        print("\n📊 فحص حالة النظام...")
        
        if (self.project_root / "COMPLETE_REMAINING_OPERATIONS.py").exists():
            return self.run_command(
                "python COMPLETE_REMAINING_OPERATIONS.py",
                "فحص شامل للنظام"
            )
        else:
            # فحص أساسي
            print("\n🔍 فحص أساسي للمكونات:")
            
            components = [
                ("ANUBIS_SYSTEM", "نظام أنوبيس"),
                ("HORUS_AI_TEAM", "فريق حورس"),
                ("ANUBIS_HORUS_MCP", "نظام MCP"),
                ("PROJECT_DOCUMENTATION", "التوثيق"),
                ("SHARED_REQUIREMENTS", "المتطلبات المشتركة")
            ]
            
            for folder, name in components:
                if (self.project_root / folder).exists():
                    file_count = len(list((self.project_root / folder).rglob("*")))
                    print(f"✅ {name}: {file_count} ملف")
                else:
                    print(f"❌ {name}: غير موجود")
            
            return True
    
    def show_documentation(self):
        """عرض التوثيق"""
        print("\n📚 التوثيق المتاح:")
        print("=" * 30)
        
        docs = [
            ("README.md", "الدليل الرئيسي"),
            ("DEPLOYMENT_GUIDE.md", "دليل النشر"),
            ("USER_GUIDE.md", "دليل المستخدم"),
            ("API_DOCUMENTATION.md", "توثيق API"),
            ("PROJECT_STRUCTURE_DETAILED.md", "هيكل المشروع"),
            ("DEVELOPMENT_RULES.md", "قواعد التطوير")
        ]
        
        for i, (file, desc) in enumerate(docs, 1):
            if (self.project_root / file).exists():
                print(f"{i}. ✅ {desc} ({file})")
            else:
                print(f"{i}. ❌ {desc} (غير موجود)")
        
        print("\n💡 لقراءة أي ملف، استخدم: notepad اسم_الملف")
        return True
    
    def run_tests(self):
        """تشغيل الاختبارات"""
        print("\n🧪 تشغيل الاختبارات...")
        
        test_files = [
            "COMPLETE_REMAINING_OPERATIONS.py",
            "FINAL_COMPLETE_SYSTEM_TEST.py",
            "test_all_services.py"
        ]
        
        for test_file in test_files:
            if (self.project_root / test_file).exists():
                return self.run_command(
                    f"python {test_file}",
                    f"تشغيل اختبارات {test_file}"
                )
        
        print("❌ لم يتم العثور على ملفات الاختبار")
        return False
    
    def run(self):
        """تشغيل البرنامج الرئيسي"""
        self.print_header()
        
        while True:
            self.show_menu()
            
            try:
                choice = input("\n🎯 اختر رقم (0-9): ").strip()
                
                if choice == "0":
                    print("\n👋 شكراً لاستخدام Universal AI Assistants!")
                    print("🌟 النظام جاهز للاستخدام في أي وقت")
                    break
                elif choice == "1":
                    self.quick_system_start()
                elif choice == "2":
                    self.start_horus_team()
                elif choice == "3":
                    self.start_mcp_system()
                elif choice == "4":
                    self.start_all_systems()
                elif choice == "5":
                    self.deploy_to_cloud()
                elif choice == "6":
                    self.start_n8n()
                elif choice == "7":
                    self.check_system_status()
                elif choice == "8":
                    self.show_documentation()
                elif choice == "9":
                    self.run_tests()
                else:
                    print("❌ اختيار غير صحيح - يرجى اختيار رقم من 0 إلى 9")
                
                input("\n⏸️ اضغط Enter للمتابعة...")
                
            except KeyboardInterrupt:
                print("\n\n👋 تم إيقاف البرنامج بواسطة المستخدم")
                break
            except Exception as e:
                print(f"\n❌ خطأ غير متوقع: {e}")
                input("⏸️ اضغط Enter للمتابعة...")

def main():
    """الدالة الرئيسية"""
    try:
        app = FinalQuickStart()
        app.run()
    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
