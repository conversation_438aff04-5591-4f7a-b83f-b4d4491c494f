#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 نشر Universal AI Assistants بعد تفعيل الفوترة
Deploy Universal AI Assistants after enabling billing
"""

import subprocess
import time
import json
from datetime import datetime

class PostBillingDeployer:
    """مُنشر ما بعد تفعيل الفوترة"""
    
    def __init__(self):
        self.project_id = "universal-ai-assistants-2025"
        self.service_name = "universal-ai-assistants"
        self.region = "us-central1"
        self.github_repo = "https://github.com/amrashour1/universal-ai-assistants-agent"
        self.gemini_key = "AIzaSyDJADkYbl6Hmhg76mfpPSl1yTE_jIVvo54"
        
    def run_command(self, command, description=""):
        """تشغيل أمر مع عرض الوصف"""
        if description:
            print(f"📋 {description}")
        print(f"🔄 تشغيل: {command}")
        
        try:
            result = subprocess.run(
                command, 
                shell=True, 
                capture_output=True, 
                text=True, 
                check=True
            )
            if result.stdout.strip():
                print(f"✅ النتيجة: {result.stdout.strip()}")
            return True, result.stdout
        except subprocess.CalledProcessError as e:
            print(f"❌ خطأ: {e}")
            if e.stderr:
                print(f"❌ تفاصيل: {e.stderr}")
            return False, e.stderr
            
    def check_billing_status(self):
        """التحقق من حالة الفوترة"""
        print("💳 التحقق من حالة الفوترة...")
        
        success, output = self.run_command(
            f"gcloud billing projects describe {self.project_id}",
            "فحص ربط حساب الفوترة"
        )
        
        if success and "billingAccountName" in output:
            print("✅ الفوترة مفعلة ومربوطة بالمشروع")
            return True
        else:
            print("❌ الفوترة غير مفعلة")
            print("🔗 يرجى تفعيل الفوترة من: https://console.cloud.google.com/billing")
            return False
            
    def enable_apis(self):
        """تفعيل APIs المطلوبة"""
        print("🔌 تفعيل APIs المطلوبة...")
        
        apis = [
            "cloudbuild.googleapis.com",
            "run.googleapis.com", 
            "containerregistry.googleapis.com",
            "artifactregistry.googleapis.com"
        ]
        
        for api in apis:
            success, _ = self.run_command(
                f"gcloud services enable {api}",
                f"تفعيل {api}"
            )
            if not success:
                return False
                
        print("✅ تم تفعيل جميع APIs بنجاح")
        return True
        
    def deploy_to_cloud_run(self):
        """النشر على Cloud Run"""
        print("🚀 بدء النشر على Cloud Run...")
        
        deploy_command = f"""gcloud run deploy {self.service_name} \
--source {self.github_repo} \
--platform managed \
--region {self.region} \
--allow-unauthenticated \
--set-env-vars GEMINI_API_KEY="{self.gemini_key}" \
--memory 2Gi \
--cpu 2 \
--max-instances 10 \
--timeout 3600"""
        
        print("⏳ هذا قد يستغرق 5-10 دقائق...")
        success, output = self.run_command(deploy_command, "النشر على Cloud Run")
        
        if success:
            print("✅ تم النشر بنجاح!")
            return True
        else:
            print("❌ فشل في النشر")
            return False
            
    def get_service_url(self):
        """الحصول على URL الخدمة"""
        print("🌐 الحصول على URL الخدمة...")
        
        success, output = self.run_command(
            f'gcloud run services describe {self.service_name} --region {self.region} --format="value(status.url)"',
            "استخراج URL الخدمة"
        )
        
        if success and output.strip():
            url = output.strip()
            print(f"🌐 URL التطبيق: {url}")
            return url
        else:
            print("❌ لم يتم العثور على URL")
            return None
            
    def test_deployment(self, url):
        """اختبار النشر"""
        if not url:
            return False
            
        print("🧪 اختبار التطبيق...")
        
        try:
            import requests
            response = requests.get(url, timeout=30)
            if response.status_code == 200:
                print("✅ التطبيق يعمل بنجاح!")
                return True
            else:
                print(f"⚠️ التطبيق يرد بكود: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
            return False
            
    def setup_monitoring(self):
        """إعداد المراقبة الأساسية"""
        print("📊 إعداد المراقبة...")
        
        # تفعيل Monitoring API
        self.run_command(
            "gcloud services enable monitoring.googleapis.com",
            "تفعيل Monitoring API"
        )
        
        print("✅ تم إعداد المراقبة الأساسية")
        
    def create_deployment_report(self, url, success):
        """إنشاء تقرير النشر"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        report = {
            "deployment_timestamp": timestamp,
            "project_id": self.project_id,
            "service_name": self.service_name,
            "region": self.region,
            "github_repo": self.github_repo,
            "deployment_success": success,
            "service_url": url,
            "configuration": {
                "memory": "2Gi",
                "cpu": "2",
                "max_instances": 10,
                "timeout": 3600
            },
            "apis_enabled": [
                "cloudbuild.googleapis.com",
                "run.googleapis.com",
                "containerregistry.googleapis.com",
                "artifactregistry.googleapis.com",
                "monitoring.googleapis.com"
            ]
        }
        
        report_file = f"deployment_report_{timestamp}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
            
        print(f"📄 تم حفظ تقرير النشر: {report_file}")
        return report_file
        
    def show_next_steps(self, url):
        """عرض الخطوات التالية"""
        print("\n" + "="*60)
        print("🎯 الخطوات التالية:")
        print("-"*60)
        
        if url:
            print(f"1. 🌐 زيارة التطبيق: {url}")
            print("2. 🧪 اختبار جميع الوظائف")
            print("3. 📊 مراقبة الأداء في Google Cloud Console")
            print("4. 💰 مراقبة التكاليف")
            print("5. 🔒 إعداد الأمان الإضافي (اختياري)")
        
        print("\n📋 أوامر إدارة مفيدة:")
        print(f"• عرض السجلات: gcloud logging read 'resource.type=cloud_run_revision' --limit 50")
        print(f"• تحديث الخدمة: gcloud run services update {self.service_name} --region {self.region}")
        print(f"• حذف الخدمة: gcloud run services delete {self.service_name} --region {self.region}")
        
    def deploy(self):
        """تنفيذ عملية النشر الكاملة"""
        print("🏺 بدء نشر Universal AI Assistants بعد تفعيل الفوترة")
        print("="*70)
        
        start_time = time.time()
        
        # التحقق من الفوترة
        if not self.check_billing_status():
            print("\n❌ يرجى تفعيل الفوترة أولاً:")
            print("🔗 https://console.cloud.google.com/billing")
            return False
            
        try:
            # تفعيل APIs
            if not self.enable_apis():
                print("❌ فشل في تفعيل APIs")
                return False
                
            # النشر
            if not self.deploy_to_cloud_run():
                print("❌ فشل في النشر")
                return False
                
            # الحصول على URL
            url = self.get_service_url()
            
            # إعداد المراقبة
            self.setup_monitoring()
            
            # اختبار النشر
            test_success = self.test_deployment(url)
            
            # إنشاء تقرير
            report_file = self.create_deployment_report(url, test_success)
            
            end_time = time.time()
            duration = end_time - start_time
            
            print("\n" + "="*70)
            print("🎉 تم إكمال النشر بنجاح!")
            print(f"⏱️ المدة الإجمالية: {duration:.2f} ثانية")
            print(f"🌐 URL التطبيق: {url}")
            print(f"📄 تقرير النشر: {report_file}")
            
            # عرض الخطوات التالية
            self.show_next_steps(url)
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في النشر: {e}")
            return False

def main():
    """الدالة الرئيسية"""
    deployer = PostBillingDeployer()
    
    print("🏺 مُنشر Universal AI Assistants - ما بعد تفعيل الفوترة")
    print("المستودع: https://github.com/amrashour1/universal-ai-assistants-agent")
    print("="*70)
    
    success = deployer.deploy()
    
    if success:
        print("\n🎉 تم النشر بنجاح! التطبيق جاهز للاستخدام.")
    else:
        print("\n❌ فشل في النشر. يرجى مراجعة الأخطاء ومحاولة مرة أخرى.")
        
    return success

if __name__ == "__main__":
    main()
