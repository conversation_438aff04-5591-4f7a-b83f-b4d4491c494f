{"system_status": {"timestamp": "20250731_053102", "operation_id": "b6115c08-d625-4f21-b88c-fb641e45b0c8", "project_root": "C:\\Users\\<USER>\\Universal-AI-Assistants", "components": {"ANUBIS_SYSTEM": {"exists": true, "file_count": 3118, "size_mb": 84.96}, "HORUS_AI_TEAM": {"exists": true, "file_count": 886, "size_mb": 4.02}, "ANUBIS_HORUS_MCP": {"exists": true, "file_count": 1050, "size_mb": 8.5}, "PROJECT_DOCUMENTATION": {"exists": true, "file_count": 61, "size_mb": 9.07}, "SHARED_REQUIREMENTS": {"exists": true, "file_count": 41, "size_mb": 0.41}}, "services": {"docker": {"available": true, "version": "Docker version 28.3.2, build 578ccf6"}, "python": {"available": true, "version": "Python 3.13.5"}, "git": {"available": true, "version": "git version 2.49.0.windows.1"}, "gcloud": {"available": true, "output": "Google Cloud SDK 523.0.1\nbeta 2025.05.22\nbq 2.1.17\ncore 2025.05.22\ngcloud-crc32c 1.0.0\ngsutil 5.34\n"}}}, "deployment_status": {"google_cloud": {"files_ready": true, "missing_files": [], "project_id": "universal-ai-assistants-2025", "project_configured": true}, "github": {"repository_connected": true, "remotes": "origin\thttps://github.com/amrashour1/universal-ai-assistants-agent.git (fetch)\norigin\thttps://github.com/amrashour1/universal-ai-assistants-agent.git (push)\n"}, "local_services": {"port_5000": {"checked": true, "note": "Manual check required"}, "port_5678": {"checked": true, "note": "Manual check required"}, "port_7000": {"checked": true, "note": "Manual check required"}, "port_8000": {"checked": true, "note": "Manual check required"}, "port_8080": {"checked": true, "note": "Manual check required"}}, "n8n": {"files_ready": true, "missing_files": [], "workflows_count": 4, "workflows_ready": true}}, "test_results": {"anubis_system": {"folder_exists": {"passed": true}, "main_file": {"passed": true}, "config_folder": {"passed": true}, "src_folder": {"passed": true}, "dockerfile": {"passed": true}}, "horus_team": {"folder_exists": {"passed": true}, "core_folder": {"passed": true}, "team_members": {"passed": true}, "memory_system": {"passed": true}, "readme": {"passed": true}}, "mcp_system": {"folder_exists": {"passed": true}, "package_json": {"passed": true}, "core_folder": {"passed": true}, "src_folder": {"passed": true}, "api_keys_vault": {"passed": true}}, "documentation": {"folder_exists": {"passed": true}, "readme_readme": {"passed": true}, "readme_project_structure_detailed": {"passed": true}, "readme_project_paths_directory": {"passed": true}, "readme_development_rules": {"passed": true}}, "summary": {"total_tests": 20, "passed_tests": 20, "success_rate": 100.0}}}