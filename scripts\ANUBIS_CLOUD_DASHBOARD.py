#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎛️ داشبورد إدارة Universal AI Assistants على Google Cloud
========================================================
"""

import streamlit as st
import subprocess
import json
import time
import requests
from datetime import datetime
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go

class AnubisCloudDashboard:
    def __init__(self):
        self.project_id = "anubis-467210"
        self.region = "us-central1"
        self.zone = "us-central1-a"
        
        # تكوين الصفحة
        st.set_page_config(
            page_title="🎛️ Anubis Cloud Dashboard",
            page_icon="🏺",
            layout="wide",
            initial_sidebar_state="expanded"
        )
        
    def main(self):
        """الواجهة الرئيسية للداشبورد"""
        
        # العنوان الرئيسي
        st.title("🎛️ Anubis Cloud Management Dashboard")
        st.markdown("---")
        
        # الشريط الجانبي
        self.create_sidebar()
        
        # المحتوى الرئيسي
        tab1, tab2, tab3, tab4, tab5 = st.tabs([
            "🏠 نظرة عامة", 
            "🚀 النشر", 
            "📊 المراقبة", 
            "🔧 الإدارة", 
            "💰 التكلفة"
        ])
        
        with tab1:
            self.overview_tab()
        
        with tab2:
            self.deployment_tab()
        
        with tab3:
            self.monitoring_tab()
        
        with tab4:
            self.management_tab()
        
        with tab5:
            self.cost_tab()
    
    def create_sidebar(self):
        """إنشاء الشريط الجانبي"""
        st.sidebar.title("🏺 Anubis Control Panel")
        
        # معلومات المشروع
        st.sidebar.markdown("### 📋 معلومات المشروع")
        st.sidebar.info(f"""
        **Project ID:** {self.project_id}
        **Region:** {self.region}
        **Zone:** {self.zone}
        **Status:** 🟢 Active
        """)
        
        # أزرار التحكم السريع
        st.sidebar.markdown("### ⚡ تحكم سريع")
        
        col1, col2 = st.sidebar.columns(2)
        
        with col1:
            if st.button("🚀 نشر", key="quick_deploy"):
                self.quick_deploy()
        
        with col2:
            if st.button("⏹️ إيقاف", key="quick_stop"):
                self.quick_stop()
        
        if st.sidebar.button("🔄 إعادة تشغيل", key="restart_all"):
            self.restart_all_services()
        
        if st.sidebar.button("📊 تحديث البيانات", key="refresh_data"):
            st.rerun()
        
        # حالة الخدمات
        st.sidebar.markdown("### 🔍 حالة الخدمات")
        self.show_services_status()
    
    def overview_tab(self):
        """تبويب النظرة العامة"""
        st.header("🏠 نظرة عامة على النظام")
        
        # مقاييس سريعة
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric(
                label="🖥️ VMs النشطة",
                value=self.get_active_vms_count(),
                delta="1"
            )
        
        with col2:
            st.metric(
                label="🚀 Cloud Run Services",
                value=self.get_cloud_run_services_count(),
                delta="2"
            )
        
        with col3:
            st.metric(
                label="🗄️ Databases",
                value=self.get_databases_count(),
                delta="1"
            )
        
        with col4:
            st.metric(
                label="💰 التكلفة الشهرية",
                value="$325",
                delta="-$50"
            )
        
        # خريطة الخدمات
        st.subheader("🗺️ خريطة الخدمات")
        self.show_services_map()
        
        # الأنشطة الأخيرة
        st.subheader("📋 الأنشطة الأخيرة")
        self.show_recent_activities()
    
    def deployment_tab(self):
        """تبويب النشر"""
        st.header("🚀 إدارة النشر")
        
        # خيارات النشر
        deployment_type = st.selectbox(
            "اختر نوع النشر:",
            ["النهج المختلط (موصى به)", "Ollama كامل", "Cloud فقط"]
        )
        
        # تكوين النشر
        st.subheader("⚙️ تكوين النشر")
        
        col1, col2 = st.columns(2)
        
        with col1:
            vm_type = st.selectbox(
                "نوع VM:",
                ["e2-standard-4", "e2-standard-8", "n1-standard-4"]
            )
            
            disk_size = st.slider("حجم القرص (GB):", 20, 200, 50)
            
        with col2:
            max_instances = st.slider("أقصى عدد instances:", 1, 20, 10)
            
            memory_limit = st.selectbox(
                "حد الذاكرة:",
                ["1Gi", "2Gi", "4Gi", "8Gi"]
            )
        
        # معاينة التكوين
        st.subheader("👁️ معاينة التكوين")
        config_preview = {
            "deployment_type": deployment_type,
            "vm_type": vm_type,
            "disk_size": f"{disk_size}GB",
            "max_instances": max_instances,
            "memory_limit": memory_limit,
            "estimated_cost": self.calculate_estimated_cost(vm_type, disk_size, max_instances)
        }
        
        st.json(config_preview)
        
        # أزرار النشر
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("🚀 بدء النشر", key="start_deployment"):
                self.start_deployment(config_preview)
        
        with col2:
            if st.button("⏸️ إيقاف مؤقت", key="pause_deployment"):
                self.pause_deployment()
        
        with col3:
            if st.button("🗑️ حذف النشر", key="delete_deployment"):
                self.delete_deployment()
        
        # سجل النشر
        st.subheader("📜 سجل النشر")
        self.show_deployment_logs()
    
    def monitoring_tab(self):
        """تبويب المراقبة"""
        st.header("📊 مراقبة النظام")
        
        # مقاييس الأداء في الوقت الفعلي
        st.subheader("⚡ الأداء في الوقت الفعلي")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # رسم بياني لاستخدام CPU
            cpu_data = self.get_cpu_usage_data()
            fig_cpu = px.line(
                cpu_data, 
                x='time', 
                y='cpu_usage',
                title='استخدام المعالج (%)',
                color_discrete_sequence=['#ff6b6b']
            )
            st.plotly_chart(fig_cpu, use_container_width=True)
        
        with col2:
            # رسم بياني لاستخدام الذاكرة
            memory_data = self.get_memory_usage_data()
            fig_memory = px.line(
                memory_data,
                x='time',
                y='memory_usage',
                title='استخدام الذاكرة (%)',
                color_discrete_sequence=['#4ecdc4']
            )
            st.plotly_chart(fig_memory, use_container_width=True)
        
        # حالة الخدمات
        st.subheader("🔍 حالة الخدمات")
        services_status = self.get_services_detailed_status()
        
        for service, status in services_status.items():
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.write(f"**{service}**")
            
            with col2:
                status_color = "🟢" if status['status'] == 'running' else "🔴"
                st.write(f"{status_color} {status['status']}")
            
            with col3:
                st.write(f"CPU: {status['cpu']}%")
            
            with col4:
                st.write(f"Memory: {status['memory']}%")
        
        # تنبيهات
        st.subheader("🚨 التنبيهات")
        alerts = self.get_system_alerts()
        
        if alerts:
            for alert in alerts:
                alert_type = "error" if alert['severity'] == 'high' else "warning"
                st.alert(f"**{alert['service']}**: {alert['message']}", icon="🚨")
        else:
            st.success("✅ لا توجد تنبيهات حالياً")
    
    def management_tab(self):
        """تبويب الإدارة"""
        st.header("🔧 إدارة الخدمات")
        
        # إدارة VMs
        st.subheader("🖥️ إدارة Virtual Machines")
        
        vms = self.get_vms_list()
        
        for vm in vms:
            with st.expander(f"🖥️ {vm['name']} - {vm['status']}"):
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    st.write(f"**Type:** {vm['machine_type']}")
                    st.write(f"**Zone:** {vm['zone']}")
                
                with col2:
                    st.write(f"**IP:** {vm['external_ip']}")
                    st.write(f"**Disk:** {vm['disk_size']}")
                
                with col3:
                    if vm['status'] == 'RUNNING':
                        if st.button(f"⏹️ إيقاف {vm['name']}", key=f"stop_{vm['name']}"):
                            self.stop_vm(vm['name'])
                    else:
                        if st.button(f"▶️ تشغيل {vm['name']}", key=f"start_{vm['name']}"):
                            self.start_vm(vm['name'])
                    
                    if st.button(f"🔄 إعادة تشغيل {vm['name']}", key=f"restart_{vm['name']}"):
                        self.restart_vm(vm['name'])
        
        # إدارة Cloud Run
        st.subheader("🚀 إدارة Cloud Run Services")
        
        cloud_run_services = self.get_cloud_run_services()
        
        for service in cloud_run_services:
            with st.expander(f"🚀 {service['name']} - {service['status']}"):
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    st.write(f"**URL:** {service['url']}")
                    st.write(f"**Region:** {service['region']}")
                
                with col2:
                    st.write(f"**CPU:** {service['cpu']}")
                    st.write(f"**Memory:** {service['memory']}")
                
                with col3:
                    if st.button(f"🔄 إعادة نشر {service['name']}", key=f"redeploy_{service['name']}"):
                        self.redeploy_service(service['name'])
                    
                    if st.button(f"📊 عرض السجلات {service['name']}", key=f"logs_{service['name']}"):
                        self.show_service_logs(service['name'])
        
        # إدارة قواعد البيانات
        st.subheader("🗄️ إدارة قواعد البيانات")
        
        databases = self.get_databases_list()
        
        for db in databases:
            with st.expander(f"🗄️ {db['name']} - {db['status']}"):
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    st.write(f"**Type:** {db['database_version']}")
                    st.write(f"**Tier:** {db['tier']}")
                
                with col2:
                    st.write(f"**Storage:** {db['storage_size']}")
                    st.write(f"**Region:** {db['region']}")
                
                with col3:
                    if st.button(f"💾 نسخ احتياطي {db['name']}", key=f"backup_{db['name']}"):
                        self.backup_database(db['name'])
                    
                    if st.button(f"🔄 إعادة تشغيل {db['name']}", key=f"restart_db_{db['name']}"):
                        self.restart_database(db['name'])
    
    def cost_tab(self):
        """تبويب التكلفة"""
        st.header("💰 إدارة التكلفة")
        
        # ملخص التكلفة
        st.subheader("📊 ملخص التكلفة الشهرية")
        
        cost_data = self.get_cost_breakdown()
        
        # رسم بياني دائري للتكلفة
        fig_pie = px.pie(
            values=list(cost_data.values()),
            names=list(cost_data.keys()),
            title="توزيع التكلفة حسب الخدمة"
        )
        st.plotly_chart(fig_pie, use_container_width=True)
        
        # جدول التكلفة التفصيلي
        cost_df = pd.DataFrame([
            {"الخدمة": service, "التكلفة الشهرية": f"${cost}", "النسبة": f"{(cost/sum(cost_data.values()))*100:.1f}%"}
            for service, cost in cost_data.items()
        ])
        
        st.dataframe(cost_df, use_container_width=True)
        
        # توقعات التكلفة
        st.subheader("📈 توقعات التكلفة")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.metric("التكلفة الحالية", "$325/شهر", "-$50")
            st.metric("التكلفة المتوقعة", "$280/شهر", "-$45")
        
        with col2:
            # رسم بياني لتوقعات التكلفة
            forecast_data = self.get_cost_forecast()
            fig_forecast = px.line(
                forecast_data,
                x='month',
                y='cost',
                title='توقعات التكلفة للأشهر القادمة'
            )
            st.plotly_chart(fig_forecast, use_container_width=True)
        
        # توصيات التوفير
        st.subheader("💡 توصيات التوفير")
        
        savings_recommendations = [
            "🔄 استخدام Preemptible VMs يمكن أن يوفر 60-80%",
            "📊 تحسين استخدام Cloud Run يمكن أن يوفر $30/شهر",
            "🗄️ تحسين حجم قاعدة البيانات يمكن أن يوفر $15/شهر",
            "💾 استخدام Nearline Storage للملفات القديمة يوفر $10/شهر"
        ]
        
        for recommendation in savings_recommendations:
            st.info(recommendation)
    
    # دوال مساعدة لجلب البيانات
    def get_active_vms_count(self):
        """جلب عدد VMs النشطة"""
        try:
            result = subprocess.run([
                'gcloud', 'compute', 'instances', 'list',
                '--filter', 'status:RUNNING',
                '--format', 'value(name)'
            ], capture_output=True, text=True, shell=True)
            return len(result.stdout.strip().split('\n')) if result.stdout.strip() else 0
        except:
            return 1  # قيمة افتراضية
    
    def get_cloud_run_services_count(self):
        """جلب عدد خدمات Cloud Run"""
        try:
            result = subprocess.run([
                'gcloud', 'run', 'services', 'list',
                '--region', self.region,
                '--format', 'value(metadata.name)'
            ], capture_output=True, text=True, shell=True)
            return len(result.stdout.strip().split('\n')) if result.stdout.strip() else 0
        except:
            return 2  # قيمة افتراضية
    
    def get_databases_count(self):
        """جلب عدد قواعد البيانات"""
        try:
            result = subprocess.run([
                'gcloud', 'sql', 'instances', 'list',
                '--format', 'value(name)'
            ], capture_output=True, text=True, shell=True)
            return len(result.stdout.strip().split('\n')) if result.stdout.strip() else 0
        except:
            return 1  # قيمة افتراضية
    
    def show_services_status(self):
        """عرض حالة الخدمات في الشريط الجانبي"""
        services = {
            "Ollama VM": "🟢",
            "Cloud Run": "🟢", 
            "Cloud SQL": "🟢",
            "Storage": "🟢"
        }
        
        for service, status in services.items():
            st.sidebar.write(f"{status} {service}")
    
    def show_services_map(self):
        """عرض خريطة الخدمات"""
        # رسم بياني لهيكل الخدمات
        import networkx as nx
        import matplotlib.pyplot as plt
        
        # إنشاء رسم بياني بسيط للخدمات
        services_data = {
            "Load Balancer": ["Cloud Run", "Ollama VM"],
            "Cloud Run": ["Cloud SQL", "Storage"],
            "Ollama VM": ["Storage"],
            "Cloud SQL": [],
            "Storage": []
        }
        
        st.write("🗺️ هيكل الخدمات:")
        st.write("```")
        st.write("Internet → Load Balancer → Cloud Run → Cloud SQL")
        st.write("                    ↓")
        st.write("                Ollama VM → Storage")
        st.write("```")
    
    def show_recent_activities(self):
        """عرض الأنشطة الأخيرة"""
        activities = [
            {"time": "10:30", "action": "تم تشغيل Ollama VM", "status": "✅"},
            {"time": "10:25", "action": "تم نشر Cloud Run Service", "status": "✅"},
            {"time": "10:20", "action": "تم إنشاء Cloud SQL Instance", "status": "✅"},
            {"time": "10:15", "action": "تم تفعيل APIs", "status": "✅"}
        ]
        
        for activity in activities:
            st.write(f"{activity['status']} {activity['time']} - {activity['action']}")
    
    # دوال العمليات
    def quick_deploy(self):
        """نشر سريع"""
        st.success("🚀 بدء النشر السريع...")
        # هنا يمكن إضافة كود النشر الفعلي
    
    def quick_stop(self):
        """إيقاف سريع"""
        st.warning("⏹️ إيقاف جميع الخدمات...")
        # هنا يمكن إضافة كود الإيقاف الفعلي
    
    def restart_all_services(self):
        """إعادة تشغيل جميع الخدمات"""
        st.info("🔄 إعادة تشغيل جميع الخدمات...")
        # هنا يمكن إضافة كود إعادة التشغيل الفعلي
    
    # دوال البيانات الوهمية (يمكن استبدالها ببيانات حقيقية)
    def get_cpu_usage_data(self):
        import pandas as pd
        import numpy as np
        
        times = pd.date_range(start='2024-01-01 10:00', periods=20, freq='5min')
        cpu_usage = np.random.randint(20, 80, 20)
        
        return pd.DataFrame({'time': times, 'cpu_usage': cpu_usage})
    
    def get_memory_usage_data(self):
        import pandas as pd
        import numpy as np
        
        times = pd.date_range(start='2024-01-01 10:00', periods=20, freq='5min')
        memory_usage = np.random.randint(30, 70, 20)
        
        return pd.DataFrame({'time': times, 'memory_usage': memory_usage})
    
    def get_services_detailed_status(self):
        return {
            "Ollama VM": {"status": "running", "cpu": 45, "memory": 60},
            "Cloud Run": {"status": "running", "cpu": 25, "memory": 40},
            "Cloud SQL": {"status": "running", "cpu": 15, "memory": 30},
            "Storage": {"status": "running", "cpu": 5, "memory": 10}
        }
    
    def get_system_alerts(self):
        return []  # لا توجد تنبيهات حالياً
    
    def get_cost_breakdown(self):
        return {
            "Compute Engine": 120,
            "Cloud Run": 75,
            "Cloud SQL": 25,
            "Storage": 15,
            "Vertex AI": 90
        }
    
    def get_cost_forecast(self):
        import pandas as pd
        
        months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']
        costs = [325, 310, 295, 280, 275, 270]
        
        return pd.DataFrame({'month': months, 'cost': costs})

if __name__ == "__main__":
    dashboard = AnubisCloudDashboard()
    dashboard.main()
