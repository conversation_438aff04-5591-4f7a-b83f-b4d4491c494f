{"timestamp": "20250731_064109", "project_analysis": {"structure": {"main_directories": {"ANUBIS_SYSTEM": {"exists": true, "file_count": 3118, "size_mb": 84.96}, "HORUS_AI_TEAM": {"exists": true, "file_count": 886, "size_mb": 4.02}, "ANUBIS_HORUS_MCP": {"exists": true, "file_count": 1050, "size_mb": 8.5}, "PROJECT_DOCUMENTATION": {"exists": true, "file_count": 61, "size_mb": 9.07}, "SHARED_REQUIREMENTS": {"exists": true, "file_count": 41, "size_mb": 0.41}, "data": {"exists": true, "file_count": 22, "size_mb": 0.69}, "docs": {"exists": true, "file_count": 56, "size_mb": 0.71}, "scripts": {"exists": true, "file_count": 12, "size_mb": 0.22}, "archive_and_backups": {"exists": true, "file_count": 208, "size_mb": 6.03}}, "loose_files": [{"name": "AI_ASSISTANTS_TASK_DISTRIBUTOR.py", "size_kb": 17.6650390625, "extension": ".py", "modified": "2025-07-30T01:34:05.098048"}, {"name": "ANUBIS_CLOUD_DASHBOARD.py", "size_kb": 19.927734375, "extension": ".py", "modified": "2025-07-29T12:25:01.674840"}, {"name": "ANUBIS_HORUS_MCP.tar.gz", "size_kb": 1470.921875, "extension": ".gz", "modified": "2025-07-29T15:47:56.283812"}, {"name": "ANUBIS_PROFESSIONAL_DASHBOARD_GUIDE.md", "size_kb": 9.1142578125, "extension": ".md", "modified": "2025-07-29T12:56:52.556616"}, {"name": "ANUBIS_SYSTEM.tar.gz", "size_kb": 13682.**********, "extension": ".gz", "modified": "2025-07-29T15:47:42.332172"}, {"name": "API_DOCUMENTATION.md", "size_kb": 0.796875, "extension": ".md", "modified": "2025-07-31T05:32:48.454744"}, {"name": "app.n8n.yaml", "size_kb": 0.5146484375, "extension": ".yaml", "modified": "2025-07-30T05:36:12.463365"}, {"name": "cloudbuild.yaml", "size_kb": 11.6630859375, "extension": ".yaml", "modified": "2025-07-28T05:14:48.588665"}, {"name": "CLOUD_DEPLOYMENT_ISSUES_ANALYSIS.md", "size_kb": 8.6572265625, "extension": ".md", "modified": "2025-07-31T06:05:16.991086"}, {"name": "cloud_fix_report.json", "size_kb": 0.4501953125, "extension": ".json", "modified": "2025-07-31T06:27:40.205839"}, {"name": "CLOUD_MODELS_GUIDE.md", "size_kb": 6.0830078125, "extension": ".md", "modified": "2025-07-30T04:52:31.153732"}, {"name": "cloud_models_manager.py", "size_kb": 9.541015625, "extension": ".py", "modified": "2025-07-30T05:00:05.316467"}, {"name": "COLLABORATIVE_SECRET_HUNTER.py", "size_kb": 14.2998046875, "extension": ".py", "modified": "2025-07-29T05:37:54.990657"}, {"name": "COMPLETE_REMAINING_OPERATIONS.py", "size_kb": 13.5966796875, "extension": ".py", "modified": "2025-07-31T05:30:54.664186"}, {"name": "COMPLETE_REMAINING_OPERATIONS_PHASE2.py", "size_kb": 13.77734375, "extension": ".py", "modified": "2025-07-31T05:32:39.293771"}, {"name": "CREATE_FRESH_REPO.py", "size_kb": 12.7958984375, "extension": ".py", "modified": "2025-07-29T06:21:45.546466"}, {"name": "CREATE_NEW_REPO_AND_UPLOAD.py", "size_kb": 19.4306640625, "extension": ".py", "modified": "2025-07-29T05:57:30.876343"}, {"name": "dashboard_config.json", "size_kb": 0.6748046875, "extension": ".json", "modified": "2025-07-29T15:42:31.396880"}, {"name": "dashboard_requirements.txt", "size_kb": 0.689453125, "extension": ".txt", "modified": "2025-07-29T12:29:52.691721"}, {"name": "DASHBOARD_USER_GUIDE.md", "size_kb": 7.828125, "extension": ".md", "modified": "2025-07-29T12:30:52.741510"}, {"name": "db_alert_policy.json", "size_kb": 0.5263671875, "extension": ".json", "modified": "2025-07-29T15:42:28.333972"}, {"name": "deploy-to-github.ps1", "size_kb": 4.611328125, "extension": ".ps1", "modified": "2025-07-29T04:58:53.682308"}, {"name": "DEPLOYMENT_ANALYSIS_GUIDE.md", "size_kb": 7.3115234375, "extension": ".md", "modified": "2025-07-29T16:23:27.670511"}, {"name": "DEPLOYMENT_ANALYSIS_README.md", "size_kb": 7.9287109375, "extension": ".md", "modified": "2025-07-29T16:29:38.683285"}, {"name": "DEPLOYMENT_ANALYSIS_SUMMARY.md", "size_kb": 8.7421875, "extension": ".md", "modified": "2025-07-29T16:31:10.198414"}, {"name": "DEPLOYMENT_COMMANDS.py", "size_kb": 18.3203125, "extension": ".py", "modified": "2025-07-29T16:22:35.216542"}, {"name": "DEPLOYMENT_COMPLETE_SUMMARY.md", "size_kb": 3.822265625, "extension": ".md", "modified": "2025-07-29T15:38:16.030684"}, {"name": "deployment_consultation_report_20250728_045905.json", "size_kb": 0.4326171875, "extension": ".json", "modified": "2025-07-28T04:59:05.557971"}, {"name": "DEPLOYMENT_GUIDE.md", "size_kb": 1.3447265625, "extension": ".md", "modified": "2025-07-31T05:32:48.453663"}, {"name": "DEPLOYMENT_IN_PROGRESS.md", "size_kb": 4.5927734375, "extension": ".md", "modified": "2025-07-30T03:03:24.460077"}, {"name": "deployment_report_20250730_054437.md", "size_kb": 1.6259765625, "extension": ".md", "modified": "2025-07-30T05:44:51.186772"}, {"name": "DEPLOYMENT_REQUIREMENTS_SUMMARY.md", "size_kb": 7.2958984375, "extension": ".md", "modified": "2025-07-29T05:03:05.217977"}, {"name": "DEPLOYMENT_STATUS_FINAL.md", "size_kb": 3.33203125, "extension": ".md", "modified": "2025-07-30T02:33:24.153298"}, {"name": "DEPLOYMENT_STATUS_REPORT.md", "size_kb": 4.2822265625, "extension": ".md", "modified": "2025-07-29T15:36:39.027715"}, {"name": "DEPLOYMENT_SUCCESS_FINAL.md", "size_kb": 7.5009765625, "extension": ".md", "modified": "2025-07-30T03:48:18.919929"}, {"name": "DEPLOYMENT_SYSTEM_COMPLETE.md", "size_kb": 8.5166015625, "extension": ".md", "modified": "2025-07-30T01:28:50.600472"}, {"name": "deploy_actual_projects.py", "size_kb": 8.1962890625, "extension": ".py", "modified": "2025-07-29T15:47:01.359291"}, {"name": "deploy_after_billing.py", "size_kb": 10.0234375, "extension": ".py", "modified": "2025-07-30T02:31:29.251262"}, {"name": "deploy_anubis_to_gcp.py", "size_kb": 13.66015625, "extension": ".py", "modified": "2025-07-29T14:22:58.162785"}, {"name": "deploy_commands.bat", "size_kb": 1.1171875, "extension": ".bat", "modified": "2025-07-30T02:05:26.066118"}, {"name": "deploy_github_to_gcloud.py", "size_kb": 12.0849609375, "extension": ".py", "modified": "2025-07-30T02:09:53.834331"}, {"name": "DEPLOY_HYBRID_APPROACH.py", "size_kb": 18.20703125, "extension": ".py", "modified": "2025-07-29T06:36:01.121867"}, {"name": "deploy_models_and_n8n.py", "size_kb": 10.859375, "extension": ".py", "modified": "2025-07-30T05:35:53.492496"}, {"name": "deploy_to_gcloud.ps1", "size_kb": 1.4462890625, "extension": ".ps1", "modified": "2025-07-30T02:09:20.469377"}, {"name": "DEPLOY_TO_GITHUB.py", "size_kb": 12.5224609375, "extension": ".py", "modified": "2025-07-29T04:55:02.669491"}, {"name": "DEPLOY_TO_GOOGLE_CLOUD.md", "size_kb": 7.6513671875, "extension": ".md", "modified": "2025-07-30T01:48:38.404522"}, {"name": "DEPLOY_TO_GOOGLE_CLOUD.py", "size_kb": 13.1572265625, "extension": ".py", "modified": "2025-07-29T06:32:16.021056"}, {"name": "DEVELOPMENT_RULES.md", "size_kb": 9.2021484375, "extension": ".md", "modified": "2025-07-29T05:45:24.268395"}, {"name": "direct_vm_setup.sh", "size_kb": 6.890625, "extension": ".sh", "modified": "2025-07-29T15:50:19.545151"}, {"name": "docker-compose-projects.yml", "size_kb": 6.11328125, "extension": ".yml", "modified": "2025-07-29T15:49:34.258226"}, {"name": "docker-compose.n8n.yml", "size_kb": 0.779296875, "extension": ".yml", "modified": "2025-07-30T05:36:12.463365"}, {"name": "docker-compose.yml", "size_kb": 4.408203125, "extension": ".yml", "modified": "2025-07-29T16:09:10.198495"}, {"name": "Dockerfile.n8n", "size_kb": 0.6328125, "extension": ".n8n", "modified": "2025-07-30T05:36:12.461858"}, {"name": "Dockerfile.production", "size_kb": 1.505859375, "extension": ".production", "modified": "2025-07-29T14:13:58.085415"}, {"name": "ENABLE_BILLING_AND_DEPLOY.md", "size_kb": 4.8203125, "extension": ".md", "modified": "2025-07-30T02:30:22.210692"}, {"name": "enhanced_models_uploader.py", "size_kb": 15.9873046875, "extension": ".py", "modified": "2025-07-30T05:53:16.917824"}, {"name": "FINAL_CLOUD_STATUS_REPORT.md", "size_kb": 8.087890625, "extension": ".md", "modified": "2025-07-31T06:30:32.261380"}, {"name": "FINAL_COMPLETE_SYSTEM_TEST.py", "size_kb": 12.2236328125, "extension": ".py", "modified": "2025-07-29T04:50:04.967793"}, {"name": "final_complete_system_test_20250729_045015.json", "size_kb": 2.412109375, "extension": ".json", "modified": "2025-07-29T04:50:15.216259"}, {"name": "FINAL_DEPLOYMENT_SUCCESS_REPORT.md", "size_kb": 7.7119140625, "extension": ".md", "modified": "2025-07-30T05:46:52.824688"}, {"name": "FINAL_DEPLOYMENT_SUMMARY.md", "size_kb": 5.17578125, "extension": ".md", "modified": "2025-07-29T15:44:08.453369"}, {"name": "FINAL_GITHUB_SOLUTION.md", "size_kb": 5.509765625, "extension": ".md", "modified": "2025-07-29T05:59:51.359635"}, {"name": "FINAL_MODELS_STORAGE_VERIFICATION_REPORT.md", "size_kb": 6.94921875, "extension": ".md", "modified": "2025-07-30T05:29:33.697375"}, {"name": "FINAL_OPERATIONS_COMPLETION_REPORT.md", "size_kb": 8.94921875, "extension": ".md", "modified": "2025-07-31T05:33:54.727068"}, {"name": "FINAL_PUSH_AFTER_SECRET_REMOVAL.py", "size_kb": 8.0947265625, "extension": ".py", "modified": "2025-07-29T06:20:02.875654"}, {"name": "FINAL_QUICK_START.py", "size_kb": 12.5810546875, "extension": ".py", "modified": "2025-07-31T05:34:56.104768"}, {"name": "FINAL_UPLOAD_SOLUTION.md", "size_kb": 4.412109375, "extension": ".md", "modified": "2025-07-29T06:14:51.515457"}, {"name": "fix_cloud_deployment.py", "size_kb": 11.939453125, "extension": ".py", "modified": "2025-07-31T06:06:55.183194"}, {"name": "FIX_NUMPY_ISSUE.py", "size_kb": 2.7958984375, "extension": ".py", "modified": "2025-07-29T12:43:37.922342"}, {"name": "GEMINI_PROJECT_ANALYSIS_REQUEST.md", "size_kb": 5.6123046875, "extension": ".md", "modified": "2025-07-31T06:00:20.110225"}, {"name": "github_cloud_deployment_plan.py", "size_kb": 8.6318359375, "extension": ".py", "modified": "2025-07-29T05:45:24.279656"}, {"name": "GITHUB_DEPLOYMENT_GUIDE.md", "size_kb": 8.404296875, "extension": ".md", "modified": "2025-07-29T04:52:30.912541"}, {"name": "GITHUB_TOKEN_STEP_BY_STEP.md", "size_kb": 4.1826171875, "extension": ".md", "modified": "2025-07-29T06:07:12.122336"}, {"name": "GITHUB_UPLOAD_FINAL_STATUS.md", "size_kb": 7.451171875, "extension": ".md", "modified": "2025-07-29T05:52:57.826225"}, {"name": "GOOGLE_CLOUD_DEPLOYMENT_PLAN.md", "size_kb": 5.4189453125, "extension": ".md", "modified": "2025-07-29T06:31:15.526148"}, {"name": "GOOGLE_CLOUD_SERVICES_STATUS.md", "size_kb": 8.7724609375, "extension": ".md", "modified": "2025-07-31T05:51:14.823156"}, {"name": "GOOGLE_CLOUD_SETUP.py", "size_kb": 13.1416015625, "extension": ".py", "modified": "2025-07-29T04:56:09.135650"}, {"name": "GOOGLE_CLOUD_TOOLS_REQUIRED.md", "size_kb": 6.5458984375, "extension": ".md", "modified": "2025-07-29T06:43:21.070650"}, {"name": "HORUS_AI_TEAM.tar.gz", "size_kb": 1514.58203125, "extension": ".gz", "modified": "2025-07-29T15:47:44.451658"}, {"name": "HORUS_PROJECT_ANALYZER.py", "size_kb": 17.**********, "extension": ".py", "modified": "2025-07-31T06:40:11.947429"}, {"name": "INTEGRATE_ALL_PROJECTS.py", "size_kb": 10.732421875, "extension": ".py", "modified": "2025-07-27T15:14:33.500710"}, {"name": "LAUNCH_ANUBIS_COMPLETE.py", "size_kb": 8.669921875, "extension": ".py", "modified": "2025-07-27T15:13:36.115490"}, {"name": "LICENSE", "size_kb": 1.**********, "extension": "", "modified": "2025-07-26T11:00:09.601059"}, {"name": "models_metadata.json", "size_kb": 0.0, "extension": ".json", "modified": "2025-07-30T05:11:46.340728"}, {"name": "models_storage_test_report.md", "size_kb": 5.908203125, "extension": ".md", "modified": "2025-07-30T05:27:18.614240"}, {"name": "models_storage_verification.py", "size_kb": 18.**********, "extension": ".py", "modified": "2025-07-30T05:17:04.768061"}, {"name": "models_verification_report_1f3a1b34-73f7-4cd0-b273-04aa1af82775.json", "size_kb": 20.**********, "extension": ".json", "modified": "2025-07-30T05:44:36.661420"}, {"name": "MULTI_AI_ASSISTANTS_MANAGER.py", "size_kb": 18.**********, "extension": ".py", "modified": "2025-07-30T01:38:19.771614"}, {"name": "n8n_cloud_setup.py", "size_kb": 14.22265625, "extension": ".py", "modified": "2025-07-30T05:34:20.199918"}, {"name": "N8N_PROFESSIONAL_DASHBOARD.md", "size_kb": 9.982421875, "extension": ".md", "modified": "2025-07-29T12:48:07.254602"}, {"name": "n8n_setup_log_20250730_053616.txt", "size_kb": 1.5771484375, "extension": ".txt", "modified": "2025-07-30T05:36:16.162986"}, {"name": "nginx.conf", "size_kb": 1.**********, "extension": ".conf", "modified": "2025-07-29T15:49:34.258761"}, {"name": "nginx_anubis.conf", "size_kb": 1.**********, "extension": ".conf", "modified": "2025-07-29T15:48:43.059419"}, {"name": "notification_channel.json", "size_kb": 0.1865234375, "extension": ".json", "modified": "2025-07-29T15:42:22.229426"}, {"name": "OLLAMA_CLOUD_MIGRATION_SYSTEM.py", "size_kb": 14.8662109375, "extension": ".py", "modified": "2025-07-30T01:32:45.184741"}, {"name": "OLLAMA_CLOUD_PROJECT_MASTER_PLAN.md", "size_kb": 9.3603515625, "extension": ".md", "modified": "2025-07-30T01:39:48.563057"}, {"name": "OPERATIONS_COMPLETION_SUMMARY.md", "size_kb": 7.8037109375, "extension": ".md", "modified": "2025-07-31T05:35:57.336356"}, {"name": "phase2_results_20250731_053248.json", "size_kb": 0.42578125, "extension": ".json", "modified": "2025-07-31T05:32:48.456861"}, {"name": "PROJECTS_DEPLOYMENT_SOLUTION.md", "size_kb": 9.9052734375, "extension": ".md", "modified": "2025-07-29T15:51:36.206691"}, {"name": "PROJECT_DEPLOYMENT_ANALYSIS.py", "size_kb": 18.94140625, "extension": ".py", "modified": "2025-07-29T16:21:22.725027"}, {"name": "PROJECT_PATHS_DIRECTORY.md", "size_kb": 22.4599609375, "extension": ".md", "modified": "2025-07-27T15:22:06.174541"}, {"name": "PROJECT_STRUCTURE_DETAILED.md", "size_kb": 13.810546875, "extension": ".md", "modified": "2025-07-27T15:20:56.294589"}, {"name": "PROJECT_STRUCTURE_UPDATED.md", "size_kb": 0.7783203125, "extension": ".md", "modified": "2025-07-31T05:32:48.452606"}, {"name": "quick_deploy.sh", "size_kb": 0.650390625, "extension": ".sh", "modified": "2025-07-29T16:12:22.541151"}, {"name": "QUICK_DEPLOYMENT_COMMANDS.md", "size_kb": 5.9833984375, "extension": ".md", "modified": "2025-07-29T16:28:36.013184"}, {"name": "QUICK_DEPLOY_GUIDE.md", "size_kb": 3.0615234375, "extension": ".md", "modified": "2025-07-30T01:55:48.168178"}, {"name": "quick_deploy_projects.py", "size_kb": 11.4072265625, "extension": ".py", "modified": "2025-07-29T15:49:23.583620"}, {"name": "quick_setup.sh", "size_kb": 0.2841796875, "extension": ".sh", "modified": "2025-07-29T16:06:26.122284"}, {"name": "QUICK_START.py", "size_kb": 5.52734375, "extension": ".py", "modified": "2025-07-27T15:15:10.897826"}, {"name": "QUICK_TEST.py", "size_kb": 9.4423828125, "extension": ".py", "modified": "2025-07-30T01:27:35.656542"}, {"name": "quick_test_report_20250730_012745.json", "size_kb": 1.244140625, "extension": ".json", "modified": "2025-07-30T01:27:45.298282"}, {"name": "QUICK_UPLOAD_INSTRUCTIONS.md", "size_kb": 1.794921875, "extension": ".md", "modified": "2025-07-29T06:08:03.427400"}, {"name": "qwen_deployment_consultant.py", "size_kb": 6.6083984375, "extension": ".py", "modified": "2025-07-29T14:09:10.186779"}, {"name": "qwen_test_report_20250728_044916.json", "size_kb": 32.8779296875, "extension": ".json", "modified": "2025-07-28T04:49:16.934803"}, {"name": "README.md", "size_kb": 3.6474609375, "extension": ".md", "modified": "2025-07-31T05:32:48.451554"}, {"name": "remaining_operations_results_20250731_053102.json", "size_kb": 3.830078125, "extension": ".json", "modified": "2025-07-31T05:31:11.339194"}, {"name": "REMOVE_ALL_SECRETS.py", "size_kb": 15.50390625, "extension": ".py", "modified": "2025-07-29T05:45:24.316322"}, {"name": "RUN_DEPLOYMENT_ANALYSIS.bat", "size_kb": 2.865234375, "extension": ".bat", "modified": "2025-07-29T16:27:11.617942"}, {"name": "RUN_DEPLOYMENT_ANALYSIS.ps1", "size_kb": 5.1669921875, "extension": ".ps1", "modified": "2025-07-29T16:27:41.090732"}, {"name": "RUN_DEPLOYMENT_ANALYSIS.sh", "size_kb": 6.2626953125, "extension": ".sh", "modified": "2025-07-30T01:22:37.535959"}, {"name": "SECRETS_FOUND_REPORT.md", "size_kb": 6.4150390625, "extension": ".md", "modified": "2025-07-29T05:45:24.322300"}, {"name": "secrets_removal_report_20250729_054540.md", "size_kb": 6.1904296875, "extension": ".md", "modified": "2025-07-29T05:45:40.211516"}, {"name": "secret_hunt_report_20250729_053916.json", "size_kb": 602.6474609375, "extension": ".json", "modified": "2025-07-29T05:45:24.466947"}, {"name": "SERVICES_TEST_AND_MONITORING_REPORT.md", "size_kb": 4.951171875, "extension": ".md", "modified": "2025-07-29T15:43:15.017878"}, {"name": "services_test_results.json", "size_kb": 0.310546875, "extension": ".json", "modified": "2025-07-29T15:41:12.417136"}, {"name": "setup_anubis_with_qwen.py", "size_kb": 17.2333984375, "extension": ".py", "modified": "2025-07-29T16:06:28.728746"}, {"name": "setup_gemini_api.py", "size_kb": 0.9970703125, "extension": ".py", "modified": "2025-07-30T01:36:06.138771"}, {"name": "SETUP_GITHUB_TOKEN_AND_UPLOAD.py", "size_kb": 12.224609375, "extension": ".py", "modified": "2025-07-29T06:02:46.951521"}, {"name": "setup_monitoring.py", "size_kb": 10.109375, "extension": ".py", "modified": "2025-07-29T15:42:07.918342"}, {"name": "SIMPLE_CLOUD_DASHBOARD.py", "size_kb": 16.7041015625, "extension": ".py", "modified": "2025-07-29T12:41:34.200014"}, {"name": "SIMPLE_GITHUB_UPLOAD.py", "size_kb": 4.51171875, "extension": ".py", "modified": "2025-07-29T06:07:42.078873"}, {"name": "startup-script.sh", "size_kb": 1.4453125, "extension": ".sh", "modified": "2025-07-29T14:28:11.427684"}, {"name": "start_anubis_with_local_mysql.py", "size_kb": 11.8076171875, "extension": ".py", "modified": "2025-07-29T05:45:24.466947"}, {"name": "start_complete_anubis_system.py", "size_kb": 14.369140625, "extension": ".py", "modified": "2025-07-29T05:45:24.477365"}, {"name": "START_DASHBOARD.py", "size_kb": 5.52734375, "extension": ".py", "modified": "2025-07-29T12:25:44.396457"}, {"name": "START_PROFESSIONAL_DASHBOARD.py", "size_kb": 14.2646484375, "extension": ".py", "modified": "2025-07-29T12:53:27.637012"}, {"name": "task_assignment_report_20250730_013505.json", "size_kb": 8.1826171875, "extension": ".json", "modified": "2025-07-30T01:35:05.699043"}, {"name": "test_all_services.py", "size_kb": 7.091796875, "extension": ".py", "modified": "2025-07-29T15:40:33.719902"}, {"name": "test_deployment.py", "size_kb": 1.9580078125, "extension": ".py", "modified": "2025-07-30T04:35:16.551631"}, {"name": "upload_models_to_gcloud.py", "size_kb": 7.1064453125, "extension": ".py", "modified": "2025-07-30T04:59:49.618725"}, {"name": "UPLOAD_TO_AGENT_REPO.py", "size_kb": 13.087890625, "extension": ".py", "modified": "2025-07-29T06:18:29.648698"}, {"name": "UPLOAD_TO_EXISTING_REPO.py", "size_kb": 8.1943359375, "extension": ".py", "modified": "2025-07-29T05:58:25.316051"}, {"name": "UPLOAD_WITH_PROVIDED_TOKEN.py", "size_kb": 10.30859375, "extension": ".py", "modified": "2025-07-29T06:12:46.383144"}, {"name": "USER_GUIDE.md", "size_kb": 1.298828125, "extension": ".md", "modified": "2025-07-31T05:32:48.455799"}, {"name": "vm_alert_policy.json", "size_kb": 0.689453125, "extension": ".json", "modified": "2025-07-29T15:42:25.277763"}], "total_files": 145, "total_directories": 9}}, "file_organization": {"categorized": {"documentation": [{"name": "ANUBIS_PROFESSIONAL_DASHBOARD_GUIDE.md", "size_kb": 9.1142578125, "extension": ".md", "modified": "2025-07-29T12:56:52.556616", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "API_DOCUMENTATION.md", "size_kb": 0.796875, "extension": ".md", "modified": "2025-07-31T05:32:48.454744", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "CLOUD_DEPLOYMENT_ISSUES_ANALYSIS.md", "size_kb": 8.6572265625, "extension": ".md", "modified": "2025-07-31T06:05:16.991086", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "cloud_fix_report.json", "size_kb": 0.4501953125, "extension": ".json", "modified": "2025-07-31T06:27:40.205839", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Keyword match"}, {"name": "CLOUD_MODELS_GUIDE.md", "size_kb": 6.0830078125, "extension": ".md", "modified": "2025-07-30T04:52:31.153732", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "dashboard_requirements.txt", "size_kb": 0.689453125, "extension": ".txt", "modified": "2025-07-29T12:29:52.691721", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .txt"}, {"name": "DASHBOARD_USER_GUIDE.md", "size_kb": 7.828125, "extension": ".md", "modified": "2025-07-29T12:30:52.741510", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "DEPLOYMENT_ANALYSIS_GUIDE.md", "size_kb": 7.3115234375, "extension": ".md", "modified": "2025-07-29T16:23:27.670511", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "DEPLOYMENT_ANALYSIS_README.md", "size_kb": 7.9287109375, "extension": ".md", "modified": "2025-07-29T16:29:38.683285", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "DEPLOYMENT_ANALYSIS_SUMMARY.md", "size_kb": 8.7421875, "extension": ".md", "modified": "2025-07-29T16:31:10.198414", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "DEPLOYMENT_COMPLETE_SUMMARY.md", "size_kb": 3.822265625, "extension": ".md", "modified": "2025-07-29T15:38:16.030684", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "deployment_consultation_report_20250728_045905.json", "size_kb": 0.4326171875, "extension": ".json", "modified": "2025-07-28T04:59:05.557971", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Keyword match"}, {"name": "DEPLOYMENT_GUIDE.md", "size_kb": 1.3447265625, "extension": ".md", "modified": "2025-07-31T05:32:48.453663", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "DEPLOYMENT_IN_PROGRESS.md", "size_kb": 4.5927734375, "extension": ".md", "modified": "2025-07-30T03:03:24.460077", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "deployment_report_20250730_054437.md", "size_kb": 1.6259765625, "extension": ".md", "modified": "2025-07-30T05:44:51.186772", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "DEPLOYMENT_REQUIREMENTS_SUMMARY.md", "size_kb": 7.2958984375, "extension": ".md", "modified": "2025-07-29T05:03:05.217977", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "DEPLOYMENT_STATUS_FINAL.md", "size_kb": 3.33203125, "extension": ".md", "modified": "2025-07-30T02:33:24.153298", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "DEPLOYMENT_STATUS_REPORT.md", "size_kb": 4.2822265625, "extension": ".md", "modified": "2025-07-29T15:36:39.027715", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "DEPLOYMENT_SUCCESS_FINAL.md", "size_kb": 7.5009765625, "extension": ".md", "modified": "2025-07-30T03:48:18.919929", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "DEPLOYMENT_SYSTEM_COMPLETE.md", "size_kb": 8.5166015625, "extension": ".md", "modified": "2025-07-30T01:28:50.600472", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "DEPLOY_TO_GOOGLE_CLOUD.md", "size_kb": 7.6513671875, "extension": ".md", "modified": "2025-07-30T01:48:38.404522", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "DEVELOPMENT_RULES.md", "size_kb": 9.2021484375, "extension": ".md", "modified": "2025-07-29T05:45:24.268395", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "ENABLE_BILLING_AND_DEPLOY.md", "size_kb": 4.8203125, "extension": ".md", "modified": "2025-07-30T02:30:22.210692", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "FINAL_CLOUD_STATUS_REPORT.md", "size_kb": 8.087890625, "extension": ".md", "modified": "2025-07-31T06:30:32.261380", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "FINAL_DEPLOYMENT_SUCCESS_REPORT.md", "size_kb": 7.7119140625, "extension": ".md", "modified": "2025-07-30T05:46:52.824688", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "FINAL_DEPLOYMENT_SUMMARY.md", "size_kb": 5.17578125, "extension": ".md", "modified": "2025-07-29T15:44:08.453369", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "FINAL_GITHUB_SOLUTION.md", "size_kb": 5.509765625, "extension": ".md", "modified": "2025-07-29T05:59:51.359635", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "FINAL_MODELS_STORAGE_VERIFICATION_REPORT.md", "size_kb": 6.94921875, "extension": ".md", "modified": "2025-07-30T05:29:33.697375", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "FINAL_OPERATIONS_COMPLETION_REPORT.md", "size_kb": 8.94921875, "extension": ".md", "modified": "2025-07-31T05:33:54.727068", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "FINAL_UPLOAD_SOLUTION.md", "size_kb": 4.412109375, "extension": ".md", "modified": "2025-07-29T06:14:51.515457", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "GEMINI_PROJECT_ANALYSIS_REQUEST.md", "size_kb": 5.6123046875, "extension": ".md", "modified": "2025-07-31T06:00:20.110225", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "GITHUB_DEPLOYMENT_GUIDE.md", "size_kb": 8.404296875, "extension": ".md", "modified": "2025-07-29T04:52:30.912541", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "GITHUB_TOKEN_STEP_BY_STEP.md", "size_kb": 4.1826171875, "extension": ".md", "modified": "2025-07-29T06:07:12.122336", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "GITHUB_UPLOAD_FINAL_STATUS.md", "size_kb": 7.451171875, "extension": ".md", "modified": "2025-07-29T05:52:57.826225", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "GOOGLE_CLOUD_DEPLOYMENT_PLAN.md", "size_kb": 5.4189453125, "extension": ".md", "modified": "2025-07-29T06:31:15.526148", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "GOOGLE_CLOUD_SERVICES_STATUS.md", "size_kb": 8.7724609375, "extension": ".md", "modified": "2025-07-31T05:51:14.823156", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "GOOGLE_CLOUD_TOOLS_REQUIRED.md", "size_kb": 6.5458984375, "extension": ".md", "modified": "2025-07-29T06:43:21.070650", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "models_storage_test_report.md", "size_kb": 5.908203125, "extension": ".md", "modified": "2025-07-30T05:27:18.614240", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "models_verification_report_1f3a1b34-73f7-4cd0-b273-04aa1af82775.json", "size_kb": 20.**********, "extension": ".json", "modified": "2025-07-30T05:44:36.661420", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Keyword match"}, {"name": "N8N_PROFESSIONAL_DASHBOARD.md", "size_kb": 9.982421875, "extension": ".md", "modified": "2025-07-29T12:48:07.254602", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "n8n_setup_log_20250730_053616.txt", "size_kb": 1.5771484375, "extension": ".txt", "modified": "2025-07-30T05:36:16.162986", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .txt"}, {"name": "OLLAMA_CLOUD_PROJECT_MASTER_PLAN.md", "size_kb": 9.3603515625, "extension": ".md", "modified": "2025-07-30T01:39:48.563057", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "OPERATIONS_COMPLETION_SUMMARY.md", "size_kb": 7.8037109375, "extension": ".md", "modified": "2025-07-31T05:35:57.336356", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "PROJECTS_DEPLOYMENT_SOLUTION.md", "size_kb": 9.9052734375, "extension": ".md", "modified": "2025-07-29T15:51:36.206691", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "PROJECT_DEPLOYMENT_ANALYSIS.py", "size_kb": 18.94140625, "extension": ".py", "modified": "2025-07-29T16:21:22.725027", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Keyword match"}, {"name": "PROJECT_PATHS_DIRECTORY.md", "size_kb": 22.4599609375, "extension": ".md", "modified": "2025-07-27T15:22:06.174541", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "PROJECT_STRUCTURE_DETAILED.md", "size_kb": 13.810546875, "extension": ".md", "modified": "2025-07-27T15:20:56.294589", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "PROJECT_STRUCTURE_UPDATED.md", "size_kb": 0.7783203125, "extension": ".md", "modified": "2025-07-31T05:32:48.452606", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "QUICK_DEPLOYMENT_COMMANDS.md", "size_kb": 5.9833984375, "extension": ".md", "modified": "2025-07-29T16:28:36.013184", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "QUICK_DEPLOY_GUIDE.md", "size_kb": 3.0615234375, "extension": ".md", "modified": "2025-07-30T01:55:48.168178", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "quick_test_report_20250730_012745.json", "size_kb": 1.244140625, "extension": ".json", "modified": "2025-07-30T01:27:45.298282", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Keyword match"}, {"name": "QUICK_UPLOAD_INSTRUCTIONS.md", "size_kb": 1.794921875, "extension": ".md", "modified": "2025-07-29T06:08:03.427400", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "qwen_test_report_20250728_044916.json", "size_kb": 32.8779296875, "extension": ".json", "modified": "2025-07-28T04:49:16.934803", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Keyword match"}, {"name": "README.md", "size_kb": 3.6474609375, "extension": ".md", "modified": "2025-07-31T05:32:48.451554", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "RUN_DEPLOYMENT_ANALYSIS.bat", "size_kb": 2.865234375, "extension": ".bat", "modified": "2025-07-29T16:27:11.617942", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Keyword match"}, {"name": "RUN_DEPLOYMENT_ANALYSIS.ps1", "size_kb": 5.1669921875, "extension": ".ps1", "modified": "2025-07-29T16:27:41.090732", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Keyword match"}, {"name": "RUN_DEPLOYMENT_ANALYSIS.sh", "size_kb": 6.2626953125, "extension": ".sh", "modified": "2025-07-30T01:22:37.535959", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Keyword match"}, {"name": "SECRETS_FOUND_REPORT.md", "size_kb": 6.4150390625, "extension": ".md", "modified": "2025-07-29T05:45:24.322300", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "secrets_removal_report_20250729_054540.md", "size_kb": 6.1904296875, "extension": ".md", "modified": "2025-07-29T05:45:40.211516", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "secret_hunt_report_20250729_053916.json", "size_kb": 602.6474609375, "extension": ".json", "modified": "2025-07-29T05:45:24.466947", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Keyword match"}, {"name": "SERVICES_TEST_AND_MONITORING_REPORT.md", "size_kb": 4.951171875, "extension": ".md", "modified": "2025-07-29T15:43:15.017878", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}, {"name": "task_assignment_report_20250730_013505.json", "size_kb": 8.1826171875, "extension": ".json", "modified": "2025-07-30T01:35:05.699043", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Keyword match"}, {"name": "USER_GUIDE.md", "size_kb": 1.298828125, "extension": ".md", "modified": "2025-07-31T05:32:48.455799", "target_folder": "PROJECT_DOCUMENTATION", "reason": "Extension: .md"}], "scripts": [{"name": "AI_ASSISTANTS_TASK_DISTRIBUTOR.py", "size_kb": 17.6650390625, "extension": ".py", "modified": "2025-07-30T01:34:05.098048", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "ANUBIS_CLOUD_DASHBOARD.py", "size_kb": 19.927734375, "extension": ".py", "modified": "2025-07-29T12:25:01.674840", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "cloud_models_manager.py", "size_kb": 9.541015625, "extension": ".py", "modified": "2025-07-30T05:00:05.316467", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "COLLABORATIVE_SECRET_HUNTER.py", "size_kb": 14.2998046875, "extension": ".py", "modified": "2025-07-29T05:37:54.990657", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "COMPLETE_REMAINING_OPERATIONS.py", "size_kb": 13.5966796875, "extension": ".py", "modified": "2025-07-31T05:30:54.664186", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "COMPLETE_REMAINING_OPERATIONS_PHASE2.py", "size_kb": 13.77734375, "extension": ".py", "modified": "2025-07-31T05:32:39.293771", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "CREATE_FRESH_REPO.py", "size_kb": 12.7958984375, "extension": ".py", "modified": "2025-07-29T06:21:45.546466", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "CREATE_NEW_REPO_AND_UPLOAD.py", "size_kb": 19.4306640625, "extension": ".py", "modified": "2025-07-29T05:57:30.876343", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "deploy-to-github.ps1", "size_kb": 4.611328125, "extension": ".ps1", "modified": "2025-07-29T04:58:53.682308", "target_folder": "scripts", "reason": "Extension: .ps1"}, {"name": "DEPLOYMENT_COMMANDS.py", "size_kb": 18.3203125, "extension": ".py", "modified": "2025-07-29T16:22:35.216542", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "deploy_actual_projects.py", "size_kb": 8.1962890625, "extension": ".py", "modified": "2025-07-29T15:47:01.359291", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "deploy_after_billing.py", "size_kb": 10.0234375, "extension": ".py", "modified": "2025-07-30T02:31:29.251262", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "deploy_anubis_to_gcp.py", "size_kb": 13.66015625, "extension": ".py", "modified": "2025-07-29T14:22:58.162785", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "deploy_commands.bat", "size_kb": 1.1171875, "extension": ".bat", "modified": "2025-07-30T02:05:26.066118", "target_folder": "scripts", "reason": "Extension: .bat"}, {"name": "deploy_github_to_gcloud.py", "size_kb": 12.0849609375, "extension": ".py", "modified": "2025-07-30T02:09:53.834331", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "DEPLOY_HYBRID_APPROACH.py", "size_kb": 18.20703125, "extension": ".py", "modified": "2025-07-29T06:36:01.121867", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "deploy_models_and_n8n.py", "size_kb": 10.859375, "extension": ".py", "modified": "2025-07-30T05:35:53.492496", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "deploy_to_gcloud.ps1", "size_kb": 1.4462890625, "extension": ".ps1", "modified": "2025-07-30T02:09:20.469377", "target_folder": "scripts", "reason": "Extension: .ps1"}, {"name": "DEPLOY_TO_GITHUB.py", "size_kb": 12.5224609375, "extension": ".py", "modified": "2025-07-29T04:55:02.669491", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "DEPLOY_TO_GOOGLE_CLOUD.py", "size_kb": 13.1572265625, "extension": ".py", "modified": "2025-07-29T06:32:16.021056", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "direct_vm_setup.sh", "size_kb": 6.890625, "extension": ".sh", "modified": "2025-07-29T15:50:19.545151", "target_folder": "scripts", "reason": "Extension: .sh"}, {"name": "enhanced_models_uploader.py", "size_kb": 15.9873046875, "extension": ".py", "modified": "2025-07-30T05:53:16.917824", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "FINAL_COMPLETE_SYSTEM_TEST.py", "size_kb": 12.2236328125, "extension": ".py", "modified": "2025-07-29T04:50:04.967793", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "final_complete_system_test_20250729_045015.json", "size_kb": 2.412109375, "extension": ".json", "modified": "2025-07-29T04:50:15.216259", "target_folder": "scripts", "reason": "Keyword match"}, {"name": "FINAL_PUSH_AFTER_SECRET_REMOVAL.py", "size_kb": 8.0947265625, "extension": ".py", "modified": "2025-07-29T06:20:02.875654", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "FINAL_QUICK_START.py", "size_kb": 12.5810546875, "extension": ".py", "modified": "2025-07-31T05:34:56.104768", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "fix_cloud_deployment.py", "size_kb": 11.939453125, "extension": ".py", "modified": "2025-07-31T06:06:55.183194", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "FIX_NUMPY_ISSUE.py", "size_kb": 2.7958984375, "extension": ".py", "modified": "2025-07-29T12:43:37.922342", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "github_cloud_deployment_plan.py", "size_kb": 8.6318359375, "extension": ".py", "modified": "2025-07-29T05:45:24.279656", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "GOOGLE_CLOUD_SETUP.py", "size_kb": 13.1416015625, "extension": ".py", "modified": "2025-07-29T04:56:09.135650", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "HORUS_PROJECT_ANALYZER.py", "size_kb": 17.**********, "extension": ".py", "modified": "2025-07-31T06:40:11.947429", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "INTEGRATE_ALL_PROJECTS.py", "size_kb": 10.732421875, "extension": ".py", "modified": "2025-07-27T15:14:33.500710", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "LAUNCH_ANUBIS_COMPLETE.py", "size_kb": 8.669921875, "extension": ".py", "modified": "2025-07-27T15:13:36.115490", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "models_storage_verification.py", "size_kb": 18.**********, "extension": ".py", "modified": "2025-07-30T05:17:04.768061", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "MULTI_AI_ASSISTANTS_MANAGER.py", "size_kb": 18.**********, "extension": ".py", "modified": "2025-07-30T01:38:19.771614", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "n8n_cloud_setup.py", "size_kb": 14.22265625, "extension": ".py", "modified": "2025-07-30T05:34:20.199918", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "OLLAMA_CLOUD_MIGRATION_SYSTEM.py", "size_kb": 14.8662109375, "extension": ".py", "modified": "2025-07-30T01:32:45.184741", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "quick_deploy.sh", "size_kb": 0.650390625, "extension": ".sh", "modified": "2025-07-29T16:12:22.541151", "target_folder": "scripts", "reason": "Extension: .sh"}, {"name": "quick_deploy_projects.py", "size_kb": 11.4072265625, "extension": ".py", "modified": "2025-07-29T15:49:23.583620", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "quick_setup.sh", "size_kb": 0.2841796875, "extension": ".sh", "modified": "2025-07-29T16:06:26.122284", "target_folder": "scripts", "reason": "Extension: .sh"}, {"name": "QUICK_START.py", "size_kb": 5.52734375, "extension": ".py", "modified": "2025-07-27T15:15:10.897826", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "QUICK_TEST.py", "size_kb": 9.4423828125, "extension": ".py", "modified": "2025-07-30T01:27:35.656542", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "qwen_deployment_consultant.py", "size_kb": 6.6083984375, "extension": ".py", "modified": "2025-07-29T14:09:10.186779", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "REMOVE_ALL_SECRETS.py", "size_kb": 15.50390625, "extension": ".py", "modified": "2025-07-29T05:45:24.316322", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "services_test_results.json", "size_kb": 0.310546875, "extension": ".json", "modified": "2025-07-29T15:41:12.417136", "target_folder": "scripts", "reason": "Keyword match"}, {"name": "setup_anubis_with_qwen.py", "size_kb": 17.2333984375, "extension": ".py", "modified": "2025-07-29T16:06:28.728746", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "setup_gemini_api.py", "size_kb": 0.9970703125, "extension": ".py", "modified": "2025-07-30T01:36:06.138771", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "SETUP_GITHUB_TOKEN_AND_UPLOAD.py", "size_kb": 12.224609375, "extension": ".py", "modified": "2025-07-29T06:02:46.951521", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "setup_monitoring.py", "size_kb": 10.109375, "extension": ".py", "modified": "2025-07-29T15:42:07.918342", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "SIMPLE_CLOUD_DASHBOARD.py", "size_kb": 16.7041015625, "extension": ".py", "modified": "2025-07-29T12:41:34.200014", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "SIMPLE_GITHUB_UPLOAD.py", "size_kb": 4.51171875, "extension": ".py", "modified": "2025-07-29T06:07:42.078873", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "startup-script.sh", "size_kb": 1.4453125, "extension": ".sh", "modified": "2025-07-29T14:28:11.427684", "target_folder": "scripts", "reason": "Extension: .sh"}, {"name": "start_anubis_with_local_mysql.py", "size_kb": 11.8076171875, "extension": ".py", "modified": "2025-07-29T05:45:24.466947", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "start_complete_anubis_system.py", "size_kb": 14.369140625, "extension": ".py", "modified": "2025-07-29T05:45:24.477365", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "START_DASHBOARD.py", "size_kb": 5.52734375, "extension": ".py", "modified": "2025-07-29T12:25:44.396457", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "START_PROFESSIONAL_DASHBOARD.py", "size_kb": 14.2646484375, "extension": ".py", "modified": "2025-07-29T12:53:27.637012", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "test_all_services.py", "size_kb": 7.091796875, "extension": ".py", "modified": "2025-07-29T15:40:33.719902", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "test_deployment.py", "size_kb": 1.9580078125, "extension": ".py", "modified": "2025-07-30T04:35:16.551631", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "upload_models_to_gcloud.py", "size_kb": 7.1064453125, "extension": ".py", "modified": "2025-07-30T04:59:49.618725", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "UPLOAD_TO_AGENT_REPO.py", "size_kb": 13.087890625, "extension": ".py", "modified": "2025-07-29T06:18:29.648698", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "UPLOAD_TO_EXISTING_REPO.py", "size_kb": 8.1943359375, "extension": ".py", "modified": "2025-07-29T05:58:25.316051", "target_folder": "scripts", "reason": "Extension: .py"}, {"name": "UPLOAD_WITH_PROVIDED_TOKEN.py", "size_kb": 10.30859375, "extension": ".py", "modified": "2025-07-29T06:12:46.383144", "target_folder": "scripts", "reason": "Extension: .py"}], "configuration": [{"name": "app.n8n.yaml", "size_kb": 0.5146484375, "extension": ".yaml", "modified": "2025-07-30T05:36:12.463365", "target_folder": "SHARED_REQUIREMENTS/data", "reason": "Extension: .yaml"}, {"name": "cloudbuild.yaml", "size_kb": 11.6630859375, "extension": ".yaml", "modified": "2025-07-28T05:14:48.588665", "target_folder": "SHARED_REQUIREMENTS/data", "reason": "Extension: .yaml"}, {"name": "dashboard_config.json", "size_kb": 0.6748046875, "extension": ".json", "modified": "2025-07-29T15:42:31.396880", "target_folder": "SHARED_REQUIREMENTS/data", "reason": "Extension: .json"}, {"name": "db_alert_policy.json", "size_kb": 0.5263671875, "extension": ".json", "modified": "2025-07-29T15:42:28.333972", "target_folder": "SHARED_REQUIREMENTS/data", "reason": "Extension: .json"}, {"name": "docker-compose-projects.yml", "size_kb": 6.11328125, "extension": ".yml", "modified": "2025-07-29T15:49:34.258226", "target_folder": "SHARED_REQUIREMENTS/data", "reason": "Extension: .yml"}, {"name": "docker-compose.n8n.yml", "size_kb": 0.779296875, "extension": ".yml", "modified": "2025-07-30T05:36:12.463365", "target_folder": "SHARED_REQUIREMENTS/data", "reason": "Extension: .yml"}, {"name": "docker-compose.yml", "size_kb": 4.408203125, "extension": ".yml", "modified": "2025-07-29T16:09:10.198495", "target_folder": "SHARED_REQUIREMENTS/data", "reason": "Extension: .yml"}, {"name": "models_metadata.json", "size_kb": 0.0, "extension": ".json", "modified": "2025-07-30T05:11:46.340728", "target_folder": "SHARED_REQUIREMENTS/data", "reason": "Extension: .json"}, {"name": "notification_channel.json", "size_kb": 0.1865234375, "extension": ".json", "modified": "2025-07-29T15:42:22.229426", "target_folder": "SHARED_REQUIREMENTS/data", "reason": "Extension: .json"}, {"name": "phase2_results_20250731_053248.json", "size_kb": 0.42578125, "extension": ".json", "modified": "2025-07-31T05:32:48.456861", "target_folder": "SHARED_REQUIREMENTS/data", "reason": "Extension: .json"}, {"name": "remaining_operations_results_20250731_053102.json", "size_kb": 3.830078125, "extension": ".json", "modified": "2025-07-31T05:31:11.339194", "target_folder": "SHARED_REQUIREMENTS/data", "reason": "Extension: .json"}, {"name": "vm_alert_policy.json", "size_kb": 0.689453125, "extension": ".json", "modified": "2025-07-29T15:42:25.277763", "target_folder": "SHARED_REQUIREMENTS/data", "reason": "Extension: .json"}], "data": [], "docker": [{"name": "Dockerfile.n8n", "size_kb": 0.6328125, "extension": ".n8n", "modified": "2025-07-30T05:36:12.461858", "target_folder": "ANUBIS_SYSTEM/docker", "reason": "Keyword match"}, {"name": "Dockerfile.production", "size_kb": 1.505859375, "extension": ".production", "modified": "2025-07-29T14:13:58.085415", "target_folder": "ANUBIS_SYSTEM/docker", "reason": "Keyword match"}]}, "uncategorized": [{"name": "ANUBIS_HORUS_MCP.tar.gz", "size_kb": 1470.921875, "extension": ".gz", "modified": "2025-07-29T15:47:56.283812"}, {"name": "ANUBIS_SYSTEM.tar.gz", "size_kb": 13682.**********, "extension": ".gz", "modified": "2025-07-29T15:47:42.332172"}, {"name": "HORUS_AI_TEAM.tar.gz", "size_kb": 1514.58203125, "extension": ".gz", "modified": "2025-07-29T15:47:44.451658"}, {"name": "LICENSE", "size_kb": 1.**********, "extension": "", "modified": "2025-07-26T11:00:09.601059"}, {"name": "nginx.conf", "size_kb": 1.**********, "extension": ".conf", "modified": "2025-07-29T15:49:34.258761"}, {"name": "nginx_anubis.conf", "size_kb": 1.**********, "extension": ".conf", "modified": "2025-07-29T15:48:43.059419"}], "total_categorized": 139, "total_uncategorized": 6}, "recommendations": {"immediate_actions": ["تصنيف 6 ملف غير مصنف يدوياً", "تنفيذ خطة تنظيم الملفات المقترحة"], "medium_term": ["إنشاء نظام تنظيم تلقائي للملفات الجديدة", "تطوير معايير تسمية موحدة للملفات", "إنشاء نظام نسخ احتياطي منتظم"], "long_term": ["تطوير نظام إدارة محتوى متقدم", "تكامل مع أنظمة التحكم في الإصدارات", "أتمتة عمليات التنظيف والصيانة"]}}