#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 اختبار سريع لنظام فحص وتحليل النشر
Quick Test for Deployment Analysis System
"""

import os
import sys
import json
import subprocess
from datetime import datetime

def print_header():
    """طباعة رأس الاختبار"""
    print("🏺 اختبار سريع لنظام فحص وتحليل النشر")
    print("=" * 60)
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 المجلد: {os.getcwd()}")
    print("=" * 60)

def check_python():
    """فحص إصدار Python"""
    print("\n🐍 فحص Python:")
    try:
        version = sys.version
        print(f"✅ Python متوفر: {version.split()[0]}")
        return True
    except Exception as e:
        print(f"❌ خطأ في Python: {e}")
        return False

def check_files():
    """فحص وجود الملفات المطلوبة"""
    print("\n📁 فحص الملفات المطلوبة:")
    
    required_files = [
        "PROJECT_DEPLOYMENT_ANALYSIS.py",
        "DEPLOYMENT_COMMANDS.py",
        "DEPLOYMENT_ANALYSIS_GUIDE.md",
        "RUN_DEPLOYMENT_ANALYSIS.bat",
        "RUN_DEPLOYMENT_ANALYSIS.ps1",
        "RUN_DEPLOYMENT_ANALYSIS.sh"
    ]
    
    results = {}
    for file in required_files:
        exists = os.path.exists(file)
        status = "✅" if exists else "❌"
        print(f"{status} {file}")
        results[file] = exists
    
    return results

def check_project_structure():
    """فحص هيكل المشروع"""
    print("\n🏗️ فحص هيكل المشروع:")
    
    main_components = [
        "ANUBIS_SYSTEM",
        "HORUS_AI_TEAM", 
        "ANUBIS_HORUS_MCP",
        "PROJECT_DOCUMENTATION",
        "SHARED_REQUIREMENTS"
    ]
    
    results = {}
    for component in main_components:
        exists = os.path.exists(component) and os.path.isdir(component)
        status = "✅" if exists else "❌"
        
        if exists:
            file_count = sum([len(files) for r, d, files in os.walk(component)])
            print(f"{status} {component} ({file_count} ملف)")
        else:
            print(f"{status} {component}")
        
        results[component] = {"exists": exists, "files": file_count if exists else 0}
    
    return results

def test_import_modules():
    """اختبار استيراد الوحدات"""
    print("\n📦 اختبار استيراد الوحدات:")
    
    modules_to_test = [
        ("os", "نظام التشغيل"),
        ("sys", "النظام"),
        ("json", "JSON"),
        ("datetime", "التاريخ والوقت"),
        ("subprocess", "العمليات الفرعية"),
        ("pathlib", "مسارات الملفات"),
        ("logging", "السجلات")
    ]
    
    results = {}
    for module, description in modules_to_test:
        try:
            __import__(module)
            print(f"✅ {module} - {description}")
            results[module] = True
        except ImportError as e:
            print(f"❌ {module} - {description}: {e}")
            results[module] = False
    
    return results

def check_docker():
    """فحص Docker"""
    print("\n🐳 فحص Docker:")
    
    try:
        result = subprocess.run(
            ["docker", "--version"], 
            capture_output=True, 
            text=True, 
            timeout=10
        )
        if result.returncode == 0:
            print(f"✅ Docker متوفر: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ Docker غير متوفر: {result.stderr}")
            return False
    except FileNotFoundError:
        print("❌ Docker غير مثبت")
        return False
    except subprocess.TimeoutExpired:
        print("❌ Docker لا يستجيب")
        return False
    except Exception as e:
        print(f"❌ خطأ في فحص Docker: {e}")
        return False

def check_git():
    """فحص Git"""
    print("\n📝 فحص Git:")
    
    try:
        result = subprocess.run(
            ["git", "--version"], 
            capture_output=True, 
            text=True, 
            timeout=10
        )
        if result.returncode == 0:
            print(f"✅ Git متوفر: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ Git غير متوفر: {result.stderr}")
            return False
    except FileNotFoundError:
        print("❌ Git غير مثبت")
        return False
    except Exception as e:
        print(f"❌ خطأ في فحص Git: {e}")
        return False

def calculate_score(results):
    """حساب نقاط الاختبار"""
    total_checks = 0
    passed_checks = 0
    
    # فحص الملفات (30%)
    file_results = results.get('files', {})
    total_checks += len(file_results)
    passed_checks += sum(file_results.values())
    
    # فحص هيكل المشروع (30%)
    structure_results = results.get('structure', {})
    total_checks += len(structure_results)
    passed_checks += sum([comp['exists'] for comp in structure_results.values()])
    
    # فحص الوحدات (20%)
    module_results = results.get('modules', {})
    total_checks += len(module_results)
    passed_checks += sum(module_results.values())
    
    # فحص الأدوات (20%)
    tool_results = results.get('tools', {})
    total_checks += len(tool_results)
    passed_checks += sum(tool_results.values())
    
    if total_checks == 0:
        return 0
    
    score = (passed_checks / total_checks) * 100
    return round(score, 1)

def generate_report(results, score):
    """توليد تقرير الاختبار"""
    report = {
        "test_date": datetime.now().isoformat(),
        "test_directory": os.getcwd(),
        "overall_score": score,
        "results": results,
        "recommendations": []
    }
    
    # إضافة التوصيات
    if score >= 90:
        report["recommendations"].append("🟢 النظام جاهز للاستخدام الفوري")
    elif score >= 80:
        report["recommendations"].append("🟡 النظام جيد مع تحسينات بسيطة")
    elif score >= 70:
        report["recommendations"].append("🟠 النظام يحتاج تحسينات متوسطة")
    else:
        report["recommendations"].append("🔴 النظام يحتاج عمل إضافي")
    
    # فحص الملفات المفقودة
    file_results = results.get('files', {})
    missing_files = [f for f, exists in file_results.items() if not exists]
    if missing_files:
        report["recommendations"].append(f"📁 ملفات مفقودة: {', '.join(missing_files)}")
    
    # فحص المكونات المفقودة
    structure_results = results.get('structure', {})
    missing_components = [comp for comp, data in structure_results.items() if not data['exists']]
    if missing_components:
        report["recommendations"].append(f"🏗️ مكونات مفقودة: {', '.join(missing_components)}")
    
    # فحص الأدوات المفقودة
    tool_results = results.get('tools', {})
    missing_tools = [tool for tool, available in tool_results.items() if not available]
    if missing_tools:
        report["recommendations"].append(f"🛠️ أدوات مفقودة: {', '.join(missing_tools)}")
    
    return report

def save_report(report):
    """حفظ التقرير"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"quick_test_report_{timestamp}.json"
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        print(f"\n💾 تم حفظ التقرير: {filename}")
        return filename
    except Exception as e:
        print(f"\n❌ خطأ في حفظ التقرير: {e}")
        return None

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # تجميع نتائج الاختبارات
    results = {}
    
    # فحص Python
    results['python'] = check_python()
    
    # فحص الملفات
    results['files'] = check_files()
    
    # فحص هيكل المشروع
    results['structure'] = check_project_structure()
    
    # فحص الوحدات
    results['modules'] = test_import_modules()
    
    # فحص الأدوات
    tools = {}
    tools['docker'] = check_docker()
    tools['git'] = check_git()
    results['tools'] = tools
    
    # حساب النقاط
    score = calculate_score(results)
    
    # طباعة النتائج
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار السريع:")
    print("=" * 60)
    print(f"🎯 النقاط الإجمالية: {score}%")
    
    if score >= 90:
        print("🟢 التقييم: ممتاز - النظام جاهز للاستخدام الفوري")
    elif score >= 80:
        print("🟡 التقييم: جيد جداً - تحسينات بسيطة مطلوبة")
    elif score >= 70:
        print("🟠 التقييم: جيد - تحسينات متوسطة مطلوبة")
    elif score >= 60:
        print("🔴 التقييم: مقبول - عمل إضافي مطلوب")
    else:
        print("⚫ التقييم: ضعيف - عمل كبير مطلوب")
    
    # توليد وحفظ التقرير
    report = generate_report(results, score)
    save_report(report)
    
    print("\n🎉 انتهى الاختبار السريع!")
    print("💡 لتشغيل التحليل الشامل:")
    print("   python PROJECT_DEPLOYMENT_ANALYSIS.py")
    print("   أو RUN_DEPLOYMENT_ANALYSIS.bat")

if __name__ == "__main__":
    main()
