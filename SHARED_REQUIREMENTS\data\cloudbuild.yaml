# Google Cloud Build Configuration
# ملف تكوين Google Cloud Build لمشروع Universal-AI-Assistants

steps:
  # الخطوة 1: بناء صورة Docker الأساسية
  - name: "gcr.io/cloud-builders/docker"
    args:
      [
        "build",
        "-t",
        "gcr.io/$PROJECT_ID/universal-ai-assistants:$BUILD_ID",
        "-t",
        "gcr.io/$PROJECT_ID/universal-ai-assistants:latest",
        "-f",
        "Dockerfile.production",
        ".",
      ]
    id: "build-main-image"

  # الخطوة 2: بناء صورة ANUBIS_SYSTEM
  - name: "gcr.io/cloud-builders/docker"
    args:
      [
        "build",
        "-t",
        "gcr.io/$PROJECT_ID/anubis-system:$BUILD_ID",
        "-t",
        "gcr.io/$PROJECT_ID/anubis-system:latest",
        "-f",
        "ANUBIS_SYSTEM/Dockerfile",
        "./ANUBIS_SYSTEM",
      ]
    id: "build-anubis"

  # الخطوة 3: بناء صورة HORUS_AI_TEAM
  - name: "gcr.io/cloud-builders/docker"
    args:
      [
        "build",
        "-t",
        "gcr.io/$PROJECT_ID/horus-ai-team:$BUILD_ID",
        "-t",
        "gcr.io/$PROJECT_ID/horus-ai-team:latest",
        "-f",
        "HORUS_AI_TEAM/Dockerfile",
        "./HORUS_AI_TEAM",
      ]
    id: "build-horus"

  # الخطوة 4: بناء صورة ANUBIS_HORUS_MCP
  - name: "gcr.io/cloud-builders/docker"
    args:
      [
        "build",
        "-t",
        "gcr.io/$PROJECT_ID/anubis-horus-mcp:$BUILD_ID",
        "-t",
        "gcr.io/$PROJECT_ID/anubis-horus-mcp:latest",
        "-f",
        "ANUBIS_HORUS_MCP/Dockerfile",
        "./ANUBIS_HORUS_MCP",
      ]
    id: "build-mcp"

  # الخطوة 5: بناء صورة Qwen3-Coder
  - name: "gcr.io/cloud-builders/docker"
    args:
      [
        "build",
        "-t",
        "gcr.io/$PROJECT_ID/qwen3-coder:$BUILD_ID",
        "-t",
        "gcr.io/$PROJECT_ID/qwen3-coder:latest",
        "-f",
        "Qwen3-Coder/Dockerfile",
        "./Qwen3-Coder",
      ]
    id: "build-qwen"

  # الخطوة 6: تشغيل الاختبارات
  - name: "gcr.io/$PROJECT_ID/universal-ai-assistants:$BUILD_ID"
    args: ["python", "-m", "pytest", "tests/", "-v"]
    id: "run-tests"
    waitFor: ["build-main-image"]

  # الخطوة 7: فحص الأمان
  - name: "gcr.io/cloud-builders/docker"
    args:
      [
        "run",
        "--rm",
        "aquasec/trivy:latest",
        "image",
        "gcr.io/$PROJECT_ID/universal-ai-assistants:$BUILD_ID",
      ]
    id: "security-scan"
    waitFor: ["build-main-image"]

  # الخطوة 8: رفع الصور إلى Container Registry
  - name: "gcr.io/cloud-builders/docker"
    args: ["push", "gcr.io/$PROJECT_ID/universal-ai-assistants:$BUILD_ID"]
    id: "push-main"
    waitFor: ["run-tests", "security-scan"]

  - name: "gcr.io/cloud-builders/docker"
    args: ["push", "gcr.io/$PROJECT_ID/anubis-system:$BUILD_ID"]
    id: "push-anubis"
    waitFor: ["build-anubis"]

  - name: "gcr.io/cloud-builders/docker"
    args: ["push", "gcr.io/$PROJECT_ID/horus-ai-team:$BUILD_ID"]
    id: "push-horus"
    waitFor: ["build-horus"]

  - name: "gcr.io/cloud-builders/docker"
    args: ["push", "gcr.io/$PROJECT_ID/anubis-horus-mcp:$BUILD_ID"]
    id: "push-mcp"
    waitFor: ["build-mcp"]

  - name: "gcr.io/cloud-builders/docker"
    args: ["push", "gcr.io/$PROJECT_ID/qwen3-coder:$BUILD_ID"]
    id: "push-qwen"
    waitFor: ["build-qwen"]

  # الخطوة 9: نشر على Google Cloud Run
  - name: "gcr.io/cloud-builders/gcloud"
    args:
      [
        "run",
        "deploy",
        "universal-ai-assistants",
        "--image",
        "gcr.io/$PROJECT_ID/universal-ai-assistants:$BUILD_ID",
        "--region",
        "us-central1",
        "--platform",
        "managed",
        "--allow-unauthenticated",
        "--memory",
        "2Gi",
        "--cpu",
        "2",
        "--max-instances",
        "10",
        "--set-env-vars",
        "PROJECT_ID=$PROJECT_ID,BUILD_ID=$BUILD_ID",
      ]
    id: "deploy-main"
    waitFor: ["push-main"]

  # الخطوة 10: نشر خدمات إضافية
  - name: "gcr.io/cloud-builders/gcloud"
    args:
      [
        "run",
        "deploy",
        "anubis-system",
        "--image",
        "gcr.io/$PROJECT_ID/anubis-system:$BUILD_ID",
        "--region",
        "us-central1",
        "--platform",
        "managed",
        "--memory",
        "1Gi",
        "--cpu",
        "1",
      ]
    id: "deploy-anubis"
    waitFor: ["push-anubis"]

  - name: "gcr.io/cloud-builders/gcloud"
    args:
      [
        "run",
        "deploy",
        "horus-ai-team",
        "--image",
        "gcr.io/$PROJECT_ID/horus-ai-team:$BUILD_ID",
        "--region",
        "us-central1",
        "--platform",
        "managed",
        "--memory",
        "1Gi",
        "--cpu",
        "1",
      ]
    id: "deploy-horus"
    waitFor: ["push-horus"]

  # الخطوة 11: نشر MCP Service
  - name: "gcr.io/cloud-builders/gcloud"
    args:
      [
        "run",
        "deploy",
        "anubis-horus-mcp",
        "--image",
        "gcr.io/$PROJECT_ID/anubis-horus-mcp:$BUILD_ID",
        "--region",
        "us-central1",
        "--platform",
        "managed",
        "--memory",
        "2Gi",
        "--cpu",
        "2",
        "--port",
        "3000",
        "--set-env-vars",
        "NODE_ENV=production",
      ]
    id: "deploy-mcp"
    waitFor: ["push-mcp"]

  # الخطوة 12: نشر Qwen3-Coder Service
  - name: "gcr.io/cloud-builders/gcloud"
    args:
      [
        "run",
        "deploy",
        "qwen3-coder",
        "--image",
        "gcr.io/$PROJECT_ID/qwen3-coder:$BUILD_ID",
        "--region",
        "us-central1",
        "--platform",
        "managed",
        "--memory",
        "4Gi",
        "--cpu",
        "4",
        "--max-instances",
        "5",
        "--set-env-vars",
        "MODEL_NAME=qwen3-coder",
      ]
    id: "deploy-qwen"
    waitFor: ["push-qwen"]

  # الخطوة 13: إعداد Load Balancer
  - name: "gcr.io/cloud-builders/gcloud"
    args:
      [
        "compute",
        "url-maps",
        "create",
        "universal-ai-lb",
        "--default-service",
        "universal-ai-assistants",
      ]
    id: "create-load-balancer"
    waitFor: ["deploy-main", "deploy-anubis", "deploy-horus", "deploy-mcp"]

  # الخطوة 14: إعداد SSL Certificate
  - name: "gcr.io/cloud-builders/gcloud"
    args:
      [
        "compute",
        "ssl-certificates",
        "create",
        "universal-ai-ssl",
        "--domains",
        "${_DOMAIN_NAME}",
      ]
    id: "create-ssl-cert"
    waitFor: ["create-load-balancer"]

  # الخطوة 15: إعداد Cloud SQL Database
  - name: "gcr.io/cloud-builders/gcloud"
    args:
      [
        "sql",
        "instances",
        "create",
        "universal-ai-db",
        "--database-version",
        "POSTGRES_14",
        "--tier",
        "db-f1-micro",
        "--region",
        "us-central1",
      ]
    id: "create-database"

  # الخطوة 16: إنشاء قاعدة البيانات
  - name: "gcr.io/cloud-builders/gcloud"
    args:
      [
        "sql",
        "databases",
        "create",
        "anubis_system",
        "--instance",
        "universal-ai-db",
      ]
    id: "create-db-schema"
    waitFor: ["create-database"]

  # الخطوة 17: إعداد Redis Cache
  - name: "gcr.io/cloud-builders/gcloud"
    args:
      [
        "redis",
        "instances",
        "create",
        "universal-ai-cache",
        "--size",
        "1",
        "--region",
        "us-central1",
        "--redis-version",
        "redis_6_x",
      ]
    id: "create-redis"

  # الخطوة 18: إعداد Monitoring
  - name: "gcr.io/cloud-builders/gcloud"
    args:
      [
        "logging",
        "sinks",
        "create",
        "universal-ai-logs",
        "bigquery.googleapis.com/projects/$PROJECT_ID/datasets/ai_logs",
      ]
    id: "setup-monitoring"

  # الخطوة 19: إعداد Backup
  - name: "gcr.io/cloud-builders/gcloud"
    args:
      [
        "sql",
        "backups",
        "create",
        "--instance",
        "universal-ai-db",
        "--description",
        "Initial backup after deployment",
      ]
    id: "create-backup"
    waitFor: ["create-db-schema"]

  # الخطوة 20: اختبار النشر النهائي
  - name: "gcr.io/cloud-builders/curl"
    args:
      [
        "-f",
        "-s",
        "-o",
        "/dev/null",
        "https://universal-ai-assistants-$PROJECT_ID.a.run.app/health",
      ]
    id: "health-check"
    waitFor: ["deploy-main"]

  # الخطوة 21: إرسال إشعار النجاح
  - name: "gcr.io/cloud-builders/gcloud"
    args:
      [
        "pubsub",
        "topics",
        "publish",
        "deployment-notifications",
        "--message",
        "Universal AI Assistants deployed successfully to $PROJECT_ID",
      ]
    id: "notify-success"
    waitFor:
      [
        "health-check",
        "deploy-anubis",
        "deploy-horus",
        "deploy-mcp",
        "deploy-qwen",
      ]

# إعدادات إضافية
options:
  # استخدام آلة قوية للبناء
  machineType: "E2_HIGHCPU_8"
  # تسجيل مفصل
  logging: CLOUD_LOGGING_ONLY
  # تخزين مؤقت للطبقات
  diskSizeGb: 100
  # استخدام شبكة خاصة
  substitutionOption: ALLOW_LOOSE

# زيادة مهلة البناء (خارج options)
timeout: "3600s"

# متغيرات البيئة
substitutions:
  _DEPLOY_REGION: "us-central1"
  _SERVICE_NAME: "universal-ai-assistants"
  _DOMAIN_NAME: "universal-ai-assistants.com"
  _DB_TIER: "db-f1-micro"
  _REDIS_SIZE: "1"
  _MAX_INSTANCES: "10"
  _MIN_INSTANCES: "1"
  _MEMORY_LIMIT: "2Gi"
  _CPU_LIMIT: "2"

# الصور المنتجة
images:
  - "gcr.io/$PROJECT_ID/universal-ai-assistants:$BUILD_ID"
  - "gcr.io/$PROJECT_ID/universal-ai-assistants:latest"
  - "gcr.io/$PROJECT_ID/anubis-system:$BUILD_ID"
  - "gcr.io/$PROJECT_ID/horus-ai-team:$BUILD_ID"
  - "gcr.io/$PROJECT_ID/anubis-horus-mcp:$BUILD_ID"
  - "gcr.io/$PROJECT_ID/qwen3-coder:$BUILD_ID"

# العلامات للتنظيم
tags:
  [
    "universal-ai-assistants",
    "ai-system",
    "production",
    "multi-service",
    "cloud-run",
  ]

# إعدادات الأمان
serviceAccount: "projects/$PROJECT_ID/serviceAccounts/cloudbuild@$PROJECT_ID.iam.gserviceaccount.com"

# إعدادات الأسرار
availableSecrets:
  secretManager:
    - versionName: projects/$PROJECT_ID/secrets/database-password/versions/latest
      env: "DB_PASSWORD"
    - versionName: projects/$PROJECT_ID/secrets/api-keys/versions/latest
      env: "API_KEYS"
    - versionName: projects/$PROJECT_ID/secrets/jwt-secret/versions/latest
      env: "JWT_SECRET"
    - versionName: projects/$PROJECT_ID/secrets/openai-api-key/versions/latest
      env: "OPENAI_API_KEY"
    - versionName: projects/$PROJECT_ID/secrets/gemini-api-key/versions/latest
      env: "GEMINI_API_KEY"

# إعدادات الشبكة والأمان
logsBucket: "gs://$PROJECT_ID-cloudbuild-logs"

# إعدادات إضافية للنشر
artifacts:
  objects:
    location: "gs://$PROJECT_ID-artifacts"
    paths:
      - "deployment-manifest.yaml"
      - "health-check-results.json"
      - "security-scan-report.json"

# إعدادات البيئة المتقدمة
env:
  - "PROJECT_ID=$PROJECT_ID"
  - "BUILD_ID=$BUILD_ID"
  - "COMMIT_SHA=$COMMIT_SHA"
  - "BRANCH_NAME=$BRANCH_NAME"
  - "REPO_NAME=$REPO_NAME"
  - "TRIGGER_NAME=$TRIGGER_NAME"

# إعدادات الإشعارات
notifications:
  - filter: 'build.status = "SUCCESS"'
    pubsubTopic: "projects/$PROJECT_ID/topics/build-notifications"
  - filter: 'build.status = "FAILURE"'
    pubsubTopic: "projects/$PROJECT_ID/topics/build-failures"

# إعدادات التحكم في الإصدار
gitFileSource:
  path: "cloudbuild.yaml"
  uri: "https://github.com/amrashour1/Universal-AI-Assistants"
  revision: "$COMMIT_SHA"
