# 🚀 Production Dockerfile for Anubis Dashboard
# Multi-stage build optimized for Google Cloud Run

# Stage 1: Build the React application
FROM node:18-alpine as builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies with cache optimization
RUN npm ci --only=production --silent

# Copy source code
COPY . .

# Set production environment
ENV NODE_ENV=production
ENV REACT_APP_API_URL=https://anubis-api.com
ENV REACT_APP_N8N_URL=https://anubis-n8n.com

# Build the application
RUN npm run build

# Stage 2: Production server with nginx
FROM nginx:1.21-alpine

# Install security updates
RUN apk update && apk upgrade && apk add --no-cache curl

# Copy built application
COPY --from=builder /app/build /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S anubis -u 1001 -G nodejs

# Set ownership
RUN chown -R anubis:nodejs /usr/share/nginx/html && \
    chown -R anubis:nodejs /var/cache/nginx && \
    chown -R anubis:nodejs /var/log/nginx && \
    chown -R anubis:nodejs /etc/nginx/conf.d

# Create nginx pid directory
RUN mkdir -p /var/run/nginx && \
    chown -R anubis:nodejs /var/run/nginx

# Switch to non-root user
USER anubis

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/ || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
