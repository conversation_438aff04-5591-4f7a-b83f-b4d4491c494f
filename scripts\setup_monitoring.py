#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import subprocess
import json
import time
from datetime import datetime

class AnubisMonitoringSetup:
    def __init__(self):
        self.project_id = "anubis-467210"
        self.notification_email = "<EMAIL>"
        
    def enable_monitoring_api(self):
        """تفعيل Monitoring API"""
        print("📊 تفعيل Cloud Monitoring API...")
        try:
            result = subprocess.run([
                'gcloud', 'services', 'enable', 'monitoring.googleapis.com',
                '--project', self.project_id
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print("   ✅ تم تفعيل Monitoring API")
                return True
            else:
                print(f"   ❌ فشل تفعيل Monitoring API: {result.stderr}")
                return False
        except Exception as e:
            print(f"   ❌ خطأ في تفعيل Monitoring API: {e}")
            return False
    
    def create_notification_channel(self):
        """إنشاء قناة إشعارات"""
        print("📧 إنشاء قناة الإشعارات...")
        
        notification_config = {
            "type": "email",
            "displayName": "Anubis Admin Email",
            "description": "Email notifications for Anubis project",
            "labels": {
                "email_address": self.notification_email
            }
        }
        
        try:
            # حفظ التكوين في ملف مؤقت
            with open('notification_channel.json', 'w') as f:
                json.dump(notification_config, f, indent=2)
            
            result = subprocess.run([
                'gcloud', 'alpha', 'monitoring', 'channels', 'create',
                '--channel-content-from-file=notification_channel.json',
                '--project', self.project_id
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print(f"   ✅ تم إنشاء قناة الإشعارات: {self.notification_email}")
                return True
            else:
                print(f"   ⚠️ قناة الإشعارات: {result.stderr}")
                return False
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء قناة الإشعارات: {e}")
            return False
    
    def create_vm_uptime_alert(self):
        """إنشاء تنبيه لحالة VM"""
        print("🖥️ إنشاء تنبيه حالة VM...")
        
        alert_policy = {
            "displayName": "Anubis VM Down Alert",
            "documentation": {
                "content": "Alert when Anubis VM is down",
                "mimeType": "text/markdown"
            },
            "conditions": [
                {
                    "displayName": "VM Instance Down",
                    "conditionThreshold": {
                        "filter": 'resource.type="gce_instance" AND resource.labels.instance_id="anubis-n8n-ollama-vm"',
                        "comparison": "COMPARISON_EQUAL",
                        "thresholdValue": 0,
                        "duration": "300s",
                        "aggregations": [
                            {
                                "alignmentPeriod": "60s",
                                "perSeriesAligner": "ALIGN_MEAN",
                                "crossSeriesReducer": "REDUCE_MEAN"
                            }
                        ]
                    }
                }
            ],
            "enabled": True
        }
        
        try:
            with open('vm_alert_policy.json', 'w') as f:
                json.dump(alert_policy, f, indent=2)
            
            result = subprocess.run([
                'gcloud', 'alpha', 'monitoring', 'policies', 'create',
                '--policy-from-file=vm_alert_policy.json',
                '--project', self.project_id
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print("   ✅ تم إنشاء تنبيه حالة VM")
                return True
            else:
                print(f"   ⚠️ تنبيه VM: {result.stderr}")
                return False
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء تنبيه VM: {e}")
            return False
    
    def create_database_alert(self):
        """إنشاء تنبيه لقاعدة البيانات"""
        print("🗄️ إنشاء تنبيه قاعدة البيانات...")
        
        db_alert_policy = {
            "displayName": "Anubis MySQL Database Alert",
            "documentation": {
                "content": "Alert for MySQL database issues",
                "mimeType": "text/markdown"
            },
            "conditions": [
                {
                    "displayName": "Database CPU High",
                    "conditionThreshold": {
                        "filter": 'resource.type="cloudsql_database" AND resource.labels.database_id="anubis-467210:anubis-mysql-db"',
                        "comparison": "COMPARISON_GREATER_THAN",
                        "thresholdValue": 80,
                        "duration": "300s"
                    }
                }
            ],
            "enabled": True
        }
        
        try:
            with open('db_alert_policy.json', 'w') as f:
                json.dump(db_alert_policy, f, indent=2)
            
            result = subprocess.run([
                'gcloud', 'alpha', 'monitoring', 'policies', 'create',
                '--policy-from-file=db_alert_policy.json',
                '--project', self.project_id
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print("   ✅ تم إنشاء تنبيه قاعدة البيانات")
                return True
            else:
                print(f"   ⚠️ تنبيه قاعدة البيانات: {result.stderr}")
                return False
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء تنبيه قاعدة البيانات: {e}")
            return False
    
    def create_custom_dashboard(self):
        """إنشاء لوحة مراقبة مخصصة"""
        print("📊 إنشاء لوحة المراقبة...")
        
        dashboard_config = {
            "displayName": "Anubis Project Dashboard",
            "mosaicLayout": {
                "tiles": [
                    {
                        "width": 6,
                        "height": 4,
                        "widget": {
                            "title": "VM CPU Usage",
                            "xyChart": {
                                "dataSets": [
                                    {
                                        "timeSeriesQuery": {
                                            "timeSeriesFilter": {
                                                "filter": 'resource.type="gce_instance"',
                                                "aggregation": {
                                                    "alignmentPeriod": "60s",
                                                    "perSeriesAligner": "ALIGN_MEAN"
                                                }
                                            }
                                        }
                                    }
                                ]
                            }
                        }
                    }
                ]
            }
        }
        
        try:
            with open('dashboard_config.json', 'w') as f:
                json.dump(dashboard_config, f, indent=2)
            
            result = subprocess.run([
                'gcloud', 'monitoring', 'dashboards', 'create',
                '--config-from-file=dashboard_config.json',
                '--project', self.project_id
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print("   ✅ تم إنشاء لوحة المراقبة")
                return True
            else:
                print(f"   ⚠️ لوحة المراقبة: {result.stderr}")
                return False
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء لوحة المراقبة: {e}")
            return False
    
    def setup_monitoring(self):
        """إعداد المراقبة الكاملة"""
        print("🔧 بدء إعداد Cloud Monitoring لمشروع أنوبيس...")
        print("=" * 60)
        
        steps = [
            ("تفعيل Monitoring API", self.enable_monitoring_api),
            ("إنشاء قناة الإشعارات", self.create_notification_channel),
            ("إنشاء تنبيه VM", self.create_vm_uptime_alert),
            ("إنشاء تنبيه قاعدة البيانات", self.create_database_alert),
            ("إنشاء لوحة المراقبة", self.create_custom_dashboard)
        ]
        
        completed = 0
        total = len(steps)
        
        for step_name, step_func in steps:
            print(f"\n🔄 {step_name}...")
            if step_func():
                completed += 1
            time.sleep(3)  # انتظار بين الخطوات
        
        print("\n" + "=" * 60)
        print(f"📊 نتائج الإعداد: {completed}/{total} خطوة مكتملة")
        
        if completed == total:
            print("🎉 تم إعداد المراقبة بنجاح!")
            print(f"📧 ستصلك الإشعارات على: {self.notification_email}")
            print("🌐 يمكنك عرض المراقبة في: https://console.cloud.google.com/monitoring")
        elif completed >= total * 0.8:
            print("⚠️ تم إعداد معظم المراقبة - بعض الميزات قد تحتاج إعداد يدوي")
        else:
            print("❌ فشل في إعداد المراقبة - يحتاج فحص الصلاحيات")
        
        return completed, total

if __name__ == "__main__":
    monitor = AnubisMonitoringSetup()
    monitor.setup_monitoring()
