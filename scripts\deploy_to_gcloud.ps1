# Deploy Universal AI Assistants to Google Cloud
Write-Host "🏺 Deploying Universal AI Assistants to Google Cloud" -ForegroundColor Cyan
Write-Host "================================================" -ForegroundColor Cyan

Write-Host "📋 Step 1: Creating project..." -ForegroundColor Yellow
gcloud projects create universal-ai-assistants-2025 --name="Universal AI Assistants"

Write-Host "📋 Step 2: Setting active project..." -ForegroundColor Yellow
gcloud config set project universal-ai-assistants-2025

Write-Host "📋 Step 3: Enabling required APIs..." -ForegroundColor Yellow
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com

Write-Host "📋 Step 4: Deploying to Cloud Run..." -ForegroundColor Yellow
gcloud run deploy universal-ai-assistants `
  --source https://github.com/amrashour1/universal-ai-assistants-agent `
  --platform managed `
  --region us-central1 `
  --allow-unauthenticated `
  --set-env-vars GEMINI_API_KEY="AIzaSyDJADkYbl6Hmhg76mfpPSl1yTE_jIVvo54" `
  --memory 2Gi `
  --cpu 2 `
  --max-instances 10

Write-Host "📋 Step 5: Getting application URL..." -ForegroundColor Yellow
$url = gcloud run services describe universal-ai-assistants --region us-central1 --format="value(status.url)"
Write-Host "🌐 Application URL: $url" -ForegroundColor Green

Write-Host "✅ Deployment completed!" -ForegroundColor Green
Read-Host "Press Enter to continue"
