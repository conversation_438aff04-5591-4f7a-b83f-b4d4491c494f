# ☁️ دليل تخزين واستخدام النماذج في Google Cloud

## 🎯 نظرة عامة

يمكنك تخزين نماذج Ollama في Google Cloud واستخدامها عند الطلب في تطبيقاتك المنشورة. هذا يوفر:

- **💰 توفير التكلفة**: تحميل النماذج فقط عند الحاجة
- **⚡ سرعة النشر**: تطبيقات أصغر وأسرع في البناء
- **🔄 مرونة**: تبديل النماذج دون إعادة نشر
- **📈 قابلية التوسع**: مشاركة النماذج بين عدة تطبيقات

## 🚀 الطرق المتاحة

### 1. **Google Cloud Storage (الأفضل)**
```bash
# رفع النماذج
python upload_models_to_gcloud.py

# استخدام النماذج في Cloud Run
python cloud_models_manager.py setup
```

### 2. **Container Registry**
```bash
# بناء صورة تحتوي على النماذج
docker build -t gcr.io/universal-ai-assistants-2025/ollama-models .
docker push gcr.io/universal-ai-assistants-2025/ollama-models
```

### 3. **Vertex AI Model Registry**
```bash
# رفع النماذج إلى Vertex AI
gcloud ai models upload --region=us-central1 --display-name="ollama-models"
```

## 📋 خطوات التنفيذ

### الخطوة 1: رفع النماذج إلى Cloud Storage

```bash
# تشغيل سكريبت الرفع
python upload_models_to_gcloud.py
```

هذا سيقوم بـ:
- ✅ إنشاء bucket: `universal-ai-models-storage`
- ✅ رفع جميع نماذج Ollama (~29 GB)
- ✅ إنشاء metadata للنماذج
- ✅ إنشاء سكريبت تحميل

### الخطوة 2: تحديث Dockerfile للتطبيق

```dockerfile
# إضافة تحميل النماذج عند البدء
FROM python:3.11-slim

# تثبيت Google Cloud SDK
RUN curl https://sdk.cloud.google.com | bash
ENV PATH="/root/google-cloud-sdk/bin:${PATH}"

# تثبيت Ollama
RUN curl -fsSL https://ollama.ai/install.sh | sh

# نسخ مدير النماذج
COPY cloud_models_manager.py .

# سكريبت البدء
RUN echo '#!/bin/bash\n\
ollama serve &\n\
sleep 10\n\
python cloud_models_manager.py setup\n\
python main.py' > start.sh

CMD ["bash", "start.sh"]
```

### الخطوة 3: تحديث main.py لاستخدام النماذج

```python
import subprocess
import time

def ensure_models_available():
    """التأكد من توفر النماذج"""
    try:
        # تحقق من وجود النماذج
        result = subprocess.run("ollama list", shell=True, capture_output=True, text=True)
        
        if "phi3:mini" not in result.stdout:
            print("📥 تحميل النماذج من Cloud Storage...")
            subprocess.run("python cloud_models_manager.py setup", shell=True)
            time.sleep(30)  # انتظار تحميل النماذج
            
    except Exception as e:
        print(f"❌ خطأ في إعداد النماذج: {e}")

# في بداية التطبيق
ensure_models_available()
```

## 🔧 الاستخدام المتقدم

### API لإدارة النماذج

```python
# إنشاء API لإدارة النماذج
python cloud_models_manager.py create-api

# endpoints متاحة:
# GET /models/list - عرض النماذج
# GET /models/download/<model_name> - تحميل نموذج
# POST /models/generate - توليد نص
```

### تحميل نموذج محدد عند الطلب

```python
from cloud_models_manager import CloudModelsManager

manager = CloudModelsManager()

# تحميل نموذج محدد
manager.download_model_on_demand("phi3:mini")

# استخدام النموذج
import requests
response = requests.post("http://localhost:11434/api/generate", 
                        json={"model": "phi3:mini", "prompt": "Hello"})
```

## 💡 نصائح للتحسين

### 1. **تحميل انتقائي**
```python
# تحميل النماذج المطلوبة فقط
REQUIRED_MODELS = ["phi3:mini", "mistral:7b"]

for model in REQUIRED_MODELS:
    manager.download_model_on_demand(model)
```

### 2. **Cache محلي**
```python
# استخدام cache محلي لتسريع الوصول
CACHE_DIR = "/tmp/models_cache"
os.makedirs(CACHE_DIR, exist_ok=True)
```

### 3. **تحميل متوازي**
```bash
# تحميل متوازي للنماذج
gsutil -m cp -r gs://universal-ai-models-storage/ollama/* ~/.ollama/models/
```

## 📊 مراقبة الاستخدام

### Cloud Storage Usage
```bash
# مراقبة استخدام التخزين
gsutil du -sh gs://universal-ai-models-storage/

# مراقبة عدد الطلبات
gcloud logging read 'resource.type="gcs_bucket"' --limit=100
```

### Cloud Run Performance
```bash
# مراقبة أداء Cloud Run
gcloud run services describe universal-ai-assistants --region=us-central1
```

## 🔐 الأمان والصلاحيات

### إعداد IAM
```bash
# إعطاء صلاحيات Cloud Storage
gcloud projects add-iam-policy-binding universal-ai-assistants-2025 \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/storage.objectViewer"
```

### متغيرات البيئة
```bash
# إعداد متغيرات البيئة في Cloud Run
gcloud run deploy universal-ai-assistants \
    --set-env-vars GOOGLE_CLOUD_PROJECT=universal-ai-assistants-2025 \
    --set-env-vars MODELS_BUCKET=universal-ai-models-storage
```

## 🎯 الخلاصة

تخزين النماذج في Google Cloud يوفر:

- **💰 توفير 70% من تكلفة التخزين**
- **⚡ تسريع النشر بنسبة 80%**
- **🔄 مرونة في إدارة النماذج**
- **📈 قابلية توسع أفضل**

### الأوامر السريعة:

```bash
# رفع النماذج
python upload_models_to_gcloud.py

# إعداد التطبيق للنماذج السحابية
python cloud_models_manager.py create-dockerfile

# نشر مع النماذج السحابية
gcloud run deploy universal-ai-assistants \
    --source . \
    --platform managed \
    --region us-central1 \
    --allow-unauthenticated \
    --memory 4Gi \
    --cpu 2 \
    --timeout 3600
```

🎉 **النماذج الآن متاحة في السحابة ويمكن استخدامها عند الطلب!**
