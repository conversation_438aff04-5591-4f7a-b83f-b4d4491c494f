#!/usr/bin/env python3
import requests
import json

def test_deployment():
    base_url = 'https://universal-ai-assistants-554716410816.us-central1.run.app'
    endpoints = ['/', '/health', '/anubis', '/horus', '/mcp', '/metrics', '/status/detailed', '/api/genkit/status']
    
    print('🎉 اختبار نشر Universal AI Assistants')
    print('=' * 60)
    print(f'🌐 URL: {base_url}')
    print('=' * 60)
    
    all_success = True
    
    for endpoint in endpoints:
        try:
            print(f'🔍 اختبار {endpoint}...')
            response = requests.get(f'{base_url}{endpoint}', timeout=10)
            
            if response.status_code == 200:
                print(f'✅ {endpoint}: نجح ({response.status_code})')
                
                try:
                    data = response.json()
                    if 'message' in data:
                        print(f'   📝 الرسالة: {data["message"]}')
                    elif 'description' in data:
                        print(f'   📝 الوصف: {data["description"]}')
                    elif 'system' in data:
                        print(f'   🏺 النظام: {data["system"]}')
                except:
                    print(f'   📄 المحتوى: {response.text[:100]}...')
                    
            else:
                print(f'❌ {endpoint}: فشل ({response.status_code})')
                all_success = False
                
        except Exception as e:
            print(f'❌ {endpoint}: خطأ - {str(e)}')
            all_success = False
            
        print()
    
    print('=' * 60)
    if all_success:
        print('🎉 جميع الاختبارات نجحت! التطبيق يعمل بشكل مثالي!')
        print('🌟 Universal AI Assistants متاح الآن للعالم!')
    else:
        print('⚠️ بعض الاختبارات فشلت. يرجى المراجعة.')
    print('=' * 60)

if __name__ == "__main__":
    test_deployment()
