#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import subprocess
import os
import time
from pathlib import Path

class DeployActualProjects:
    def __init__(self):
        self.vm_ip = "**************"
        self.project_id = "anubis-467210"
        self.zone = "us-central1-a"
        self.vm_name = "anubis-n8n-ollama-vm"
        
    def upload_projects_to_vm(self):
        """رفع المشاريع الأصلية إلى VM"""
        print("📦 رفع المشاريع الأصلية إلى VM...")
        
        projects = [
            "ANUBIS_SYSTEM",
            "HORUS_AI_TEAM", 
            "ANUBIS_HORUS_MCP"
        ]
        
        for project in projects:
            if os.path.exists(project):
                print(f"📁 رفع {project}...")
                try:
                    # إنشاء أرشيف مضغوط
                    subprocess.run([
                        'tar', '-czf', f'{project}.tar.gz', project
                    ], check=True, shell=True)
                    
                    # رفع إلى VM
                    subprocess.run([
                        'gcloud', 'compute', 'scp', f'{project}.tar.gz',
                        f'{self.vm_name}:~/', '--zone', self.zone
                    ], check=True)
                    
                    print(f"   ✅ تم رفع {project}")
                except Exception as e:
                    print(f"   ❌ فشل رفع {project}: {e}")
            else:
                print(f"   ⚠️ {project} غير موجود")
    
    def setup_projects_on_vm(self):
        """إعداد المشاريع على VM"""
        print("🔧 إعداد المشاريع على VM...")
        
        setup_commands = [
            # إيقاف الخدمات الحالية
            "sudo docker stop $(sudo docker ps -q) || true",
            
            # استخراج المشاريع
            "tar -xzf ANUBIS_SYSTEM.tar.gz || true",
            "tar -xzf HORUS_AI_TEAM.tar.gz || true", 
            "tar -xzf ANUBIS_HORUS_MCP.tar.gz || true",
            
            # تثبيت Python والمتطلبات
            "sudo apt update",
            "sudo apt install -y python3-pip python3-venv",
            
            # إعداد ANUBIS_SYSTEM
            "cd ANUBIS_SYSTEM && python3 -m venv venv && source venv/bin/activate && pip install -r requirements.txt || true",
            
            # إعداد HORUS_AI_TEAM
            "cd ~/HORUS_AI_TEAM && python3 -m venv venv && source venv/bin/activate && pip install -r requirements.txt || true",
            
            # تشغيل ANUBIS_SYSTEM
            "cd ~/ANUBIS_SYSTEM && nohup python3 main.py > anubis.log 2>&1 &",
            
            # تشغيل HORUS_AI_TEAM
            "cd ~/HORUS_AI_TEAM && nohup python3 quick_start.py > horus.log 2>&1 &",
            
            # تشغيل Ollama وتحميل النماذج
            "curl -fsSL https://ollama.ai/install.sh | sh",
            "sudo systemctl start ollama",
            "ollama pull phi3:mini",
            "ollama pull mistral:7b",
            "ollama pull llama3:8b",
            
            # إعداد nginx reverse proxy
            "sudo apt install -y nginx",
            "sudo systemctl start nginx",
            "sudo systemctl enable nginx"
        ]
        
        for cmd in setup_commands:
            print(f"🔄 تنفيذ: {cmd[:50]}...")
            try:
                result = subprocess.run([
                    'gcloud', 'compute', 'ssh', self.vm_name,
                    '--zone', self.zone, '--command', cmd
                ], capture_output=True, text=True, timeout=300)
                
                if result.returncode == 0:
                    print(f"   ✅ نجح")
                else:
                    print(f"   ⚠️ تحذير: {result.stderr[:100]}")
            except Exception as e:
                print(f"   ❌ خطأ: {e}")
            
            time.sleep(2)
    
    def create_nginx_config(self):
        """إنشاء تكوين nginx للمشاريع"""
        print("🌐 إعداد nginx reverse proxy...")
        
        nginx_config = """
server {
    listen 80;
    server_name _;
    
    # ANUBIS_SYSTEM
    location /anubis/ {
        proxy_pass http://localhost:8000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # HORUS_AI_TEAM  
    location /horus/ {
        proxy_pass http://localhost:7000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # ANUBIS_HORUS_MCP
    location /mcp/ {
        proxy_pass http://localhost:3000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # n8n
    location /n8n/ {
        proxy_pass http://localhost:5678/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # Ollama
    location /ollama/ {
        proxy_pass http://localhost:11434/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # الصفحة الرئيسية
    location / {
        return 200 'Anubis AI System - Projects Available at: /anubis /horus /mcp /n8n /ollama';
        add_header Content-Type text/plain;
    }
}
"""
        
        try:
            # كتابة التكوين إلى ملف مؤقت
            with open('nginx_anubis.conf', 'w') as f:
                f.write(nginx_config)
            
            # رفع التكوين إلى VM
            subprocess.run([
                'gcloud', 'compute', 'scp', 'nginx_anubis.conf',
                f'{self.vm_name}:~/', '--zone', self.zone
            ], check=True)
            
            # تطبيق التكوين
            subprocess.run([
                'gcloud', 'compute', 'ssh', self.vm_name,
                '--zone', self.zone, '--command',
                'sudo mv nginx_anubis.conf /etc/nginx/sites-available/default && sudo systemctl reload nginx'
            ], check=True)
            
            print("   ✅ تم إعداد nginx")
            
        except Exception as e:
            print(f"   ❌ فشل إعداد nginx: {e}")
    
    def open_firewall_ports(self):
        """فتح منافذ إضافية للمشاريع"""
        print("🔥 فتح منافذ إضافية...")
        
        ports = [
            ("8000", "ANUBIS_SYSTEM"),
            ("7000", "HORUS_AI_TEAM"), 
            ("3000", "ANUBIS_HORUS_MCP"),
            ("80", "nginx")
        ]
        
        for port, service in ports:
            try:
                subprocess.run([
                    'gcloud', 'compute', 'firewall-rules', 'create',
                    f'allow-{service.lower().replace("_", "-")}-{port}',
                    '--allow', f'tcp:{port}',
                    '--source-ranges', '0.0.0.0/0',
                    '--description', f'Allow {service} on port {port}'
                ], check=True)
                print(f"   ✅ فتح منفذ {port} لـ {service}")
            except:
                print(f"   ⚠️ منفذ {port} مفتوح مسبقاً أو خطأ")
    
    def deploy_all(self):
        """نشر جميع المشاريع"""
        print("🚀 بدء نشر المشاريع الأصلية...")
        print("=" * 60)
        
        steps = [
            ("رفع المشاريع إلى VM", self.upload_projects_to_vm),
            ("إعداد المشاريع", self.setup_projects_on_vm),
            ("إعداد nginx", self.create_nginx_config),
            ("فتح المنافذ", self.open_firewall_ports)
        ]
        
        for step_name, step_func in steps:
            print(f"\n🔄 {step_name}...")
            step_func()
            time.sleep(5)
        
        print("\n" + "=" * 60)
        print("🎉 تم نشر المشاريع!")
        print(f"🌐 الوصول للمشاريع:")
        print(f"   • ANUBIS_SYSTEM: http://{self.vm_ip}/anubis")
        print(f"   • HORUS_AI_TEAM: http://{self.vm_ip}/horus") 
        print(f"   • ANUBIS_HORUS_MCP: http://{self.vm_ip}/mcp")
        print(f"   • n8n: http://{self.vm_ip}/n8n")
        print(f"   • Ollama: http://{self.vm_ip}/ollama")

if __name__ == "__main__":
    deployer = DeployActualProjects()
    deployer.deploy_all()
