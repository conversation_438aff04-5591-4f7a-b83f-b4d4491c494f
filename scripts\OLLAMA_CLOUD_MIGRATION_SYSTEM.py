#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 نظام تخزين نماذج Ollama على Google Cloud
Ollama Models Cloud Storage Migration System
"""

import os
import sys
import json
import subprocess
import logging
from datetime import datetime
from pathlib import Path

class OllamaCloudMigrationSystem:
    """نظام تخزين نماذج Ollama على Google Cloud"""
    
    def __init__(self):
        self.setup_logging()
        self.project_root = Path.cwd()
        self.cloud_config = {}
        self.local_models = []
        self.migration_plan = {}
        
    def setup_logging(self):
        """إعداد نظام السجلات"""
        log_dir = Path("ollama_cloud_migration_logs")
        log_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = log_dir / f"ollama_migration_{timestamp}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def print_header(self):
        """طباعة رأس النظام"""
        print("🏺 نظام تخزين نماذج Ollama على Google Cloud")
        print("=" * 70)
        print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📁 مجلد المشروع: {self.project_root}")
        print("=" * 70)
        
    def check_prerequisites(self):
        """فحص المتطلبات الأساسية"""
        self.logger.info("🔍 فحص المتطلبات الأساسية...")
        
        prerequisites = {
            'ollama': self.check_ollama(),
            'gcloud': self.check_gcloud_cli(),
            'docker': self.check_docker(),
            'python': self.check_python_packages()
        }
        
        return prerequisites
        
    def check_ollama(self):
        """فحص Ollama"""
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
            if result.returncode == 0:
                self.logger.info("✅ Ollama متوفر ويعمل")
                return True
            else:
                self.logger.error("❌ Ollama غير متوفر أو لا يعمل")
                return False
        except FileNotFoundError:
            self.logger.error("❌ Ollama غير مثبت")
            return False
            
    def check_gcloud_cli(self):
        """فحص Google Cloud CLI"""
        try:
            result = subprocess.run(['gcloud', 'version'], capture_output=True, text=True)
            if result.returncode == 0:
                self.logger.info("✅ Google Cloud CLI متوفر")
                return True
            else:
                self.logger.error("❌ Google Cloud CLI غير متوفر")
                return False
        except FileNotFoundError:
            self.logger.error("❌ Google Cloud CLI غير مثبت")
            return False
            
    def check_docker(self):
        """فحص Docker"""
        try:
            result = subprocess.run(['docker', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                self.logger.info("✅ Docker متوفر")
                return True
            else:
                self.logger.error("❌ Docker غير متوفر")
                return False
        except FileNotFoundError:
            self.logger.error("❌ Docker غير مثبت")
            return False
            
    def check_python_packages(self):
        """فحص حزم Python المطلوبة"""
        required_packages = [
            'google-cloud-storage',
            'google-cloud-compute',
            'docker',
            'requests'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
                self.logger.info(f"✅ {package} متوفر")
            except ImportError:
                missing_packages.append(package)
                self.logger.warning(f"⚠️ {package} غير متوفر")
                
        return len(missing_packages) == 0
        
    def scan_local_models(self):
        """فحص النماذج المحلية"""
        self.logger.info("📊 فحص النماذج المحلية...")
        
        try:
            result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]  # تجاهل العنوان
                
                for line in lines:
                    if line.strip():
                        parts = line.split()
                        if len(parts) >= 3:
                            model_info = {
                                'name': parts[0],
                                'id': parts[1] if len(parts) > 1 else '',
                                'size': parts[2] if len(parts) > 2 else '',
                                'modified': ' '.join(parts[3:]) if len(parts) > 3 else ''
                            }
                            self.local_models.append(model_info)
                            
                self.logger.info(f"📦 تم العثور على {len(self.local_models)} نموذج محلي")
                return self.local_models
            else:
                self.logger.error("❌ فشل في قراءة قائمة النماذج المحلية")
                return []
        except Exception as e:
            self.logger.error(f"❌ خطأ في فحص النماذج المحلية: {e}")
            return []
            
    def create_migration_plan(self):
        """إنشاء خطة الترحيل"""
        self.logger.info("📋 إنشاء خطة الترحيل...")
        
        self.migration_plan = {
            'timestamp': datetime.now().isoformat(),
            'total_models': len(self.local_models),
            'models': [],
            'cloud_config': {
                'project_id': '',
                'bucket_name': 'ollama-models-storage',
                'region': 'us-central1',
                'storage_class': 'STANDARD'
            },
            'estimated_cost': 0,
            'estimated_time': 0
        }
        
        total_size_gb = 0
        for model in self.local_models:
            # تحويل حجم النموذج إلى GB
            size_str = model.get('size', '0B')
            size_gb = self.parse_size_to_gb(size_str)
            total_size_gb += size_gb
            
            model_plan = {
                'name': model['name'],
                'local_size': size_str,
                'size_gb': size_gb,
                'priority': self.get_model_priority(model['name']),
                'compression': True,
                'backup_local': True,
                'cloud_path': f"models/{model['name'].replace(':', '_')}"
            }
            self.migration_plan['models'].append(model_plan)
            
        # تقدير التكلفة والوقت
        self.migration_plan['total_size_gb'] = total_size_gb
        self.migration_plan['estimated_cost'] = self.estimate_storage_cost(total_size_gb)
        self.migration_plan['estimated_time'] = self.estimate_migration_time(total_size_gb)
        
        return self.migration_plan
        
    def parse_size_to_gb(self, size_str):
        """تحويل حجم النموذج إلى GB"""
        if not size_str or size_str == '0B':
            return 0
            
        size_str = size_str.upper().replace(' ', '')
        
        if 'GB' in size_str:
            return float(size_str.replace('GB', ''))
        elif 'MB' in size_str:
            return float(size_str.replace('MB', '')) / 1024
        elif 'KB' in size_str:
            return float(size_str.replace('KB', '')) / (1024 * 1024)
        elif 'B' in size_str:
            return float(size_str.replace('B', '')) / (1024 * 1024 * 1024)
        else:
            return 0
            
    def get_model_priority(self, model_name):
        """تحديد أولوية النموذج"""
        high_priority = ['llama', 'mistral', 'gemma', 'phi']
        medium_priority = ['codellama', 'vicuna', 'orca']
        
        model_lower = model_name.lower()
        
        for priority_model in high_priority:
            if priority_model in model_lower:
                return 'high'
                
        for priority_model in medium_priority:
            if priority_model in model_lower:
                return 'medium'
                
        return 'low'
        
    def estimate_storage_cost(self, total_gb):
        """تقدير تكلفة التخزين الشهرية"""
        # Google Cloud Storage Standard pricing (تقريبي)
        cost_per_gb_month = 0.020  # $0.020 per GB per month
        return total_gb * cost_per_gb_month
        
    def estimate_migration_time(self, total_gb):
        """تقدير وقت الترحيل"""
        # افتراض سرعة رفع 10 MB/s
        upload_speed_mbps = 10
        total_mb = total_gb * 1024
        time_seconds = total_mb / upload_speed_mbps
        return time_seconds / 3600  # تحويل إلى ساعات
        
    def generate_cloud_setup_script(self):
        """توليد سكريبت إعداد Google Cloud"""
        script_content = f"""#!/bin/bash
# 🏺 سكريبت إعداد Google Cloud لتخزين نماذج Ollama

echo "🏺 إعداد Google Cloud لتخزين نماذج Ollama"
echo "================================================"

# 1. تسجيل الدخول إلى Google Cloud
echo "🔐 تسجيل الدخول إلى Google Cloud..."
gcloud auth login

# 2. إنشاء مشروع جديد (اختياري)
echo "📁 إنشاء مشروع جديد..."
read -p "أدخل معرف المشروع (أو اتركه فارغاً لاستخدام المشروع الحالي): " PROJECT_ID

if [ ! -z "$PROJECT_ID" ]; then
    gcloud projects create $PROJECT_ID
    gcloud config set project $PROJECT_ID
    echo "✅ تم إنشاء المشروع: $PROJECT_ID"
fi

# 3. تفعيل APIs المطلوبة
echo "🔧 تفعيل APIs المطلوبة..."
gcloud services enable storage.googleapis.com
gcloud services enable compute.googleapis.com
gcloud services enable container.googleapis.com

# 4. إنشاء bucket للتخزين
echo "🪣 إنشاء bucket للتخزين..."
BUCKET_NAME="ollama-models-$(date +%Y%m%d)"
gsutil mb -p $(gcloud config get-value project) -c STANDARD -l us-central1 gs://$BUCKET_NAME

# 5. إعداد الصلاحيات
echo "🔒 إعداد الصلاحيات..."
gsutil iam ch allUsers:objectViewer gs://$BUCKET_NAME

echo "✅ تم إعداد Google Cloud بنجاح!"
echo "📦 اسم Bucket: $BUCKET_NAME"
echo "🌐 المنطقة: us-central1"
"""
        
        script_path = Path("setup_google_cloud.sh")
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
            
        # جعل الملف قابل للتنفيذ على Linux/Mac
        try:
            os.chmod(script_path, 0o755)
        except:
            pass
            
        self.logger.info(f"📝 تم إنشاء سكريبت الإعداد: {script_path}")
        return script_path
        
    def save_migration_plan(self):
        """حفظ خطة الترحيل"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        plan_file = Path(f"ollama_migration_plan_{timestamp}.json")
        
        with open(plan_file, 'w', encoding='utf-8') as f:
            json.dump(self.migration_plan, f, ensure_ascii=False, indent=2)
            
        self.logger.info(f"💾 تم حفظ خطة الترحيل: {plan_file}")
        return plan_file
        
    def print_summary(self):
        """طباعة ملخص الخطة"""
        print("\n" + "=" * 70)
        print("📊 ملخص خطة ترحيل نماذج Ollama إلى Google Cloud")
        print("=" * 70)
        
        print(f"📦 عدد النماذج: {self.migration_plan['total_models']}")
        print(f"💾 الحجم الإجمالي: {self.migration_plan['total_size_gb']:.2f} GB")
        print(f"💰 التكلفة المقدرة: ${self.migration_plan['estimated_cost']:.2f}/شهر")
        print(f"⏱️ الوقت المقدر: {self.migration_plan['estimated_time']:.1f} ساعة")
        
        print("\n🎯 النماذج حسب الأولوية:")
        priorities = {'high': [], 'medium': [], 'low': []}
        
        for model in self.migration_plan['models']:
            priorities[model['priority']].append(model)
            
        for priority, models in priorities.items():
            if models:
                priority_name = {'high': 'عالية', 'medium': 'متوسطة', 'low': 'منخفضة'}[priority]
                print(f"\n{priority_name} ({len(models)} نماذج):")
                for model in models[:3]:  # عرض أول 3 فقط
                    print(f"  • {model['name']} ({model['local_size']})")
                if len(models) > 3:
                    print(f"  ... و {len(models) - 3} نماذج أخرى")
                    
    def run_analysis(self):
        """تشغيل التحليل الشامل"""
        self.print_header()
        
        # فحص المتطلبات
        prerequisites = self.check_prerequisites()
        
        if not all(prerequisites.values()):
            self.logger.error("❌ بعض المتطلبات غير متوفرة. يرجى تثبيتها أولاً.")
            return False
            
        # فحص النماذج المحلية
        self.scan_local_models()
        
        if not self.local_models:
            self.logger.warning("⚠️ لم يتم العثور على نماذج محلية")
            return False
            
        # إنشاء خطة الترحيل
        self.create_migration_plan()
        
        # توليد سكريبت الإعداد
        self.generate_cloud_setup_script()
        
        # حفظ الخطة
        self.save_migration_plan()
        
        # طباعة الملخص
        self.print_summary()
        
        self.logger.info("✅ تم إكمال التحليل بنجاح!")
        return True

def main():
    """الدالة الرئيسية"""
    system = OllamaCloudMigrationSystem()
    success = system.run_analysis()
    
    if success:
        print("\n🎉 تم إنشاء خطة ترحيل نماذج Ollama بنجاح!")
        print("\n📋 الخطوات التالية:")
        print("1. مراجعة خطة الترحيل المحفوظة")
        print("2. تشغيل سكريبت إعداد Google Cloud")
        print("3. بدء عملية الترحيل")
    else:
        print("\n❌ فشل في إنشاء خطة الترحيل")
        print("يرجى مراجعة السجلات لمعرفة السبب")

if __name__ == "__main__":
    main()
