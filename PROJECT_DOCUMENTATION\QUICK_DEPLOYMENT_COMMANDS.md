# 🏺 أوامر فحص وتحليل نشر المشروع - دليل سريع

## 🚀 الأوامر الأساسية

### 1. التحليل الشامل (الأداة الرئيسية)
```bash
python PROJECT_DEPLOYMENT_ANALYSIS.py
```
**الوصف:** تحليل شامل لجاهزية المشروع للنشر مع نقاط التقييم والتوصيات

### 2. فحص متطلبات النظام
```bash
python DEPLOYMENT_COMMANDS.py --command requirements
```
**الوصف:** فحص توفر Python, Docker, Git, Node.js وإصداراتها

### 3. تحليل صحة المشروع
```bash
python DEPLOYMENT_COMMANDS.py --command health
```
**الوصف:** فحص المكونات الرئيسية وإحصائيات المشروع

### 4. فحص حالة Docker
```bash
python DEPLOYMENT_COMMANDS.py --command docker
```
**الوصف:** فحص تثبيت Docker والحاويات والصور المتاحة

### 5. تحليل التبعيات
```bash
python DEPLOYMENT_COMMANDS.py --command dependencies
```
**الوصف:** فحص متطلبات Python و Node.js والحزم القديمة

### 6. فحص الأمان
```bash
python DEPLOYMENT_COMMANDS.py --command security
```
**الوصف:** كشف الملفات الحساسة والأسرار المكشوفة

### 7. قائمة فحص النشر
```bash
python DEPLOYMENT_COMMANDS.py --command checklist
```
**الوصف:** توليد قائمة مهام ما قبل وأثناء وبعد النشر

### 8. التحليل الكامل
```bash
python DEPLOYMENT_COMMANDS.py --command full
```
**الوصف:** تشغيل جميع الفحوصات في أمر واحد

## 🖥️ واجهات التشغيل

### Windows Batch
```cmd
RUN_DEPLOYMENT_ANALYSIS.bat
```

### PowerShell
```powershell
.\RUN_DEPLOYMENT_ANALYSIS.ps1
```

### Linux/Mac
```bash
chmod +x RUN_DEPLOYMENT_ANALYSIS.sh
./RUN_DEPLOYMENT_ANALYSIS.sh
```

## 📊 مثال على النتائج

### نتائج فحص متطلبات النظام
```json
{
  "python": {
    "required": true,
    "installed": true,
    "version": "Python 3.11.5"
  },
  "docker": {
    "required": true,
    "installed": true,
    "version": "Docker version 24.0.6"
  },
  "git": {
    "required": true,
    "installed": true,
    "version": "git version 2.42.0"
  }
}
```

### نتائج تحليل صحة المشروع
```json
{
  "overall_health": 85.5,
  "components": {
    "ANUBIS_SYSTEM": {
      "status": "healthy",
      "files": 156,
      "size_mb": 45.2
    },
    "HORUS_AI_TEAM": {
      "status": "healthy", 
      "files": 89,
      "size_mb": 12.8
    }
  }
}
```

### نتائج فحص الأمان
```json
{
  "sensitive_files": [
    "config/private.key",
    "ssl/certificate.pem"
  ],
  "exposed_secrets": [
    {
      "file": "src/config.py",
      "pattern": "api_key"
    }
  ],
  "recommendations": [
    "إزالة الأسرار المكشوفة من الكود",
    "إزالة أو تأمين الملفات الحساسة"
  ]
}
```

## 🎯 تفسير النقاط

### نقاط جاهزية النشر
- **90-100%**: 🟢 **ممتاز** - جاهز للنشر الفوري
- **80-89%**: 🟡 **جيد جداً** - يحتاج تحسينات بسيطة
- **70-79%**: 🟠 **جيد** - يحتاج تحسينات متوسطة
- **60-69%**: 🔴 **مقبول** - يحتاج عمل إضافي
- **أقل من 60%**: ⚫ **ضعيف** - يحتاج عمل كبير

### معايير التقييم
| المعيار | الوزن | الحد الأدنى المقبول |
|---------|--------|-------------------|
| 🏗️ الهيكل | 20% | 70% |
| 📚 التوثيق | 15% | 60% |
| 📦 التبعيات | 15% | 70% |
| 🔒 الأمان | 20% | 80% |
| 🐳 Docker | 10% | 50% |
| 🧪 الاختبارات | 10% | 60% |
| 🚀 النشر | 10% | 70% |

## ⚡ أوامر سريعة للمشاكل الشائعة

### إصلاح مشاكل Docker
```bash
# تشغيل Docker (Windows)
net start docker

# تشغيل Docker (Linux)
sudo systemctl start docker

# فحص حالة Docker
docker info
```

### تحديث التبعيات
```bash
# Python
pip install --upgrade -r requirements.txt

# Node.js
npm update

# فحص الحزم القديمة
pip list --outdated
```

### إصلاح مشاكل الأمان
```bash
# إنشاء .gitignore
echo "*.key" >> .gitignore
echo "*.env" >> .gitignore
echo "__pycache__/" >> .gitignore

# إزالة ملفات حساسة من Git
git rm --cached sensitive_file.key
git commit -m "Remove sensitive file"
```

## 📁 مجلدات التقارير

```
deployment_analysis_reports/     # تقارير المحلل الرئيسي
├── deployment_analysis_*.json
├── analysis_*.log
└── README.md

deployment_reports/              # تقارير الأوامر المتخصصة
├── full_deployment_analysis_*.json
├── security_scan_*.json
├── health_check_*.json
└── docker_status_*.json
```

## 🔧 استكشاف الأخطاء

### خطأ: "Python not found"
```bash
# Windows
where python
# إذا لم يوجد، ثبت Python من python.org

# Linux/Mac
which python3
# إذا لم يوجد: sudo apt install python3 (Ubuntu)
```

### خطأ: "Docker not running"
```bash
# فحص حالة Docker
docker version

# تشغيل Docker Desktop (Windows/Mac)
# أو sudo systemctl start docker (Linux)
```

### خطأ: "Permission denied"
```bash
# Linux/Mac
chmod +x *.py
chmod +x *.sh

# Windows (تشغيل كمدير)
# Right-click → Run as administrator
```

## 📞 الحصول على المساعدة

### عرض المساعدة
```bash
python DEPLOYMENT_COMMANDS.py --help
python PROJECT_DEPLOYMENT_ANALYSIS.py --help
```

### تشغيل في وضع التفصيل
```bash
python PROJECT_DEPLOYMENT_ANALYSIS.py --verbose
```

### فحص السجلات
```bash
# عرض آخر سجل
cat deployment_analysis_reports/analysis_*.log | tail -50

# البحث عن أخطاء
grep -i "error" deployment_analysis_reports/analysis_*.log
```

---

**🏺 نظام أنوبيس للذكاء الاصطناعي**  
*دليل سريع لأوامر فحص وتحليل النشر*
