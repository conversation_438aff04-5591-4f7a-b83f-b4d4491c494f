# 📋 قواعد التطوير - Development Rules

<div align="center">

![Development Rules](https://img.shields.io/badge/📋-Development%20Rules-blue?style=for-the-badge)
![Version](https://img.shields.io/badge/Version-1.0.0-green?style=for-the-badge)
![Status](https://img.shields.io/badge/Status-Active-brightgreen?style=for-the-badge)

**قواعد وإرشادات التطوير للعمل مع مشاريع أنوبيس وحورس**

📅 تاريخ الإنشاء: 2025-01-27 | 📝 آخر تحديث: 2025-01-27

</div>

---

## 🎯 الهدف من هذا الملف

هذا الملف يحتوي على القواعد والإرشادات الأساسية للتطوير والعمل مع مشاريع:
- **🏺 نظام أنوبيس (ANUBIS_SYSTEM)**
- **𓅃 فريق حورس (HORUS_AI_TEAM)**  
- **🔗 نظام MCP المتكامل (ANUBIS_HORUS_MCP)**

---

## 📁 قاعدة تنظيم الملفات الأساسية

### 🔧 القاعدة الذهبية الأولى: تنظيم الملفات
```
عند فحص أو تحليل مجلد/مشروع:
✅ إنشاء جميع ملفات العمل داخل المجلد المخصص للمشروع
❌ عدم إنشاء ملفات في المجلد الرئيسي
```

### 📂 هيكل المجلدات المعتمد:
```
Universal-AI-Assistants/
├── ANUBIS_SYSTEM/           # نظام أنوبيس الأساسي
│   ├── analysis/            # ملفات التحليل الخاصة بأنوبيس
│   ├── reports/             # تقارير أنوبيس
│   └── tools/               # أدوات أنوبيس
├── HORUS_AI_TEAM/           # فريق حورس
│   ├── 05_analysis/         # تحليلات حورس
│   ├── 06_documentation/    # توثيق حورس
│   └── 08_utilities/        # أدوات حورس
├── ANUBIS_HORUS_MCP/        # نظام MCP المتكامل
│   ├── analysis/            # تحليلات MCP
│   └── reports/             # تقارير MCP
└── PROJECT_DOCUMENTATION/   # التوثيق العام فقط
```

---

## 🔍 قواعد الفحص والتحليل

### 📊 عند فحص مشروع:

#### ✅ الممارسات الصحيحة:
1. **إنشاء ملفات التحليل داخل المشروع المفحوص**
   ```
   مثال: فحص HORUS_AI_TEAM
   ✅ إنشاء: HORUS_AI_TEAM/05_analysis/analysis_report.md
   ❌ تجنب: analysis_report.md في المجلد الرئيسي
   ```

2. **استخدام مجلدات فرعية منظمة**
   ```
   HORUS_AI_TEAM/
   ├── 05_analysis/
   │   ├── reports/         # تقارير التحليل
   │   ├── tools/           # أدوات التحليل
   │   └── data/            # بيانات التحليل
   ```

3. **تسمية الملفات بوضوح**
   ```
   ✅ horus_comprehensive_analysis_20250127.md
   ✅ gemini_cli_analysis_report.json
   ✅ team_members_analysis.py
   ```

#### ❌ الممارسات المرفوضة:
1. إنشاء ملفات تحليل في المجلد الرئيسي
2. خلط ملفات مشاريع مختلفة
3. استخدام أسماء ملفات غير واضحة

---

## 🤖 قواعد العمل مع الوكلاء

### 🔍 فحص الوكلاء الجدد:

#### 📋 خطوات الفحص المعتمدة:
1. **فحص وجود الملف**
   ```python
   # التحقق من وجود الوكيل
   agent_path = "02_team_members/AGENT_NAME.py"
   if os.path.exists(agent_path):
       # تحليل الوكيل
   ```

2. **تحليل محتوى الوكيل**
   - عدد الأسطر
   - عدد الكلاسات والدوال
   - التبعيات والمكتبات
   - النموذج المستخدم

3. **توثيق النتائج**
   ```
   إنشاء تقرير في: PROJECT/05_analysis/reports/
   ```

### 🆕 الوكلاء المعروفين حالياً:

#### 🏺 الفريق الأساسي (محلي):
- ⚡ **THOTH** - المحلل السريع (phi3:mini)
- 🔧 **PTAH** - المطور الخبير (mistral:7b)
- 🎯 **RA** - المستشار الاستراتيجي (llama3:8b)
- 💡 **KHNUM** - المبدع والمبتكر (strikegpt-r1-zero-8b)
- 👁️ **SESHAT** - المحللة البصرية (Qwen2.5-VL-7B)

#### 🌐 الفريق المتقدم (سحابي):
- 🔐 **ANUBIS** - حارس الأمان السيبراني (qwen/qwen-3-coder)
- ⚖️ **MAAT** - حارسة العدالة والأخلاقيات (gpt-4-turbo)
- 📊 **HAPI** - محلل البيانات والإحصائيات (gemini-pro)

#### ❓ وكلاء للفحص:
- 🌐 **web_research_agent** - وكيل البحث على الويب (يحتاج فحص)

---

## 📚 قواعد التوثيق

### 📝 تحديث ملفات README:

#### 🔄 عند اكتشاف وكلاء جدد:
1. **تحديث README الرئيسي للمشروع**
2. **إضافة معلومات الوكيل الجديد**
3. **تحديث الإحصائيات**
4. **إضافة أمثلة الاستخدام**

#### 📊 معلومات مطلوبة لكل وكيل:
```markdown
#### 🔍 AGENT_NAME - الوصف
- **النموذج**: `model_name`
- **الرمز**: 🔍
- **التخصص**: التخصص الرئيسي
- **القدرات**: قائمة القدرات
- **الملف**: `path/to/agent.py`
- **الحالة**: 🟢 نشط ومطور (XXX سطر)
```

---

## 🔧 قواعد استخدام الأدوات

### 🤖 استدعاء Gemini CLI:
```python
# استخدام Google AI Studio API
api_key = "[GOOGLE_API_KEY]"
url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key={api_key}"
```

### 𓅃 استدعاء فريق حورس:
```python
# تشغيل مساعد حورس
horus_path = "HORUS_AI_TEAM/summon_horus_assistant.py"
subprocess.run([sys.executable, horus_path])
```

---

## 📊 قواعد التقارير

### 📋 أنواع التقارير المطلوبة:

#### 1. تقرير التحليل الشامل:
- **المسار**: `PROJECT/05_analysis/reports/comprehensive_analysis_YYYYMMDD.md`
- **المحتوى**: تحليل شامل للمشروع
- **التنسيق**: Markdown مع جداول وإحصائيات

#### 2. تقرير JSON التقني:
- **المسار**: `PROJECT/05_analysis/reports/technical_report_YYYYMMDD.json`
- **المحتوى**: بيانات تقنية مفصلة
- **التنسيق**: JSON منظم

#### 3. ملخص النجاح:
- **المسار**: `PROJECT/06_documentation/success_summary_YYYYMMDD.md`
- **المحتوى**: ملخص الإنجازات والنتائج
- **التنسيق**: Markdown جذاب مع شارات

---

## 🎯 قواعد الجودة

### ✅ معايير الجودة المطلوبة:

#### 📊 للتحليل:
- **الشمولية**: فحص جميع الجوانب
- **الدقة**: معلومات صحيحة ومحدثة
- **الوضوح**: تقارير واضحة ومفهومة
- **التنظيم**: هيكل منطقي ومنظم

#### 🤖 للوكلاء:
- **الوظائف**: جميع الدوال تعمل بشكل صحيح
- **التوثيق**: تعليقات وتوثيق شامل
- **التكامل**: يعمل مع باقي النظام
- **الأداء**: استجابة سريعة وفعالة

---

## 🚨 تحذيرات مهمة

### ❌ ممنوع تماماً:
1. **إنشاء ملفات في المجلد الرئيسي** عند فحص مشروع فرعي
2. **خلط ملفات مشاريع مختلفة** في مجلد واحد
3. **استخدام أسماء ملفات مكررة** أو غير واضحة
4. **ترك ملفات مؤقتة** بدون تنظيف
5. **تجاهل تحديث التوثيق** بعد التغييرات

### ⚠️ تحذيرات:
1. **دائماً تحقق من وجود المجلدات** قبل إنشاء الملفات
2. **استخدم التواريخ في أسماء الملفات** للتتبع
3. **احفظ نسخ احتياطية** قبل التعديلات الكبيرة
4. **اختبر الوكلاء الجدد** قبل إضافتهم للتوثيق

---

## 📞 المراجع والمساعدة

### 🔗 ملفات مرجعية مهمة:
- `PROJECT_STRUCTURE_DETAILED.md` - هيكل المشروع المفصل
- `PROJECT_PATHS_DIRECTORY.md` - دليل المسارات
- `HORUS_AI_TEAM/README.md` - دليل فريق حورس
- `ANUBIS_SYSTEM/README.md` - دليل نظام أنوبيس

### 🆘 عند الحاجة للمساعدة:
1. **راجع هذا الملف أولاً**
2. **تحقق من التوثيق الموجود**
3. **استخدم أدوات التحليل المتاحة**
4. **اطلب مساعدة فريق حورس**

---

<div align="center">

**📋 هذه القواعد إلزامية لضمان جودة وتنظيم العمل**

![Follow Rules](https://img.shields.io/badge/📋-Follow%20Rules-red?style=for-the-badge)
![Quality First](https://img.shields.io/badge/🎯-Quality%20First-gold?style=for-the-badge)
![Organized Work](https://img.shields.io/badge/📁-Organized%20Work-blue?style=for-the-badge)

*آخر تحديث: 2025-01-27 | الإصدار: 1.0.0*

</div>
