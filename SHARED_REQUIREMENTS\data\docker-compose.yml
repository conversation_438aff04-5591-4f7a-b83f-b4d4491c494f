cat > ~/anubis-projects/docker-compose.yml << 'EOF'
version: '3.8'

networks:
  anubis-network:
    driver: bridge

services:
  nginx:
    image: nginx:alpine
    container_name: nginx-proxy
    ports:
      - "80:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - anubis-system
      - horus-team
      - anubis-mcp
    networks:
      - anubis-network
    restart: unless-stopped

  anubis-system:
    image: python:3.11-slim
    container_name: anubis-system
    expose:
      - "8000"
    command: >
      bash -c "
        pip install fastapi uvicorn &&
        python -c \"
from fastapi import FastAPI
import uvicorn

app = FastAPI(title='ANUBIS SYSTEM', description='نظام أنوبيس للذكاء الاصطناعي')

@app.get('/')
def read_root():
    return {
        'message': 'مرحباً بك في نظام أنوبيس 🏺',
        'status': 'يعمل بنجاح ✅',
        'services': ['AI Models', 'Database', 'API', 'Security'],
        'version': '2.0',
        'models': ['phi3:mini', 'mistral:7b', 'llama3:8b'],
        'agents': 8,
        'database': 'MySQL Connected',
        'cache': 'Redis Active'
    }

@app.get('/models')
def get_models():
    return {
        'local_models': [
            {'name': 'phi3:mini', 'status': 'available', 'size': '2.2GB'},
            {'name': 'mistral:7b', 'status': 'available', 'size': '4.1GB'},
            {'name': 'llama3:8b', 'status': 'available', 'size': '4.7GB'}
        ],
        'cloud_models': [
            {'name': 'gemini-pro', 'status': 'available'},
            {'name': 'gpt-4', 'status': 'available'},
            {'name': 'claude-3', 'status': 'available'}
        ]
    }

if __name__ == '__main__':
    uvicorn.run(app, host='0.0.0.0', port=8000)
        \"
      "
    networks:
      - anubis-network
    restart: unless-stopped

  horus-team:
    image: python:3.11-slim
    container_name: horus-team
    expose:
      - "7000"
    command: >
      bash -c "
        pip install fastapi uvicorn &&
        python -c \"
from fastapi import FastAPI
import uvicorn

app = FastAPI(title='HORUS AI TEAM', description='فريق حورس للذكاء الاصطناعي')

agents = [
    {'name': 'THOTH', 'role': 'المحلل السريع', 'model': 'phi3:mini', 'status': 'active'},
    {'name': 'PTAH', 'role': 'المطور الخبير', 'model': 'mistral:7b', 'status': 'active'},
    {'name': 'RA', 'role': 'المستشار الاستراتيجي', 'model': 'llama3:8b', 'status': 'active'},
    {'name': 'ANUBIS', 'role': 'حارس الأمان', 'model': 'claude-3', 'status': 'active'},
    {'name': 'MAAT', 'role': 'حارسة العدالة', 'model': 'gpt-4', 'status': 'active'},
    {'name': 'HAPI', 'role': 'محلل البيانات', 'model': 'gemini-pro', 'status': 'active'},
    {'name': 'SESHAT', 'role': 'المحللة البصرية', 'model': 'qwen2.5-vl', 'status': 'active'},
    {'name': 'HORUS', 'role': 'المنسق الأعلى', 'model': 'gemini-pro', 'status': 'active'}
]

@app.get('/')
def read_root():
    return {
        'message': 'مرحباً بك في فريق حورس 𓅃',
        'team_size': len(agents),
        'status': 'جميع الوكلاء نشطين ✅',
        'capabilities': ['تحليل', 'برمجة', 'استراتيجية', 'أمان', 'عدالة', 'بيانات', 'رؤية', 'تنسيق']
    }

@app.get('/agents')
def get_agents():
    return {'agents': agents, 'total': len(agents)}

if __name__ == '__main__':
    uvicorn.run(app, host='0.0.0.0', port=7000)
        \"
      "
    networks:
      - anubis-network
    restart: unless-stopped

  anubis-mcp:
    image: node:18-slim
    container_name: anubis-mcp
    expose:
      - "3000"
    command: >
      bash -c "
        npm init -y &&
        npm install express &&
        node -e \"
const express = require('express');
const app = express();

app.get('/', (req, res) => {
  res.json({
    message: 'مرحباً بك في نظام MCP 🔗',
    system: 'ANUBIS_HORUS_MCP',
    status: 'يعمل بنجاح ✅',
    api_keys: 726,
    tools: ['API Key Management', 'Model Router', 'Context Manager']
  });
});

app.listen(3000, '0.0.0.0', () => {
  console.log('ANUBIS_HORUS_MCP running on port 3000');
});
        \"
      "
    networks:
      - anubis-network
    restart: unless-stopped
EOF