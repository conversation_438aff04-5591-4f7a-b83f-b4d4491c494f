runtime: custom
env: flex

service: n8n-automation

automatic_scaling:
  min_num_instances: 1
  max_num_instances: 3
  cool_down_period: 120s
  cpu_utilization:
    target_utilization: 0.6

resources:
  cpu: 1
  memory_gb: 2
  disk_size_gb: 10

network:
  forwarded_ports:
    - 5678

env_variables:
  N8N_HOST: "0.0.0.0"
  N8N_PORT: "5678"
  N8N_PROTOCOL: "https"
  NODE_ENV: "production"
  N8N_BASIC_AUTH_ACTIVE: "true"
  N8N_BASIC_AUTH_USER: "admin"
  N8N_BASIC_AUTH_PASSWORD: "anubis_n8n_2025"
