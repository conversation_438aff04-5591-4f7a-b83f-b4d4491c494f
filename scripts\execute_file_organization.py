#!/usr/bin/env python3
"""
🗂️ تنفيذ خطة تنظيم الملفات - فريق حورس
تنظيم الملفات المتناثرة تلقائياً
"""

from HORUS_PROJECT_ANALYZER import HorusProjectAnalyzer

def main():
    print("🗂️ تنفيذ خطة تنظيم الملفات - فريق حورس")
    print("="*60)
    
    analyzer = HorusProjectAnalyzer()
    
    # تشغيل التحليل وتنفيذ التنظيم
    print("🔍 إجراء تحليل سريع...")
    analyzer.analyze_project_structure()
    analyzer.categorize_loose_files()
    
    print("\n📁 تنفيذ خطة التنظيم...")
    print("⚠️ سيتم نقل الملفات إلى مجلداتها المناسبة")
    
    # تأكيد من المستخدم
    confirm = input("\n❓ هل تريد المتابعة؟ (y/N): ")
    
    if confirm.lower() in ['y', 'yes', 'نعم']:
        # تنفيذ التنظيم
        organization_results = analyzer.organize_files(dry_run=False)
        
        print(f"\n✅ تم نقل {len(organization_results['moved_files'])} ملف بنجاح")
        if organization_results['failed_moves']:
            print(f"❌ فشل في نقل {len(organization_results['failed_moves'])} ملف")
        
        # إنشاء تقرير نهائي
        analyzer.generate_comprehensive_report()
        print("\n🎉 تم إكمال تنظيم الملفات بنجاح!")
        
    else:
        print("❌ تم إلغاء العملية")

if __name__ == "__main__":
    main()
