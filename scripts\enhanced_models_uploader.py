#!/usr/bin/env python3
"""
🚀 Enhanced Models Uploader - رفع النماذج المحسن
رفع نماذج Ollama إلى Google Cloud Storage مع تتبع التقدم
"""

import os
import subprocess
import json
import time
import threading
from pathlib import Path
from datetime import datetime

class EnhancedModelsUploader:
    def __init__(self):
        self.project_id = "universal-ai-assistants-2025"
        self.bucket_name = "universal-ai-models-2025-storage"
        self.ollama_models_path = Path.home() / ".ollama" / "models"
        self.upload_log = []
        self.total_size = 0
        self.uploaded_size = 0
        
    def log_message(self, message, level="INFO"):
        """تسجيل الرسائل مع الوقت"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}"
        print(log_entry)
        self.upload_log.append(log_entry)
    
    def check_prerequisites(self):
        """فحص المتطلبات الأساسية"""
        self.log_message("🔍 فحص المتطلبات الأساسية...")
        
        # فحص Google Cloud CLI
        try:
            result = subprocess.run("gcloud --version", shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                self.log_message("✅ Google Cloud CLI متوفر")
            else:
                self.log_message("❌ Google Cloud CLI غير متوفر", "ERROR")
                return False
        except Exception as e:
            self.log_message(f"❌ خطأ في فحص Google Cloud CLI: {e}", "ERROR")
            return False
        
        # فحص المصادقة
        try:
            result = subprocess.run("gcloud auth list --filter=status:ACTIVE --format='value(account)'",
                                  shell=True, capture_output=True, text=True)
            if result.returncode == 0 and result.stdout.strip():
                accounts = result.stdout.strip().split('\n')
                active_account = accounts[0] if accounts else ""
                self.log_message(f"✅ مسجل الدخول كـ: {active_account}")
            else:
                # محاولة أخرى بطريقة مختلفة
                result2 = subprocess.run("gcloud config get-value account",
                                       shell=True, capture_output=True, text=True)
                if result2.returncode == 0 and result2.stdout.strip():
                    self.log_message(f"✅ مسجل الدخول كـ: {result2.stdout.strip()}")
                else:
                    self.log_message("❌ غير مسجل الدخول في Google Cloud", "ERROR")
                    return False
        except Exception as e:
            self.log_message(f"❌ خطأ في فحص المصادقة: {e}", "ERROR")
            return False
        
        # فحص Ollama
        try:
            result = subprocess.run("ollama list", shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                self.log_message("✅ Ollama متوفر")
            else:
                self.log_message("❌ Ollama غير متوفر", "ERROR")
                return False
        except Exception as e:
            self.log_message(f"❌ خطأ في فحص Ollama: {e}", "ERROR")
            return False
        
        return True
    
    def create_bucket_if_needed(self):
        """إنشاء bucket إذا لم يكن موجوداً"""
        self.log_message("🪣 التحقق من وجود bucket...")
        
        try:
            # فحص وجود bucket
            result = subprocess.run(f"gsutil ls gs://{self.bucket_name}/", 
                                  shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.log_message(f"✅ Bucket موجود: gs://{self.bucket_name}")
            else:
                # إنشاء bucket
                self.log_message(f"📦 إنشاء bucket جديد: {self.bucket_name}")
                create_cmd = f"gsutil mb -p {self.project_id} -l us-central1 gs://{self.bucket_name}"
                result = subprocess.run(create_cmd, shell=True, capture_output=True, text=True)
                
                if result.returncode == 0:
                    self.log_message(f"✅ تم إنشاء bucket بنجاح")
                else:
                    self.log_message(f"❌ فشل إنشاء bucket: {result.stderr}", "ERROR")
                    return False
            
            return True
            
        except Exception as e:
            self.log_message(f"❌ خطأ في إدارة bucket: {e}", "ERROR")
            return False
    
    def get_local_models(self):
        """الحصول على قائمة النماذج المحلية"""
        self.log_message("📋 جمع معلومات النماذج المحلية...")
        
        try:
            result = subprocess.run("ollama list", shell=True, capture_output=True, text=True)
            if result.returncode != 0:
                self.log_message("❌ فشل في الحصول على قائمة النماذج", "ERROR")
                return []
            
            lines = result.stdout.strip().split('\n')[1:]  # تجاهل header
            models = []
            
            for line in lines:
                if line.strip():
                    parts = line.split()
                    if len(parts) >= 3:
                        model_name = parts[0]
                        model_id = parts[1]
                        size_str = parts[2]
                        modified = " ".join(parts[3:]) if len(parts) > 3 else "unknown"
                        
                        # تحويل الحجم إلى bytes
                        size_bytes = self.parse_size(size_str)
                        
                        models.append({
                            "name": model_name,
                            "id": model_id,
                            "size_str": size_str,
                            "size_bytes": size_bytes,
                            "modified": modified
                        })
                        
                        self.total_size += size_bytes
            
            self.log_message(f"✅ تم العثور على {len(models)} نموذج (إجمالي: {self.format_size(self.total_size)})")
            return models
            
        except Exception as e:
            self.log_message(f"❌ خطأ في جمع معلومات النماذج: {e}", "ERROR")
            return []
    
    def parse_size(self, size_str):
        """تحويل حجم النموذج إلى bytes"""
        try:
            if 'GB' in size_str:
                return float(size_str.replace('GB', '').strip()) * 1024 * 1024 * 1024
            elif 'MB' in size_str:
                return float(size_str.replace('MB', '').strip()) * 1024 * 1024
            elif 'KB' in size_str:
                return float(size_str.replace('KB', '').strip()) * 1024
            else:
                return float(size_str.replace('B', '').strip())
        except:
            return 0
    
    def format_size(self, size_bytes):
        """تنسيق حجم الملف"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} PB"
    
    def find_model_files(self, model_name, model_id):
        """البحث عن ملفات النموذج في نظام الملفات"""
        model_files = []
        
        # البحث في مجلد .ollama/models
        if self.ollama_models_path.exists():
            # البحث بناءً على model_id
            for root, dirs, files in os.walk(self.ollama_models_path):
                for file in files:
                    file_path = Path(root) / file
                    # البحث عن ملفات تحتوي على model_id أو اسم النموذج
                    if model_id in str(file_path) or any(part in str(file_path) for part in model_name.split(':')):
                        if file_path.is_file() and file_path.stat().st_size > 1024:  # أكبر من 1KB
                            model_files.append(file_path)
        
        return model_files
    
    def upload_model_files(self, model, model_files):
        """رفع ملفات نموذج واحد"""
        model_name = model["name"]
        self.log_message(f"📤 بدء رفع النموذج: {model_name}")
        
        uploaded_files = []
        failed_files = []
        
        for file_path in model_files:
            try:
                # إنشاء مسار في Cloud Storage
                relative_path = file_path.relative_to(self.ollama_models_path)
                cloud_path = f"gs://{self.bucket_name}/models/{model_name}/{relative_path}"
                
                self.log_message(f"   📁 رفع: {file_path.name} ({self.format_size(file_path.stat().st_size)})")
                
                # رفع الملف
                cmd = f"gsutil cp '{file_path}' '{cloud_path}'"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                
                if result.returncode == 0:
                    uploaded_files.append(str(file_path))
                    self.uploaded_size += file_path.stat().st_size
                    self.log_message(f"   ✅ تم رفع: {file_path.name}")
                else:
                    failed_files.append(str(file_path))
                    self.log_message(f"   ❌ فشل رفع: {file_path.name} - {result.stderr}", "ERROR")
                    
            except Exception as e:
                failed_files.append(str(file_path))
                self.log_message(f"   ❌ خطأ في رفع {file_path.name}: {e}", "ERROR")
        
        return uploaded_files, failed_files
    
    def create_metadata(self, models, upload_results):
        """إنشاء ملف metadata للنماذج"""
        self.log_message("📋 إنشاء ملف metadata...")
        
        metadata = {
            "created_at": datetime.now().isoformat(),
            "total_models": len(models),
            "total_size_bytes": self.total_size,
            "total_size_human": self.format_size(self.total_size),
            "uploaded_size_bytes": self.uploaded_size,
            "uploaded_size_human": self.format_size(self.uploaded_size),
            "models": []
        }
        
        for model in models:
            model_metadata = {
                "name": model["name"],
                "id": model["id"],
                "size_str": model["size_str"],
                "size_bytes": model["size_bytes"],
                "modified": model["modified"],
                "upload_status": upload_results.get(model["name"], {}).get("status", "failed"),
                "uploaded_files": upload_results.get(model["name"], {}).get("uploaded_files", []),
                "failed_files": upload_results.get(model["name"], {}).get("failed_files", [])
            }
            metadata["models"].append(model_metadata)
        
        # حفظ metadata محلياً
        metadata_file = "models_metadata.json"
        with open(metadata_file, "w", encoding="utf-8") as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        # رفع metadata إلى Cloud Storage
        try:
            cmd = f"gsutil cp {metadata_file} gs://{self.bucket_name}/metadata/"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.log_message("✅ تم رفع ملف metadata بنجاح")
            else:
                self.log_message(f"❌ فشل رفع metadata: {result.stderr}", "ERROR")
                
        except Exception as e:
            self.log_message(f"❌ خطأ في رفع metadata: {e}", "ERROR")
        
        return metadata
    
    def run_upload(self):
        """تشغيل عملية الرفع الكاملة"""
        start_time = time.time()
        self.log_message("🚀 بدء عملية رفع النماذج إلى Google Cloud Storage")
        self.log_message("=" * 60)
        
        # فحص المتطلبات
        if not self.check_prerequisites():
            self.log_message("❌ فشل في فحص المتطلبات الأساسية", "ERROR")
            return False
        
        # إنشاء bucket
        if not self.create_bucket_if_needed():
            self.log_message("❌ فشل في إعداد bucket", "ERROR")
            return False
        
        # الحصول على النماذج
        models = self.get_local_models()
        if not models:
            self.log_message("❌ لم يتم العثور على نماذج للرفع", "ERROR")
            return False
        
        # رفع النماذج
        upload_results = {}
        
        for i, model in enumerate(models, 1):
            self.log_message(f"\n📦 النموذج {i}/{len(models)}: {model['name']}")
            
            # البحث عن ملفات النموذج
            model_files = self.find_model_files(model["name"], model["id"])
            
            if not model_files:
                self.log_message(f"❌ لم يتم العثور على ملفات للنموذج: {model['name']}", "ERROR")
                upload_results[model["name"]] = {
                    "status": "failed",
                    "uploaded_files": [],
                    "failed_files": [],
                    "error": "No files found"
                }
                continue
            
            # رفع ملفات النموذج
            uploaded_files, failed_files = self.upload_model_files(model, model_files)
            
            upload_results[model["name"]] = {
                "status": "success" if uploaded_files and not failed_files else "partial" if uploaded_files else "failed",
                "uploaded_files": uploaded_files,
                "failed_files": failed_files
            }
            
            # عرض التقدم
            progress = (self.uploaded_size / self.total_size) * 100 if self.total_size > 0 else 0
            self.log_message(f"📊 التقدم الإجمالي: {progress:.1f}% ({self.format_size(self.uploaded_size)}/{self.format_size(self.total_size)})")
        
        # إنشاء metadata
        metadata = self.create_metadata(models, upload_results)
        
        # ملخص النتائج
        end_time = time.time()
        duration = end_time - start_time
        
        self.log_message("\n" + "=" * 60)
        self.log_message("📊 ملخص عملية الرفع")
        self.log_message("=" * 60)
        
        successful_models = sum(1 for result in upload_results.values() if result["status"] == "success")
        partial_models = sum(1 for result in upload_results.values() if result["status"] == "partial")
        failed_models = sum(1 for result in upload_results.values() if result["status"] == "failed")
        
        self.log_message(f"⏱️ المدة الإجمالية: {duration/60:.1f} دقيقة")
        self.log_message(f"✅ نماذج مرفوعة بالكامل: {successful_models}")
        self.log_message(f"⚠️ نماذج مرفوعة جزئياً: {partial_models}")
        self.log_message(f"❌ نماذج فاشلة: {failed_models}")
        self.log_message(f"📊 البيانات المرفوعة: {self.format_size(self.uploaded_size)}/{self.format_size(self.total_size)}")
        
        # حفظ سجل الرفع
        log_file = f"upload_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(log_file, "w", encoding="utf-8") as f:
            f.write("\n".join(self.upload_log))
        
        self.log_message(f"📄 تم حفظ سجل الرفع في: {log_file}")
        
        return successful_models > 0 or partial_models > 0

if __name__ == "__main__":
    uploader = EnhancedModelsUploader()
    success = uploader.run_upload()
    
    if success:
        print("\n🎉 تمت عملية الرفع بنجاح!")
    else:
        print("\n❌ فشلت عملية الرفع!")
