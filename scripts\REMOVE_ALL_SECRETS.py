#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔐 أداة إزالة الأسرار التلقائية
===============================

أداة متقدمة لإزالة جميع الأسرار والمفاتيح من المشروع تلقائياً
لضمان الأمان قبل رفع المشروع على GitHub.

📅 تاريخ الإنشاء: 2025-07-29
🔧 الإصدار: 1.0.0
"""

import os
import re
import shutil
from datetime import datetime
from pathlib import Path

class SecretRemover:
    def __init__(self):
        self.project_root = os.getcwd()
        self.backup_dir = f"secrets_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.removed_count = 0
        self.files_modified = []
        
        # أنماط الأسرار للاستبدال
        self.secret_replacements = {
            # مفاتيح OpenRouter
            r'sk-or-v1-[a-zA-Z0-9]{64}': '[OPENROUTER_API_KEY]',
            r'sk-or-v1-[a-zA-Z0-9_-]+': '[OPENROUTER_API_KEY]',
            
            # مفاتيح Google
            r'AIza[0-9A-Za-z_-]{35}': '[GOOGLE_API_KEY]',
            r'AIza[0-9A-Za-z_-]+': '[GOOGLE_API_KEY]',
            
            # كلمات المرور المحددة
            r'"password":\s*"[DATABASE_PASSWORD]"': '"password": "[DATABASE_PASSWORD]"',
            r"'password':\s*'[DATABASE_PASSWORD]'": "'password': '[DATABASE_PASSWORD]'",
            r'password = "[DATABASE_PASSWORD]"': 'password = "[DATABASE_PASSWORD]"',
            r'[DATABASE_PASSWORD]': '[DATABASE_PASSWORD]',
            
            # كلمات مرور Docker
            r'[MYSQL_ROOT_PASSWORD]': '[MYSQL_ROOT_PASSWORD]',
            r'[MYSQL_PASSWORD]': '[MYSQL_PASSWORD]',
            r'[TEST_PASSWORD]': '[TEST_PASSWORD]',
            
            # أنماط عامة للأسرار
            r'"key":\s*"sk-[^"]*"': '"key": "[API_KEY]"',
            r"'key':\s*'sk-[^']*'": "'key': '[API_KEY]'",
            r'"key":\s*"AIza[^"]*"': '"key": "[GOOGLE_API_KEY]"',
            r"'key':\s*'AIza[^']*'": "'key': '[GOOGLE_API_KEY]'",
        }
        
        # ملفات للحذف الكامل (تحتوي على أسرار فقط)
        self.files_to_delete = [
            'ANUBIS_SYSTEM/tests/model_testers/GEMINI_MODELS_TESTER/test_new_gemini_keys.py',
            'ANUBIS_SYSTEM/tests/model_testers/OPENROUTER_FREE_MODELS_TESTER/openrouter_free_models_caller.py',
            'ANUBIS_SYSTEM/tests/model_testers/OPENROUTER_MODELS_TESTER/openrouter_caller.py',
        ]
        
        # مجلدات للحذف الكامل
        self.dirs_to_delete = [
            'ANUBIS_SYSTEM/tests/model_testers/GEMINI_MODELS_TESTER',
            'ANUBIS_SYSTEM/tests/model_testers/OPENROUTER_FREE_MODELS_TESTER',
            'ANUBIS_SYSTEM/tests/model_testers/OPENROUTER_MODELS_TESTER',
        ]
    
    def print_header(self):
        """طباعة رأس الأداة"""
        print("=" * 80)
        print("🔐 أداة إزالة الأسرار التلقائية")
        print("=" * 80)
        print(f"📅 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📁 المجلد: {self.project_root}")
        print(f"💾 نسخة احتياطية: {self.backup_dir}")
        print("=" * 80)
    
    def create_backup(self):
        """إنشاء نسخة احتياطية من الملفات المتأثرة"""
        print("\n💾 إنشاء نسخة احتياطية...")
        
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
        
        # نسخ الملفات المتأثرة
        files_to_backup = []
        
        # البحث عن الملفات التي تحتوي على أسرار
        for root, dirs, files in os.walk(self.project_root):
            # تجاهل مجلدات معينة
            dirs[:] = [d for d in dirs if d not in ['.git', '__pycache__', 'node_modules', '.venv', self.backup_dir]]
            
            for file in files:
                if file.endswith(('.py', '.js', '.json', '.txt', '.md', '.env')):
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, self.project_root)
                    
                    # فحص إذا كان الملف يحتوي على أسرار
                    if self.file_contains_secrets(file_path):
                        files_to_backup.append((file_path, rel_path))
        
        # نسخ الملفات
        for file_path, rel_path in files_to_backup:
            backup_path = os.path.join(self.backup_dir, rel_path)
            backup_dir = os.path.dirname(backup_path)
            
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir)
            
            shutil.copy2(file_path, backup_path)
        
        print(f"✅ تم نسخ {len(files_to_backup)} ملف احتياطياً")
        return len(files_to_backup)
    
    def file_contains_secrets(self, file_path):
        """فحص إذا كان الملف يحتوي على أسرار"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # فحص الأنماط
            for pattern in self.secret_replacements.keys():
                if re.search(pattern, content, re.IGNORECASE):
                    return True
            
            return False
        except:
            return False
    
    def remove_secrets_from_file(self, file_path):
        """إزالة الأسرار من ملف واحد"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            original_content = content
            secrets_found = 0
            
            # تطبيق جميع الاستبدالات
            for pattern, replacement in self.secret_replacements.items():
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    secrets_found += len(matches)
                    content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
            
            # حفظ الملف إذا تم تعديله
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                rel_path = os.path.relpath(file_path, self.project_root)
                self.files_modified.append(rel_path)
                self.removed_count += secrets_found
                
                print(f"   🔧 {rel_path}: {secrets_found} سر تم إزالته")
                return secrets_found
            
            return 0
        except Exception as e:
            print(f"   ❌ خطأ في {file_path}: {str(e)}")
            return 0
    
    def delete_secret_files(self):
        """حذف الملفات التي تحتوي على أسرار فقط"""
        print("\n🗑️ حذف ملفات الأسرار...")
        
        deleted_files = 0
        deleted_dirs = 0
        
        # حذف الملفات المحددة
        for file_path in self.files_to_delete:
            full_path = os.path.join(self.project_root, file_path)
            if os.path.exists(full_path):
                os.remove(full_path)
                deleted_files += 1
                print(f"   🗑️ حذف ملف: {file_path}")
        
        # حذف المجلدات المحددة
        for dir_path in self.dirs_to_delete:
            full_path = os.path.join(self.project_root, dir_path)
            if os.path.exists(full_path):
                shutil.rmtree(full_path)
                deleted_dirs += 1
                print(f"   🗑️ حذف مجلد: {dir_path}")
        
        print(f"✅ تم حذف {deleted_files} ملف و {deleted_dirs} مجلد")
        return deleted_files, deleted_dirs
    
    def process_all_files(self):
        """معالجة جميع الملفات في المشروع"""
        print("\n🔍 معالجة الملفات...")
        
        processed_files = 0
        
        for root, dirs, files in os.walk(self.project_root):
            # تجاهل مجلدات معينة
            dirs[:] = [d for d in dirs if d not in ['.git', '__pycache__', 'node_modules', '.venv', self.backup_dir]]
            
            for file in files:
                if file.endswith(('.py', '.js', '.json', '.txt', '.md', '.env')):
                    file_path = os.path.join(root, file)
                    
                    # تجاهل الملفات المحذوفة
                    rel_path = os.path.relpath(file_path, self.project_root)
                    if any(rel_path.startswith(deleted_path) for deleted_path in self.files_to_delete + self.dirs_to_delete):
                        continue
                    
                    secrets_removed = self.remove_secrets_from_file(file_path)
                    if secrets_removed > 0:
                        processed_files += 1
        
        print(f"✅ تم معالجة {processed_files} ملف")
        return processed_files
    
    def update_gitignore(self):
        """تحديث ملف .gitignore لحماية الأسرار"""
        print("\n🛡️ تحديث .gitignore...")
        
        gitignore_path = os.path.join(self.project_root, '.gitignore')
        
        # قواعد حماية الأسرار
        security_rules = """
# 🔐 حماية الأسرار والمفاتيح الحساسة
**/*api_key*
**/*secret*
**/*password*
**/*token*
**/test_*_keys.py
**/model_testers/**/*.py
**/*_caller.py
.env
.env.local
.env.production
secrets_backup_*/
api_keys_vault/
**/api_keys_collection.json

# 🔑 مفاتيح API محددة
**/*openrouter*
**/*gemini*
**/*claude*
**/*openai*

# 🗄️ قواعد البيانات الحساسة
**/*.db
**/*.sqlite
**/*.sqlite3
**/database_config.json
"""
        
        try:
            # قراءة .gitignore الحالي
            if os.path.exists(gitignore_path):
                with open(gitignore_path, 'r', encoding='utf-8') as f:
                    current_content = f.read()
            else:
                current_content = ""
            
            # إضافة القواعد الجديدة إذا لم تكن موجودة
            if "# 🔐 حماية الأسرار والمفاتيح الحساسة" not in current_content:
                with open(gitignore_path, 'a', encoding='utf-8') as f:
                    f.write(security_rules)
                print("✅ تم تحديث .gitignore بقواعد الأمان")
            else:
                print("✅ .gitignore يحتوي على قواعد الأمان بالفعل")
                
        except Exception as e:
            print(f"❌ خطأ في تحديث .gitignore: {str(e)}")
    
    def create_env_template(self):
        """إنشاء ملف .env.template"""
        print("\n📝 إنشاء .env.template...")
        
        env_template = """# 🔐 ملف متغيرات البيئة - Environment Variables Template
# انسخ هذا الملف إلى .env وأضف القيم الفعلية

# 🔑 مفاتيح API - API Keys
OPENROUTER_API_KEY=your_openrouter_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
OPENAI_API_KEY=your_openai_api_key_here

# 🗄️ قاعدة البيانات - Database
DATABASE_PASSWORD=your_database_password_here
MYSQL_ROOT_PASSWORD=your_mysql_root_password_here
MYSQL_PASSWORD=your_mysql_password_here

# 🧪 الاختبار - Testing
TEST_PASSWORD=your_test_password_here

# ⚠️ تحذير: لا تشارك هذا الملف أو ترفعه على GitHub!
# Warning: Do not share this file or upload it to GitHub!
"""
        
        template_path = os.path.join(self.project_root, '.env.template')
        
        try:
            with open(template_path, 'w', encoding='utf-8') as f:
                f.write(env_template)
            print("✅ تم إنشاء .env.template")
        except Exception as e:
            print(f"❌ خطأ في إنشاء .env.template: {str(e)}")
    
    def generate_report(self):
        """إنشاء تقرير نهائي"""
        print("\n📊 إنشاء التقرير النهائي...")
        
        report = f"""# 🔐 تقرير إزالة الأسرار - Secrets Removal Report

## 📊 ملخص العملية
- **📅 التاريخ**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **🔢 الأسرار المُزالة**: {self.removed_count}
- **📁 الملفات المُعدلة**: {len(self.files_modified)}
- **💾 النسخة الاحتياطية**: {self.backup_dir}

## 📁 الملفات المُعدلة
{chr(10).join(f'- {file}' for file in self.files_modified)}

## ✅ الإجراءات المكتملة
- [x] إنشاء نسخة احتياطية
- [x] إزالة مفاتيح API
- [x] إزالة كلمات المرور
- [x] حذف ملفات الأسرار
- [x] تحديث .gitignore
- [x] إنشاء .env.template

## 🎯 الخطوات التالية
1. مراجعة الملفات المُعدلة
2. اختبار النظام محلياً
3. رفع المشروع على GitHub

---
تم إنشاء هذا التقرير تلقائياً بواسطة أداة إزالة الأسرار
"""
        
        report_path = f"secrets_removal_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"✅ تم حفظ التقرير في: {report_path}")
        except Exception as e:
            print(f"❌ خطأ في حفظ التقرير: {str(e)}")
    
    def run_removal_process(self):
        """تشغيل عملية إزالة الأسرار الكاملة"""
        self.print_header()
        
        # 1. إنشاء نسخة احتياطية
        backup_count = self.create_backup()
        
        # 2. حذف ملفات الأسرار
        deleted_files, deleted_dirs = self.delete_secret_files()
        
        # 3. معالجة الملفات المتبقية
        processed_files = self.process_all_files()
        
        # 4. تحديث .gitignore
        self.update_gitignore()
        
        # 5. إنشاء .env.template
        self.create_env_template()
        
        # 6. إنشاء التقرير
        self.generate_report()
        
        # النتائج النهائية
        print("\n" + "=" * 80)
        print("🎉 تم إكمال عملية إزالة الأسرار بنجاح!")
        print("=" * 80)
        print(f"📊 الإحصائيات النهائية:")
        print(f"   🔢 الأسرار المُزالة: {self.removed_count}")
        print(f"   📁 الملفات المُعدلة: {len(self.files_modified)}")
        print(f"   🗑️ الملفات المحذوفة: {deleted_files}")
        print(f"   📂 المجلدات المحذوفة: {deleted_dirs}")
        print(f"   💾 النسخة الاحتياطية: {backup_count} ملف")
        print("\n✅ المشروع الآن آمن للرفع على GitHub!")
        print("=" * 80)

def main():
    """الدالة الرئيسية"""
    remover = SecretRemover()
    remover.run_removal_process()

if __name__ == "__main__":
    main()
