# 🗂️ دليل مسارات مشروع أنوبيس المتكامل

<div align="center">

![Paths Directory](https://img.shields.io/badge/🗂️-Paths%20Directory-blue?style=for-the-badge)
![Navigation](https://img.shields.io/badge/🧭-Easy%20Navigation-green?style=for-the-badge)
![Complete Guide](https://img.shields.io/badge/📍-Complete%20Guide-gold?style=for-the-badge)

**دليل شامل لجميع مسارات الملفات والمجلدات في المشروع**

</div>

---

## 📋 فهرس المسارات

- [🏠 المجلد الرئيسي](#-المجلد-الرئيسي)
- [🏺 مسارات نظام أنوبيس](#-مسارات-نظام-أنوبيس)
- [𓅃 مسارات فريق حورس](#-مسارات-فريق-حورس)
- [🔗 مسارات نظام MCP](#-مسارات-نظام-mcp)
- [📚 مسارات التوثيق](#-مسارات-التوثيق)
- [📊 مسارات البيانات](#-مسارات-البيانات)
- [🔧 مسارات الأدوات](#-مسارات-الأدوات)

---

## 🏠 المجلد الرئيسي

### 📍 المسار الأساسي: `c:\Users\<USER>\Universal-AI-Assistants\`

#### 📄 ملفات التشغيل الرئيسية:
```
📄 QUICK_START.py                           # تشغيل سريع
📄 LAUNCH_ANUBIS_COMPLETE.py               # مشغل شامل مع قائمة
📄 INTEGRATE_ALL_PROJECTS.py               # تكامل جميع المشاريع
📄 start_complete_anubis_system.py         # تشغيل النظام الكامل
📄 start_anubis_with_local_mysql.py        # تشغيل مع MySQL محلي
📄 README.md                               # دليل المشروع الرئيسي
📄 LICENSE                                 # ترخيص المشروع
```

---

## 🏺 مسارات نظام أنوبيس

### 📍 المسار الأساسي: `ANUBIS_SYSTEM/`

#### 🏗️ الملفات الأساسية:
```
📄 ANUBIS_SYSTEM/main.py                   # نقطة الدخول الرئيسية
📄 ANUBIS_SYSTEM/Dockerfile               # حاوية Docker
📄 ANUBIS_SYSTEM/docker-compose.yml       # تنسيق الخدمات
📄 ANUBIS_SYSTEM/README.md                # دليل النظام
📄 ANUBIS_SYSTEM/requirements.txt         # متطلبات Python
```

#### 📁 المجلدات الفرعية:
```
📁 ANUBIS_SYSTEM/src/                     # الكود المصدري
   ├── 📁 core/                          # المكونات الأساسية
   ├── 📁 api/                           # واجهات برمجة التطبيقات
   ├── 📁 models/                        # نماذج البيانات
   ├── 📁 services/                      # الخدمات
   └── 📁 utils/                         # الأدوات المساعدة

📁 ANUBIS_SYSTEM/config/                  # ملفات الإعدادات
   ├── 📄 default_config.json            # الإعدادات الافتراضية
   ├── 📄 ai_config.json                 # إعدادات الذكاء الاصطناعي
   └── 📄 database_config.json           # إعدادات قاعدة البيانات

📁 ANUBIS_SYSTEM/data/                    # بيانات النظام
📁 ANUBIS_SYSTEM/database/                # قاعدة البيانات
📁 ANUBIS_SYSTEM/logs/                    # سجلات النظام
📁 ANUBIS_SYSTEM/tests/                   # الاختبارات
📁 ANUBIS_SYSTEM/scripts/                 # سكريبتات النظام
📁 ANUBIS_SYSTEM/security/                # الأمان
📁 ANUBIS_SYSTEM/monitoring/              # المراقبة
📁 ANUBIS_SYSTEM/utilities/               # الأدوات
```

#### 🔧 ملفات مهمة:
```
📄 ANUBIS_SYSTEM/anubis_horus_bridge.py   # جسر التكامل مع حورس
📄 ANUBIS_SYSTEM/startup.py               # سكريبت البدء
📄 ANUBIS_SYSTEM/quick_start_anubis.py    # تشغيل سريع لأنوبيس
```

---

## 𓅃 مسارات فريق حورس

### 📍 المسار الأساسي: `HORUS_AI_TEAM/`

#### 🏗️ الهيكل المنظم (9 مجلدات):
```
📁 HORUS_AI_TEAM/01_core/                 # الأنظمة الأساسية
   ├── 📁 engines/                       # محركات التشغيل
   ├── 📁 interfaces/                    # الواجهات
   │   └── 📄 horus_interface.py         # الواجهة الرئيسية
   └── 📁 orchestration/                 # التنسيق

📁 HORUS_AI_TEAM/02_team_members/         # أعضاء الفريق
   ├── 📄 THOTH.json                     # المحلل السريع
   ├── 📄 PTAH.json                      # المطور الخبير
   ├── 📄 RA.json                        # المستشار الاستراتيجي
   ├── 📄 KHNUM.json                     # المبدع والمبتكر
   ├── 📄 SESHAT.json                    # المحللة البصرية
   ├── 📄 ANUBIS.json                    # حارس الأمان
   ├── 📄 MAAT.json                      # حارسة العدالة
   └── 📄 HAPI.json                      # محلل البيانات

📁 HORUS_AI_TEAM/03_memory_system/        # نظام الذاكرة
   ├── 📄 memory_manager.py              # مدير الذاكرة
   ├── 📄 pattern_analyzer.py            # محلل الأنماط
   └── 📄 knowledge_base.py              # قاعدة المعرفة

📁 HORUS_AI_TEAM/04_collaboration/        # التعاون
📁 HORUS_AI_TEAM/05_analysis/             # التحليل
   └── 📁 tools/                         # أدوات التحليل
📁 HORUS_AI_TEAM/06_documentation/        # التوثيق
📁 HORUS_AI_TEAM/07_configuration/        # الإعدادات
📁 HORUS_AI_TEAM/08_utilities/            # الأدوات
   └── 📁 tools/                         # أدوات مساعدة
📁 HORUS_AI_TEAM/09_archive/              # الأرشيف
```

#### 📄 ملفات رئيسية:
```
📄 HORUS_AI_TEAM/README.md                # دليل فريق حورس
📄 HORUS_AI_TEAM/Dockerfile              # حاوية Docker
📄 HORUS_AI_TEAM/summon_horus_assistant.py # استدعاء مساعد حورس
```

---

## 🔗 مسارات نظام MCP

### 📍 المسار الأساسي: `ANUBIS_HORUS_MCP/`

#### 🏗️ الهيكل التقني:
```
📄 ANUBIS_HORUS_MCP/package.json          # تبعيات Node.js
📄 ANUBIS_HORUS_MCP/Dockerfile           # حاوية MCP
📄 ANUBIS_HORUS_MCP/README.md            # دليل نظام MCP

📁 ANUBIS_HORUS_MCP/src/                  # الكود المصدري
   ├── 📄 index.js                       # نقطة الدخول
   ├── 📁 protocols/                     # البروتوكولات
   └── 📁 handlers/                      # معالجات الطلبات

📁 ANUBIS_HORUS_MCP/core/                 # المكونات الأساسية
   └── 📄 mcp_server.py                  # خادم MCP الرئيسي

📁 ANUBIS_HORUS_MCP/api_keys_vault/       # خزنة مفاتيح API
   ├── 📄 keys_manager.py                # مدير المفاتيح
   ├── 📄 security_implementation.py     # تنفيذ الأمان
   ├── 📄 visual_dashboard_system.py     # لوحة التحكم المرئية
   └── 📄 api_keys_collection.json       # مجموعة المفاتيح

📁 ANUBIS_HORUS_MCP/tools/                # الأدوات
   └── 📄 registry.py                    # سجل الأدوات

📁 ANUBIS_HORUS_MCP/config/               # الإعدادات
   └── 📄 mcp_config.json                # إعدادات MCP

📁 ANUBIS_HORUS_MCP/templates/            # القوالب
📁 ANUBIS_HORUS_MCP/static/               # الملفات الثابتة
```

#### 🔑 ملفات إدارة المفاتيح:
```
📄 ANUBIS_HORUS_MCP/api_keys_vault/key_rotation_system.py      # تدوير المفاتيح
📄 ANUBIS_HORUS_MCP/api_keys_vault/secure_backup_system.py     # النسخ الاحتياطية
📄 ANUBIS_HORUS_MCP/api_keys_vault/automated_management_system.py # الإدارة التلقائية
```

---

## 📚 مسارات التوثيق

### 📍 المسار الأساسي: `PROJECT_DOCUMENTATION/`

#### 📋 الملفات الرئيسية:
```
📄 PROJECT_DOCUMENTATION/README_COMPREHENSIVE.md              # الدليل الشامل
📄 PROJECT_DOCUMENTATION/USER_GUIDE_COMPLETE.md               # دليل المستخدم الكامل
📄 PROJECT_DOCUMENTATION/DEVELOPMENT_ROADMAP.md               # خطة التطوير
📄 PROJECT_DOCUMENTATION/PROJECT_STRUCTURE_DETAILED.md        # هيكل المشروع المفصل
📄 PROJECT_DOCUMENTATION/HOW_TO_USE_ANUBIS_SYSTEM.md         # كيفية استخدام أنوبيس
📄 PROJECT_DOCUMENTATION/DOCKER_SERVER_GUIDE.md              # دليل خادم Docker
```

#### 📁 مجلدات فرعية:
```
📁 PROJECT_DOCUMENTATION/reports_and_analysis/               # التقارير والتحليلات
📁 PROJECT_DOCUMENTATION/test_reports/                       # تقارير الاختبارات
📁 PROJECT_DOCUMENTATION/collaboration_logs/                 # سجلات التعاون
📁 PROJECT_DOCUMENTATION/project_summaries/                  # ملخصات المشاريع
```

---

## 📊 مسارات البيانات

### 📍 مسارات البيانات والسجلات:

#### 🗄️ البيانات:
```
📁 data/                                  # البيانات الرئيسية
   ├── 📄 *.json                         # ملفات البيانات JSON
   └── 📄 *_report_*.json                # تقارير البيانات

📁 SHARED_REQUIREMENTS/                   # المتطلبات المشتركة
   ├── 📁 data/                          # بيانات المتطلبات
   ├── 📁 tools/                         # أدوات الفحص
   ├── 📁 reports/                       # التقارير
   ├── 📁 docs/                          # التوثيق
   └── 📁 installers/                    # أدوات التثبيت
```

#### 📊 السجلات:
```
📁 logs/                                  # السجلات الرئيسية
📁 monitoring/                            # مراقبة النظام
   └── 📄 prometheus.yml                  # إعدادات Prometheus
```

#### 🗄️ قاعدة البيانات:
```
📁 database/                              # قاعدة البيانات
   └── 📄 init.sql                       # سكريبت التهيئة
```

---

## 🔧 مسارات الأدوات

### 📍 المسار الأساسي: `scripts/`

#### 🛠️ السكريبتات الرئيسية:
```
📄 scripts/START_HERE.py                 # نقطة البداية
📄 scripts/COMPREHENSIVE_SYSTEM_TESTER.py # اختبار شامل للنظام
📄 scripts/MODELS_STATUS_CHECKER.py      # فحص حالة النماذج
📄 scripts/COLLABORATIVE_AI_SYSTEM.py    # نظام التعاون
📄 scripts/ULTIMATE_ANUBIS_HORUS_LAUNCHER.py # مشغل متقدم
📄 scripts/QUICK_SYSTEM_LAUNCHER.py      # مشغل سريع
```

#### 🌐 عميل الويب:
```
📁 web_client/                            # عميل الويب
   ├── 📄 app.py                         # تطبيق Flask
   └── 📄 Dockerfile                     # حاوية عميل الويب
```

#### 🌐 خادم الويب:
```
📁 nginx/                                 # إعدادات Nginx
   └── 📄 nginx.conf                     # ملف الإعدادات
```

---

## 🗃️ مسارات الأرشيف

### 📍 مسارات النسخ الاحتياطية:

```
📁 archive_and_backups/                   # الأرشيف والنسخ الاحتياطية
   ├── 📁 HORUS_AI_TEAM_BACKUP_*/        # نسخ احتياطية لفريق حورس
   ├── 📁 structure_backup_*/            # نسخ احتياطية للهيكل
   ├── 📁 unified_backups/               # النسخ الموحدة
   └── 📁 temp/                          # الملفات المؤقتة
```

---

## 💬 مسارات المحادثات

### 📍 سجلات المحادثات:

```
📁 chat_whit_AI/                          # سجلات المحادثات مع الذكاء الاصطناعي
   ├── 📄 CHAT3.md                       # المحادثة الثالثة
   ├── 📄 chat4.md                       # المحادثة الرابعة
   └── 📄 *.md                           # محادثات أخرى
```

---

## 🎯 ملخص المسارات المهمة

### 🚀 للتشغيل السريع:
- `QUICK_START.py`
- `LAUNCH_ANUBIS_COMPLETE.py`
- `start_complete_anubis_system.py`

### 🔧 للتطوير:
- `ANUBIS_SYSTEM/src/`
- `HORUS_AI_TEAM/01_core/`
- `ANUBIS_HORUS_MCP/src/`

### 📚 للتوثيق:
- `PROJECT_DOCUMENTATION/`
- `README.md`
- `PROJECT_STRUCTURE_DETAILED.md`

### 🔐 للأمان:
- `ANUBIS_HORUS_MCP/api_keys_vault/`
- `ANUBIS_SYSTEM/security/`

---

## 🔍 فهرس البحث السريع

### 🎯 البحث حسب نوع الملف:

#### 🐍 ملفات Python الرئيسية:
```
📄 main.py                               → ANUBIS_SYSTEM/main.py
📄 horus_interface.py                    → HORUS_AI_TEAM/01_core/interfaces/horus_interface.py
📄 mcp_server.py                         → ANUBIS_HORUS_MCP/core/mcp_server.py
📄 keys_manager.py                       → ANUBIS_HORUS_MCP/api_keys_vault/keys_manager.py
📄 QUICK_START.py                        → ./QUICK_START.py
📄 LAUNCH_ANUBIS_COMPLETE.py            → ./LAUNCH_ANUBIS_COMPLETE.py
```

#### 🌐 ملفات Node.js/JavaScript:
```
📄 index.js                              → ANUBIS_HORUS_MCP/src/index.js
📄 package.json                          → ANUBIS_HORUS_MCP/package.json
📄 app.js                                → web_client/app.js
```

#### 📋 ملفات التكوين:
```
📄 docker-compose.yml                    → ANUBIS_SYSTEM/docker-compose.yml
📄 mcp_config.json                       → ANUBIS_HORUS_MCP/config/mcp_config.json
📄 ai_config.json                        → ANUBIS_SYSTEM/config/ai_config.json
📄 nginx.conf                            → nginx/nginx.conf
```

#### 📚 ملفات التوثيق:
```
📄 README.md (رئيسي)                     → ./README.md
📄 README.md (أنوبيس)                    → ANUBIS_SYSTEM/README.md
📄 README.md (حورس)                      → HORUS_AI_TEAM/README.md
📄 README.md (MCP)                       → ANUBIS_HORUS_MCP/README.md
📄 USER_GUIDE_COMPLETE.md                → PROJECT_DOCUMENTATION/USER_GUIDE_COMPLETE.md
```

---

## 🚀 مسارات التشغيل السريع

### ⚡ للتشغيل الفوري:
```bash
# من المجلد الرئيسي
python QUICK_START.py                    # تشغيل سريع مع خيارات
python LAUNCH_ANUBIS_COMPLETE.py         # مشغل شامل مع قائمة
python start_complete_anubis_system.py   # تشغيل النظام الكامل مع Docker
```

### 🔧 للتطوير والاختبار:
```bash
# اختبار النظام
python scripts/COMPREHENSIVE_SYSTEM_TESTER.py

# فحص حالة النماذج
python scripts/MODELS_STATUS_CHECKER.py

# تكامل المشاريع
python INTEGRATE_ALL_PROJECTS.py
```

---

## 🔐 مسارات الأمان والمفاتيح

### 🗝️ إدارة مفاتيح API:
```
📁 ANUBIS_HORUS_MCP/api_keys_vault/       # المجلد الرئيسي للمفاتيح
   ├── 📄 keys_manager.py                # مدير المفاتيح الرئيسي
   ├── 📄 api_keys_collection.json       # مجموعة 726 مفتاح API
   ├── 📄 security_implementation.py     # تنفيذ الأمان
   ├── 📄 key_rotation_system.py         # نظام تدوير المفاتيح
   ├── 📄 secure_backup_system.py        # نظام النسخ الاحتياطية الآمنة
   ├── 📄 automated_management_system.py # نظام الإدارة التلقائية
   └── 📄 visual_dashboard_system.py     # لوحة التحكم المرئية
```

### 🛡️ ملفات الأمان:
```
📁 ANUBIS_SYSTEM/security/                # أمان نظام أنوبيس
📁 ANUBIS_SYSTEM/ssl/                     # شهادات SSL
📄 ANUBIS_SYSTEM/anubis_horus_bridge.py   # جسر آمن للتكامل
```

---

## 📊 مسارات البيانات والتقارير

### 📈 تقارير النظام:
```
📁 data/                                  # البيانات الرئيسية
   ├── 📄 anubis_comprehensive_test_report_*.json
   ├── 📄 docker_test_results_*.json
   ├── 📄 integration_bridges_report_*.json
   └── 📄 three_systems_integration_check_*.json

📁 PROJECT_DOCUMENTATION/reports_and_analysis/
   ├── 📄 FINAL_INTEGRATION_STATUS_REPORT.md
   ├── 📄 ANUBIS_HORUS_MCP_COMPREHENSIVE_ANALYSIS.md
   └── 📄 DOCKER_SERVER_GUIDE.md
```

### 📋 سجلات النظام:
```
📁 logs/                                  # السجلات الرئيسية
📁 ANUBIS_SYSTEM/logs/                    # سجلات أنوبيس
📁 ANUBIS_SYSTEM/system_logs/             # سجلات النظام المفصلة
📁 monitoring/                            # مراقبة الأداء
```

---

## 🤖 مسارات الذكاء الاصطناعي

### 👥 وكلاء فريق حورس:
```
📁 HORUS_AI_TEAM/02_team_members/         # تكوينات الوكلاء
   ├── 📄 THOTH.json                     # ⚡ المحلل السريع (phi3:mini)
   ├── 📄 PTAH.json                      # 🔧 المطور الخبير (mistral:7b)
   ├── 📄 RA.json                        # 🎯 المستشار الاستراتيجي (llama3:8b)
   ├── 📄 KHNUM.json                     # 💡 المبدع والمبتكر (strikegpt-r1-zero-8b)
   ├── 📄 SESHAT.json                    # 👁️ المحللة البصرية (Qwen2.5-VL-7B)
   ├── 📄 ANUBIS.json                    # 🔐 حارس الأمان (claude-3-opus)
   ├── 📄 MAAT.json                      # ⚖️ حارسة العدالة (gpt-4-turbo)
   └── 📄 HAPI.json                      # 📊 محلل البيانات (gemini-pro)
```

### 🧠 نظام الذاكرة الجماعية:
```
📁 HORUS_AI_TEAM/03_memory_system/        # نظام الذاكرة المتقدم
   ├── 📄 memory_manager.py              # إدارة الذاكرة الجماعية
   ├── 📄 pattern_analyzer.py            # تحليل الأنماط والسلوكيات
   ├── 📄 knowledge_base.py              # قاعدة المعرفة المشتركة
   └── 📄 adaptive_learning.py           # التعلم التكيفي
```

---

## 🌐 مسارات الواجهات والخدمات

### 🖥️ واجهات الويب:
```
📁 web_client/                            # عميل الويب الموحد
   ├── 📄 app.py                         # تطبيق Flask الرئيسي
   ├── 📄 Dockerfile                     # حاوية عميل الويب
   └── 📁 templates/                     # قوالب HTML

📁 ANUBIS_HORUS_MCP/templates/            # قوالب نظام MCP
📁 ANUBIS_HORUS_MCP/static/               # الملفات الثابتة (CSS, JS)
```

### 🔗 خدمات API:
```
📁 ANUBIS_SYSTEM/src/api/                 # واجهات برمجة التطبيقات
📁 ANUBIS_HORUS_MCP/src/protocols/        # بروتوكولات MCP
📁 ANUBIS_HORUS_MCP/src/handlers/         # معالجات الطلبات
```

---

## 🛠️ مسارات التطوير والأدوات

### 🔧 أدوات التطوير:
```
📁 scripts/                               # سكريبتات التطوير
   ├── 📄 START_HERE.py                  # نقطة البداية للمطورين
   ├── 📄 COMPREHENSIVE_SYSTEM_TESTER.py # اختبار شامل للنظام
   ├── 📄 MODELS_STATUS_CHECKER.py       # فحص حالة النماذج
   ├── 📄 COLLABORATIVE_AI_SYSTEM.py     # نظام التعاون
   └── 📄 ULTIMATE_ANUBIS_HORUS_LAUNCHER.py # مشغل متقدم
```

### 🧪 اختبارات النظام:
```
📁 ANUBIS_SYSTEM/tests/                   # اختبارات نظام أنوبيس
📁 PROJECT_DOCUMENTATION/test_reports/    # تقارير الاختبارات
📁 SHARED_REQUIREMENTS/tools/             # أدوات الفحص والتحليل
```

---

## 📦 مسارات المتطلبات والتبعيات

### 🐍 متطلبات Python:
```
📄 ANUBIS_SYSTEM/requirements.txt         # متطلبات نظام أنوبيس
📄 ANUBIS_HORUS_MCP/requirements_mcp.txt  # متطلبات نظام MCP
📁 SHARED_REQUIREMENTS/data/              # المتطلبات المشتركة
   ├── 📄 requirements_master.txt         # الملف الرئيسي (95 مكتبة)
   ├── 📄 requirements_core.txt           # المكتبات الأساسية
   ├── 📄 requirements_web.txt            # مكتبات تطوير الويب
   └── 📄 package.json                    # مكتبات Node.js (200+ مكتبة)
```

### 🔧 أدوات التثبيت:
```
📁 SHARED_REQUIREMENTS/installers/        # أدوات التثبيت التلقائي
📁 SHARED_REQUIREMENTS/tools/             # أدوات الفحص والتحليل
```

---

## 🎯 خريطة التنقل السريع

### 🚀 للمبتدئين - ابدأ هنا:
1. `./README.md` - اقرأ الدليل الرئيسي
2. `./QUICK_START.py` - شغل النظام بسرعة
3. `PROJECT_DOCUMENTATION/USER_GUIDE_COMPLETE.md` - دليل المستخدم

### 🔧 للمطورين - ابدأ هنا:
1. `PROJECT_STRUCTURE_DETAILED.md` - فهم الهيكل
2. `scripts/START_HERE.py` - نقطة بداية التطوير
3. `ANUBIS_SYSTEM/src/` - الكود المصدري

### 🔐 لإدارة الأمان - ابدأ هنا:
1. `ANUBIS_HORUS_MCP/api_keys_vault/` - إدارة المفاتيح
2. `ANUBIS_SYSTEM/security/` - أمان النظام
3. `ANUBIS_HORUS_MCP/api_keys_vault/visual_dashboard_system.py` - لوحة التحكم

---

<div align="center">

**🗂️ دليل مسارات شامل لمشروع أنوبيس المتكامل**

*جميع المسارات منظمة ومفهرسة لسهولة التنقل والوصول*

![Total Paths](https://img.shields.io/badge/🗂️-500+%20Paths-blue)
![Organized](https://img.shields.io/badge/📁-12%20Main%20Folders-green)
![Documented](https://img.shields.io/badge/📚-100%25%20Documented-gold)

</div>
