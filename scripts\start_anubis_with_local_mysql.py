#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 سكريبت تشغيل نظام أنوبيس مع قاعدة البيانات المحلية
Anubis System Startup Script with Local MySQL Database
"""

import os
import sys
import time
import json
import subprocess
import mysql.connector
from pathlib import Path

# ألوان للإخراج
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    NC = '\033[0m'  # No Color

def print_header():
    print(f"{Colors.PURPLE}{'='*50}{Colors.NC}")
    print(f"{Colors.PURPLE}🏺 نظام أنوبيس المتكامل مع MySQL المحلي{Colors.NC}")
    print(f"{Colors.PURPLE}Anubis Integrated System with Local MySQL{Colors.NC}")
    print(f"{Colors.PURPLE}{'='*50}{Colors.NC}")

def print_step(message):
    print(f"{Colors.BLUE}📋 {message}{Colors.NC}")

def print_success(message):
    print(f"{Colors.GREEN}✅ {message}{Colors.NC}")

def print_warning(message):
    print(f"{Colors.YELLOW}⚠️ {message}{Colors.NC}")

def print_error(message):
    print(f"{Colors.RED}❌ {message}{Colors.NC}")

def test_mysql_connection():
    """اختبار الاتصال بقاعدة البيانات MySQL المحلية"""
    print_step("اختبار الاتصال بقاعدة البيانات MySQL المحلية...")
    
    try:
        connection = mysql.connector.connect(
            host='localhost',
            port=3306,
            user='root',
            password = "[DATABASE_PASSWORD]"',
            database='anubis_system'
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print_success(f"متصل بـ MySQL: {version[0]}")
            
            # فحص الجداول الموجودة
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            print_success(f"عدد الجداول الموجودة: {len(tables)}")
            
            cursor.close()
            connection.close()
            return True
            
    except mysql.connector.Error as e:
        print_error(f"فشل الاتصال بقاعدة البيانات: {e}")
        return False

def check_docker():
    """فحص Docker"""
    print_step("فحص Docker...")
    try:
        result = subprocess.run(['docker', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print_success(f"Docker متوفر: {result.stdout.strip()}")
            return True
        else:
            print_error("Docker غير متوفر")
            return False
    except Exception as e:
        print_error(f"خطأ في فحص Docker: {e}")
        return False

def stop_existing_containers():
    """إيقاف الحاويات الموجودة"""
    print_step("إيقاف الحاويات الموجودة...")
    
    containers = [
        'anubis-core-server',
        'anubis-mcp-server', 
        'horus-team-server',
        'anubis-web-client-server',
        'anubis-database',
        'anubis-redis'
    ]
    
    for container in containers:
        try:
            subprocess.run(['docker', 'rm', '-f', container], 
                         capture_output=True, text=True)
        except:
            pass
    
    print_success("تم إيقاف الحاويات الموجودة")

def create_network():
    """إنشاء شبكة Docker"""
    print_step("إنشاء شبكة Docker...")
    
    try:
        # حذف الشبكة إذا كانت موجودة
        subprocess.run(['docker', 'network', 'rm', 'anubis-network'], 
                      capture_output=True, text=True)
        
        # إنشاء شبكة جديدة
        result = subprocess.run(['docker', 'network', 'create', 'anubis-network'], 
                               capture_output=True, text=True)
        
        if result.returncode == 0:
            print_success("تم إنشاء شبكة anubis-network")
            return True
        else:
            print_warning("الشبكة موجودة بالفعل أو تم إنشاؤها")
            return True
            
    except Exception as e:
        print_error(f"خطأ في إنشاء الشبكة: {e}")
        return False

def start_redis():
    """تشغيل Redis"""
    print_step("تشغيل Redis...")
    
    cmd = [
        'docker', 'run', '-d',
        '--name', 'anubis-redis',
        '--network', 'anubis-network',
        '-p', '6379:6379',
        'redis:7-alpine',
        'redis-server', '--requirepass', 'anubis_redis_pass'
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print_success("تم تشغيل Redis")
            return True
        else:
            print_error(f"فشل تشغيل Redis: {result.stderr}")
            return False
    except Exception as e:
        print_error(f"خطأ في تشغيل Redis: {e}")
        return False

def start_anubis_core():
    """تشغيل نظام أنوبيس الأساسي"""
    print_step("تشغيل نظام أنوبيس الأساسي...")
    
    cmd = [
        'docker', 'run', '-d',
        '--name', 'anubis-core-server',
        '--network', 'anubis-network',
        '-p', '8000:8000',
        '-e', 'DATABASE_URL=mysql://root:[DATABASE_PASSWORD]@host.docker.internal:3306/anubis_system',
        '-e', 'REDIS_URL=redis://anubis-redis:6379',
        '-e', 'ANUBIS_ENV=production',
        '-v', f'{os.getcwd()}/ANUBIS_SYSTEM:/app',
        'anubis-core'
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print_success("تم تشغيل نظام أنوبيس الأساسي")
            return True
        else:
            print_error(f"فشل تشغيل أنوبيس: {result.stderr}")
            return False
    except Exception as e:
        print_error(f"خطأ في تشغيل أنوبيس: {e}")
        return False

def start_mcp_system():
    """تشغيل نظام MCP"""
    print_step("تشغيل نظام MCP...")
    
    cmd = [
        'docker', 'run', '-d',
        '--name', 'anubis-mcp-server',
        '--network', 'anubis-network',
        '-p', '3000:3000',
        '-e', 'MCP_SECRET_KEY=mcp_secret_key_2024',
        '-v', f'{os.getcwd()}/ANUBIS_HORUS_MCP:/app',
        'anubis-mcp'
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print_success("تم تشغيل نظام MCP")
            return True
        else:
            print_error(f"فشل تشغيل MCP: {result.stderr}")
            return False
    except Exception as e:
        print_error(f"خطأ في تشغيل MCP: {e}")
        return False

def start_horus_team():
    """تشغيل فريق حورس"""
    print_step("تشغيل فريق حورس...")
    
    cmd = [
        'docker', 'run', '-d',
        '--name', 'horus-team-server',
        '--network', 'anubis-network',
        '-p', '7000:7000',
        '-e', 'HORUS_SECRET_KEY=horus_secret_key_2024',
        '-v', f'{os.getcwd()}/HORUS_AI_TEAM:/app',
        'horus-team'
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print_success("تم تشغيل فريق حورس")
            return True
        else:
            print_error(f"فشل تشغيل حورس: {result.stderr}")
            return False
    except Exception as e:
        print_error(f"خطأ في تشغيل حورس: {e}")
        return False

def start_web_client():
    """تشغيل الواجهة الموحدة"""
    print_step("تشغيل الواجهة الموحدة...")
    
    cmd = [
        'docker', 'run', '-d',
        '--name', 'anubis-web-client-server',
        '--network', 'anubis-network',
        '-p', '5000:5000',
        'anubis-web-client'
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print_success("تم تشغيل الواجهة الموحدة")
            return True
        else:
            print_error(f"فشل تشغيل الواجهة: {result.stderr}")
            return False
    except Exception as e:
        print_error(f"خطأ في تشغيل الواجهة: {e}")
        return False

def check_services():
    """فحص حالة الخدمات"""
    print_step("فحص حالة الخدمات...")
    
    services = [
        ('أنوبيس الأساسي', 'http://localhost:8000/health'),
        ('نظام MCP', 'http://localhost:3000/health'),
        ('فريق حورس', 'http://localhost:7000/health'),
        ('الواجهة الموحدة', 'http://localhost:5000')
    ]
    
    time.sleep(10)  # انتظار حتى تبدأ الخدمات
    
    for name, url in services:
        try:
            import requests
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print_success(f"{name}: متاح")
            else:
                print_warning(f"{name}: غير متاح (كود: {response.status_code})")
        except:
            print_warning(f"{name}: غير متاح")

def show_access_info():
    """عرض معلومات الوصول"""
    print_step("معلومات الوصول للخدمات:")
    
    print(f"{Colors.CYAN}🌐 الواجهات المتاحة:{Colors.NC}")
    print(f"{Colors.GREEN}🏺 نظام أنوبيس: http://localhost:8000{Colors.NC}")
    print(f"{Colors.GREEN}🔗 نظام MCP: http://localhost:3000{Colors.NC}")
    print(f"{Colors.GREEN}𓅃 فريق حورس: http://localhost:7000{Colors.NC}")
    print(f"{Colors.GREEN}🌐 الواجهة الموحدة: http://localhost:5000{Colors.NC}")
    
    print(f"\n{Colors.CYAN}🔧 أوامر مفيدة:{Colors.NC}")
    print(f"{Colors.YELLOW}عرض السجلات: docker logs anubis-core-server{Colors.NC}")
    print(f"{Colors.YELLOW}إيقاف الخدمات: docker stop anubis-core-server anubis-mcp-server horus-team-server{Colors.NC}")

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # فحص المتطلبات
    if not test_mysql_connection():
        print_error("فشل الاتصال بقاعدة البيانات MySQL المحلية")
        return False
    
    if not check_docker():
        print_error("Docker غير متوفر")
        return False
    
    # إيقاف الحاويات الموجودة
    stop_existing_containers()
    
    # إنشاء الشبكة
    if not create_network():
        return False
    
    # تشغيل الخدمات
    if not start_redis():
        return False
    
    time.sleep(5)  # انتظار Redis
    
    if not start_anubis_core():
        return False
    
    if not start_mcp_system():
        return False
    
    if not start_horus_team():
        return False
    
    if not start_web_client():
        return False
    
    # فحص الخدمات
    check_services()
    
    # عرض معلومات الوصول
    show_access_info()
    
    print_success("تم تشغيل نظام أنوبيس المتكامل بنجاح مع قاعدة البيانات المحلية!")
    
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print_warning("\nتم إيقاف التشغيل بواسطة المستخدم")
    except Exception as e:
        print_error(f"خطأ غير متوقع: {e}")
