# 🚀 دليل النشر الشامل

## 📋 متطلبات النشر

### الأدوات المطلوبة:
- ✅ Docker 28.3.2+
- ✅ Python 3.13.5+
- ✅ Google Cloud SDK 523.0.1+
- ✅ Git 2.49.0+

### الحسابات المطلوبة:
- Google Cloud Platform (مع تفعيل الفوترة)
- GitHub (للكود المصدري)

## 🎯 خطوات النشر

### 1. النشر المحلي
```bash
# تشغيل جميع الخدمات محلياً
python LAUNCH_ANUBIS_COMPLETE.py
```

### 2. الن<PERSON>ر على Google Cloud
```bash
# تأكد من تفعيل الفوترة أولاً
python deploy_after_billing.py
```

### 3. إعداد n8n للأتمتة
```bash
# تشغيل n8n
docker-compose -f docker-compose.n8n.yml up -d
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها:
1. **Docker غير متاح**: تأكد من تشغيل Docker Desktop
2. **مشكلة في Google Cloud**: تحقق من تفعيل الفوترة
3. **مشكلة في المنافذ**: تأكد من عدم استخدام المنافذ

## 📊 مراقبة النشر

بعد النشر، تحقق من:
- ✅ جميع الخدمات تعمل
- ✅ المنافذ متاحة
- ✅ لا توجد أخطاء في السجلات

---
**تم إنشاؤه تلقائياً في:** 2025-07-31 05:32:48
