#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 مشغل داشبورد إدارة Anubis Cloud
==================================
"""

import subprocess
import sys
import os
import time
from datetime import datetime

def check_requirements():
    """التحقق من المتطلبات"""
    print("🔍 التحقق من المتطلبات...")
    
    required_packages = [
        'streamlit',
        'plotly', 
        'pandas',
        'requests',
        'google-cloud-compute',
        'google-cloud-run',
        'google-cloud-sql'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} - مفقود")
    
    if missing_packages:
        print(f"\n📦 تثبيت المكتبات المفقودة...")
        for package in missing_packages:
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                print(f"✅ تم تثبيت {package}")
            except subprocess.CalledProcessError:
                print(f"❌ فشل في تثبيت {package}")
    
    print("✅ جميع المتطلبات متوفرة!")

def check_gcloud():
    """التحقق من Google Cloud SDK"""
    print("\n🔍 التحقق من Google Cloud SDK...")
    
    try:
        result = subprocess.run(['gcloud', '--version'], 
                              capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            print("✅ Google Cloud SDK متوفر")
            return True
        else:
            print("❌ Google Cloud SDK غير متوفر")
            return False
    except:
        print("❌ Google Cloud SDK غير مثبت")
        return False

def check_authentication():
    """التحقق من المصادقة"""
    print("\n🔍 التحقق من المصادقة...")
    
    try:
        result = subprocess.run(['gcloud', 'auth', 'list', '--filter=status:ACTIVE'], 
                              capture_output=True, text=True, shell=True)
        if "<EMAIL>" in result.stdout:
            print("✅ المصادقة متوفرة")
            return True
        else:
            print("⚠️ يجب تسجيل الدخول")
            return False
    except:
        print("❌ مشكلة في التحقق من المصادقة")
        return False

def setup_project():
    """إعداد المشروع"""
    print("\n⚙️ إعداد المشروع...")
    
    project_id = "anubis-467210"
    
    try:
        subprocess.run(['gcloud', 'config', 'set', 'project', project_id], 
                     check=True, shell=True)
        print(f"✅ تم تعيين المشروع: {project_id}")
        return True
    except:
        print(f"❌ فشل في تعيين المشروع: {project_id}")
        return False

def create_dashboard_config():
    """إنشاء ملف تكوين الداشبورد"""
    print("\n📝 إنشاء ملف التكوين...")
    
    config_content = """# 🎛️ Anubis Cloud Dashboard Configuration

[theme]
primaryColor = "#ff6b6b"
backgroundColor = "#ffffff"
secondaryBackgroundColor = "#f0f2f6"
textColor = "#262730"

[server]
port = 8501
address = "0.0.0.0"
maxUploadSize = 200

[browser]
gatherUsageStats = false
"""
    
    config_dir = os.path.expanduser("~/.streamlit")
    os.makedirs(config_dir, exist_ok=True)
    
    config_path = os.path.join(config_dir, "config.toml")
    
    with open(config_path, "w", encoding="utf-8") as f:
        f.write(config_content)
    
    print(f"✅ تم إنشاء ملف التكوين: {config_path}")

def start_dashboard():
    """تشغيل الداشبورد"""
    print("\n🚀 تشغيل داشبورد Anubis Cloud...")
    print("=" * 60)
    print(f"📅 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🌐 الرابط: http://localhost:8501")
    print("🎛️ الداشبورد: Anubis Cloud Management")
    print("=" * 60)
    
    try:
        # تشغيل Streamlit
        subprocess.run([
            'streamlit', 'run', 'ANUBIS_CLOUD_DASHBOARD.py',
            '--server.port', '8501',
            '--server.address', '0.0.0.0',
            '--browser.gatherUsageStats', 'false'
        ], shell=True)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الداشبورد")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الداشبورد: {str(e)}")

def main():
    """الدالة الرئيسية"""
    print("🎛️ مشغل داشبورد إدارة Anubis Cloud")
    print("=" * 50)
    
    # التحقق من المتطلبات
    check_requirements()
    
    # التحقق من Google Cloud SDK
    if not check_gcloud():
        print("\n❌ يجب تثبيت Google Cloud SDK أولاً")
        print("📥 تحميل من: https://cloud.google.com/sdk/docs/install")
        return
    
    # التحقق من المصادقة
    if not check_authentication():
        print("\n🔐 تسجيل الدخول...")
        try:
            subprocess.run(['gcloud', 'auth', 'login'], shell=True)
        except:
            print("❌ فشل في تسجيل الدخول")
            return
    
    # إعداد المشروع
    if not setup_project():
        return
    
    # إنشاء ملف التكوين
    create_dashboard_config()
    
    # تشغيل الداشبورد
    start_dashboard()

if __name__ == "__main__":
    main()
