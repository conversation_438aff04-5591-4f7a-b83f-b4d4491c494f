{"test_summary": {"total_tests": 8, "passed_tests": 8, "failed_tests": 0, "success_rate": 100.0, "test_date": "2025-07-28T04:49:16.933129"}, "detailed_results": [{"test_name": "البرمجة الأساسية - Python", "prompt": "اكتب دالة Python لحساب العدد الأولي", "response": "بالطبع! إليك دالة بلغة Python تُستخدم لحساب **العدد الأولي** في موقع معين (مثلاً العدد الأولي الخامس). ولكن قبل أن نكتب الدالة، دعنا نُحدد المقصود:\n\n### ما هو العدد الأولي؟\nالعدد الأولي هو عدد طبيعي أكبر من 1، ولا يقبل القسمة إلا على نفسه وعلى 1.  \nأمثلة: 2, 3, 5, 7, 11, 13, 17, ...\n\n---\n\n## ✅ الهدف:\nكتابة دالة تُرجع العدد الأولي في الموقع `n` (مثلاً العدد الأولي الخامس).\n\n---\n\n### 🔧 الكود:\n\n```python\ndef is_prime(num):\n    \"\"\"دالة تتحقق مما إذا كان العدد أولياً\"\"\"\n    if num < 2:\n        return False\n    for i in range(2, int(num ** 0.5) + 1):\n        if num % i == 0:\n            return False\n    return True\n\ndef nth_prime(n):\n    \"\"\"دالة تُرجع العدد الأولي في الموقع n\"\"\"\n    count = 0\n    num = 1\n    while count < n:\n        num += 1\n        if is_prime(num):\n            count += 1\n    return num\n\n# مثال على الاستخدام:\nprint(nth_prime(5))  # الناتج سيكون: 11 (لأن 11 هو الخامس في تسلسل الأعداد الأولية)\n```\n\n---\n\n### 📌 شرح الكود:\n\n1. `is_prime(num)`:\n   - تتحقق مما إذا كان العدد أولياً.\n   - نستخدم الجذر التربيعي لتسريع العملية.\n\n2. `nth_prime(n)`:\n   - تبحث عن العدد الأولي رقم `n` (مثلاً العاشر).\n   - تستخدم حلقة للعد التنازلي من العدد 2 فصاعدًا، وتحقق إذا كان العدد أوليًا.\n\n---\n\n### ✅ مثال تنفيذي:\n\n```python\nprint(nth_prime(1))   # الناتج: 2\nprint(nth_prime(10))  # الناتج: 29\n```\n\nهل ترغب أن نحسب قائمة من الأعداد الأولية حتى عدد معين؟", "response_time": 31.68, "success": true, "expected_keywords": ["def", "prime", "return"], "timestamp": "2025-07-28T04:43:15.610018"}, {"test_name": "هياكل البيانات", "prompt": "اكتب كلاس Python لتن<PERSON>ي<PERSON> (مكدس) مع العمليات الأساسية", "response": "سأكتب لك كلاس Python متكامل لتنفيذ مكدس (Stack) مع جميع العمليات الأساسية:\n\n```python\nclass Stack:\n    def __init__(self):\n        \"\"\"المُنشئ - يُنشئ مكدس فارغ\"\"\"\n        self.items = []\n    \n    def push(self, item):\n        \"\"\"إضافة عنصر إلى قمة المكدس\"\"\"\n        self.items.append(item)\n    \n    def pop(self):\n        \"\"\"إزالة وإرجاع العنصر من قمة المكدس\"\"\"\n        if self.is_empty():\n            raise IndexError(\"لا يمكن إزالة عنصر من مكدس فارغ\")\n        return self.items.pop()\n    \n    def peek(self):\n        \"\"\"عرض العنصر في قمة المكدس دون إزالته\"\"\"\n        if self.is_empty():\n            raise IndexError(\"لا يوجد عناصر في المكدس\")\n        return self.items[-1]\n    \n    def is_empty(self):\n        \"\"\"التحقق مما إذا كان المكدس فارغًا\"\"\"\n        return len(self.items) == 0\n    \n    def size(self):\n        \"\"\"إرجاع عدد العناصر في المكدس\"\"\"\n        return len(self.items)\n    \n    def __str__(self):\n        \"\"\"تمثيل نصي للمكدس\"\"\"\n        return f\"Stack({self.items})\"\n    \n    def __len__(self):\n        \"\"\"السماح باستخدام len() على المكدس\"\"\"\n        return len(self.items)\n\n# أمثلة على الاستخدام:\n\n# إنشاء مكدس جديد\nstack = Stack()\nprint(f\"هل المكدس فارغ؟ {stack.is_empty()}\")  # True\n\n# إضافة عناصر\nstack.push(10)\nstack.push(20)\nstack.push(30)\nprint(f\"المكدس: {stack}\")  # Stack([10, 20, 30])\nprint(f\"حجم المكدس: {stack.size()}\")  # 3\n\n# عرض العنصر في القمة\nprint(f\"العنصر في القمة: {stack.peek()}\")  # 30\n\n# إزالة العناصر\nprint(f\"العنصر المزال: {stack.pop()}\")  # 30\nprint(f\"العنصر المزال: {stack.pop()}\")  # 20\nprint(f\"المكدس الآن: {stack}\")  # Stack([10])\nprint(f\"حجم المكدس: {stack.size()}\")  # 1\n\n# استخدام المكدس لعكس نص\ndef reverse_string(text):\n    stack = Stack()\n    \n    # إضافة جميع الأحرف إلى المكدس\n    for char in text:\n        stack.push(char)\n    \n    # إزالة الأحرف للحصول على النص المعاكس\n    reversed_text = \"\"\n    while not stack.is_empty():\n        reversed_text += stack.pop()\n    \n    return reversed_text\n\n# مثال على عكس النص\noriginal = \"Hello World\"\nreversed_text = reverse_string(original)\nprint(f\"النص الأصلي: {original}\")  # Hello World\nprint(f\"النص المعاكس: {reversed_text}\")  # dlroW olleH\n\n# التحقق من صحة الأقواس المتوازنة\ndef is_balanced_parentheses(expression):\n    stack = Stack()\n    opening = \"({[\"\n    closing = \")}]\"\n    pairs = {\"(\": \")\", \"{\": \"}\", \"[\": \"]\"}\n    \n    for char in expression:\n        if char in opening:\n            stack.push(char)\n        elif char in closing:\n            if stack.is_empty():\n                return False\n            if pairs[stack.pop()] != char:\n                return False\n    \n    return stack.is_empty()\n\n# أمثلة على التحقق من الأقواس\nprint(is_balanced_parentheses(\"()\"))  # True\nprint(is_balanced_parentheses(\"([{}])\"))  # True\nprint(is_balanced_parentheses(\"([)]\"))  # False\nprint(is_balanced_parentheses(\"((())\"))  # False\n```\n\n## شرح العمليات الأساسية:\n\n### 1. **push(item)**:\n- تضيف عنصرًا إلى قمة المكدس\n- التعقيد الزمني: O(1)\n\n### 2. **pop()**:\n- تزيل وترجع العنصر من قمة المكدس\n- ترفع استثناء إذا كان المكدس فارغًا\n- التعقيد الزمني: O(1)\n\n### 3. **peek()**:\n- تعرض العنصر في قمة المكدس دون إزالته\n- ترفع استثناء إذا كان المكدس فارغًا\n- التعقيد الزمني: O(1)\n\n### 4. **is_empty()**:\n- تتحقق مما إذا كان المكدس فارغًا\n- ترجع True أو False\n- التعقيد الزمني: O(1)\n\n### 5. **size()**:\n- ترجع عدد العناصر في المكدس\n- التعقيد الزمني: O(1)\n\n## مميزات هذا التنفيذ:\n\n1. **آمن**: يتعامل مع الحالات الاستثنائية (مكدس فارغ)\n2. **سهل الاستخدام**: واجهة برمجية واضحة ومباشرة\n3. **مرن**: يمكن تخزين أي نوع من البيانات\n4. **مدعوم**: يدعم الوظائف المدمجة مثل `len()` وتحويل النص\n\nهذا التنفيذ يتبع مبدأ LIFO (Last In, First Out) وهو الأساس في عمل المكدسات.", "response_time": 90.46, "success": true, "expected_keywords": ["class", "stack", "push", "pop"], "timestamp": "2025-07-28T04:44:46.073186"}, {"test_name": "الخوارزميات", "prompt": "اكتب خوارزمية الفرز السريع (Quick Sort) في Python", "response": "سأقدم لك خوارزمية الفرز السريع (Quick Sort) مع شرح مفصل وأمثلة عملية.\n\n## الخوارزمية الأساسية\n\n```python\ndef quick_sort(arr):\n    \"\"\"\n    خوارزمية الفرز السريع - النسخة الأساسية\n    \"\"\"\n    # الحالة الأساسية: إذا كان المصفوفة يحتوي على عنصر واحد أو أقل\n    if len(arr) <= 1:\n        return arr\n    \n    # اختيار العنصر المحوري (pivot)\n    pivot = arr[len(arr) // 2]\n    \n    # تقسيم المصفوفة إلى ثلاث أجزاء\n    left = [x for x in arr if x < pivot]      # العناصر الأصغر من المحور\n    middle = [x for x in arr if x == pivot]   # العناصر المساوية للمحور\n    right = [x for x in arr if x > pivot]     # العناصر الأكبر من المحور\n    \n    # تطبيق الفرز السريع بشكل تكراري ثم دمج النتائج\n    return quick_sort(left) + middle + quick_sort(right)\n\n# مثال على الاستخدام\nif __name__ == \"__main__\":\n    # اختبار الخوارزمية\n    test_array = [64, 34, 25, 12, 22, 11, 90, 5]\n    print(\"المصفوفة الأصلية:\", test_array)\n    \n    sorted_array = quick_sort(test_array)\n    print(\"المصفوفة بعد الفرز:\", sorted_array)\n```\n\n## النسخة المحسنة (In-place)\n\n```python\ndef quick_sort_inplace(arr, low=0, high=None):\n    \"\"\"\n    خوارزمية الفرز السريع المحسنة التي تعمل على نفس المصفوفة\n    \"\"\"\n    if high is None:\n        high = len(arr) - 1\n    \n    if low < high:\n        # تقسيم المصفوفة والحصول على موقع المحور\n        pivot_index = partition(arr, low, high)\n        \n        # تطبيق الفرز على الجزء الأيسر من المحور\n        quick_sort_inplace(arr, low, pivot_index - 1)\n        \n        # تطبيق الفرز على الجزء الأيمن من المحور\n        quick_sort_inplace(arr, pivot_index + 1, high)\n\ndef partition(arr, low, high):\n    \"\"\"\n    دالة التقسيم - تعيد موقع المحور الصحيح\n    \"\"\"\n    # اختيار آخر عنصر كمحور\n    pivot = arr[high]\n    \n    # مؤشر للعناصر الأصغر\n    i = low - 1\n    \n    for j in range(low, high):\n        # إذا كان العنصر الحالي أصغر من أو يساوي المحور\n        if arr[j] <= pivot:\n            i += 1\n            arr[i], arr[j] = arr[j], arr[i]  # تبديل العناصر\n    \n    # وضع المحور في موقعه الصحيح\n    arr[i + 1], arr[high] = arr[high], arr[i + 1]\n    return i + 1\n\n# مثال على الاستخدام\nif __name__ == \"__main__\":\n    test_array = [64, 34, 25, 12, 22, 11, 90, 5]\n    print(\"المصفوفة الأصلية:\", test_array)\n    \n    quick_sort_inplace(test_array)\n    print(\"المصفوفة بعد الفرز:\", test_array)\n```\n\n## النسخة المتقدمة مع خيارات متعددة\n\n```python\nimport random\n\nclass QuickSort:\n    def __init__(self):\n        self.comparisons = 0\n        self.swaps = 0\n    \n    def reset_counters(self):\n        \"\"\"إعادة تعيين عدادات المقارنات والتبديلات\"\"\"\n        self.comparisons = 0\n        self.swaps = 0\n    \n    def quick_sort_random(self, arr, low=0, high=None):\n        \"\"\"\n        الفرز السريع مع اختيار محور عشوائي\n        \"\"\"\n        if high is None:\n            high = len(arr) - 1\n        \n        if low < high:\n            # اختيار محور عشوائي\n            random_index = random.randint(low, high)\n            arr[random_index], arr[high] = arr[high], arr[random_index]\n            self.swaps += 1\n            \n            # تقسيم وفرز\n            pivot_index = self.partition(arr, low, high)\n            self.quick_sort_random(arr, low, pivot_index - 1)\n            self.quick_sort_random(arr, pivot_index + 1, high)\n    \n    def partition(self, arr, low, high):\n        \"\"\"\n        تقسيم المصفوفة مع عد المقارنات والتبديلات\n        \"\"\"\n        pivot = arr[high]\n        i = low - 1\n        \n        for j in range(low, high):\n            self.comparisons += 1\n            if arr[j] <= pivot:\n                i += 1\n                if i != j:\n                    arr[i], arr[j] = arr[j], arr[i]\n                    self.swaps += 1\n        \n        if i + 1 != high:\n            arr[i + 1], arr[high] = arr[high], arr[i + 1]\n            self.swaps += 1\n        \n        return i + 1\n    \n    def sort_with_stats(self, arr):\n        \"\"\"\n        فرز مع عرض الإحصائيات\n        \"\"\"\n        self.reset_counters()\n        arr_copy = arr.copy()\n        self.quick_sort_random(arr_copy)\n        \n        print(f\"عدد المقارنات: {self.comparisons}\")\n        print(f\"عدد التبديلات: {self.swaps}\")\n        return arr_copy\n\n# أمثلة استخدام متنوعة\ndef demonstrate_quick_sort():\n    \"\"\"\n    عرض أمثلة مختلفة لاستخدام الفرز السريع\n    \"\"\"\n    \n    # المثال 1: الأرقام العادية\n    print(\"=== المثال 1: الأرقام العادية ===\")\n    numbers = [64, 34, 25, 12, 22, 11, 90, 5]\n    print(f\"قبل الفرز: {numbers}\")\n    sorted_numbers = quick_sort(numbers)\n    print(f\"بعد الفرز: {sorted_numbers}\")\n    print()\n    \n    # المثال 2: الأرقام السالبة والموجبة\n    print(\"=== المثال 2: الأرقام السالبة والموجبة ===\")\n    mixed_numbers = [-3, 5, -1, 8, -7, 2, 0, -4]\n    print(f\"قبل الفرز: {mixed_numbers}\")\n    sorted_mixed = quick_sort(mixed_numbers)\n    print(f\"بعد الفرز: {sorted_mixed}\")\n    print()\n    \n    # المثال 3: السلاسل النصية\n    print(\"=== المثال 3: السلاسل النصية ===\")\n    words = [\"تفاح\", \"موز\", \"برتقال\", \"عنب\", \"كيوي\"]\n    print(f\"قبل الفرز: {words}\")\n    sorted_words = quick_sort(words)\n    print(f\"بعد الفرز: {sorted_words}\")\n    print()\n    \n    # المثال 4: مع الإحصائيات\n    print(\"=== المثال 4: مع الإحصائيات ===\")\n    qs = QuickSort()\n    data = [3, 6, 8, 10, 1, 2, 1]\n    print(f\"المصفوفة الأصلية: {data}\")\n    sorted_data = qs.sort_with_stats(data)\n    print(f\"المصفوفة بعد الفرز: {sorted_data}\")\n    print()\n\n# دالة لمقارنة الأداء\ndef performance_comparison():\n    \"\"\"\n    مقارنة الأداء مع مصفوفات مختلفة الأحجام\n    \"\"\"\n    import time\n    \n    sizes = [100, 1000, 5000]\n    \n    for size in sizes:\n        # إنشاء مصفوفة عشوائية\n        test_data = [random.randint(1, 1000) for _ in range(size)]\n        \n        # قياس زمن التنفيذ\n        start_time = time.time()\n        quick_sort(test_data.copy())\n        end_time = time.time()\n        \n        print(f\"حجم المصفوفة: {size}\")\n        print(f\"زمن التنفيذ: {end_time - start_time:.6f} ثانية\")\n        print(\"-\" * 30)\n\nif __name__ == \"__main__\":\n    # تشغيل الأمثلة\n    demonstrate_quick_sort()\n    \n    # مقارنة الأداء (اختياري)\n    # performance_comparison()\n```\n\n## شرح مفصل للخوارزمية:\n\n### 1. **المبدأ الأساسي:**\n- اختيار عنصر محوري (pivot)\n- تقسيم المصفوفة إلى عناصر أصغر وأكبر من المحور\n- تطبيق الفرز بشكل تكراري على الأجزاء\n\n### 2. **التعقيد الزمني:**\n- **الأفضل:** O(n log n) - عندما يتم تقسيم المصفوفة بشكل متوازن\n- **المتوسط:**", "response_time": 78.65, "success": true, "expected_keywords": ["quicksort", "sort", "partition"], "timestamp": "2025-07-28T04:46:04.724454"}, {"test_name": "JavaScript", "prompt": "اكتب دالة JavaScript لتحويل JSON إلى HTML table", "response": "بالطبع! إليك دالة بلغة JavaScript تقوم بتحويل بيانات بصيغة JSON إلى جدول HTML. هذه الدالة تفترض أن البيانات تكون على شكل مصفوفة من الكائنات، وكل كائن يمثل صفًا في الجدول.\n\n### ✅ مثال على JSON:\n```json\n[\n  { \"الاسم\": \"أحمد\", \"العمر\": 25, \"المدينة\": \"الرياض\" },\n  { \"الاسم\": \"سارة\", \"العمر\": 30, \"المدينة\": \"جدة\" }\n]\n```\n\n### ✅ الدالة:\n```javascript\nfunction jsonToHtmlTable(jsonData) {\n  if (!Array.isArray(jsonData) || jsonData.length === 0) {\n    return '<p>لا توجد بيانات</p>';\n  }\n\n  // استخراج أسماء الأعمدة من أول كائن\n  const headers = Object.keys(jsonData[0]);\n\n  // بدء إنشاء الجدول\n  let table = '<table border=\"1\" style=\"width:100%; border-collapse: collapse;\">\\n';\n\n  // إنشاء صف العناوين\n  table += '  <thead>\\n    <tr>\\n';\n  headers.forEach(header => {\n    table += `      <th>${header}</th>\\n`;\n  });\n  table += '    </tr>\\n  </thead>\\n';\n\n  // إنشاء صفوف البيانات\n  table += '  <tbody>\\n';\n  jsonData.forEach(row => {\n    table += '    <tr>\\n';\n    headers.forEach(header => {\n      table += `      <td>${row[header] || ''}</td>\\n`;\n    });\n    table += '    </tr>\\n';\n  });\n  table += '  </tbody>\\n';\n\n  table += '</table>';\n\n  return table;\n}\n```\n\n### ✅ كيفية الاستخدام:\n```javascript\nconst بيانات = [\n  { \"الاسم\": \"أحمد\", \"العمر\": 25, \"المدينة\": \"الرياض\" },\n  { \"الاسم\": \"سارة\", \"العمر\": 30, \"المدينة\": \"جدة\" },\n  { \"الاسم\": \"ليلى\", \"العمر\": 22, \"المدينة\": \"الدمام\" }\n];\n\nconst جدول = jsonToHtmlTable(بيانات);\ndocument.body.innerHTML = جدول;\n```\n\n### ✅ الناتج (HTML):\n```html\n<table border=\"1\" style=\"width:100%; border-collapse: collapse;\">\n  <thead>\n    <tr>\n      <th>الاسم</th>\n      <th>العمر</th>\n      <th>المدينة</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <td>أحمد</td>\n      <td>25</td>\n      <td>الرياض</td>\n    </tr>\n    <tr>\n      <td>سارة</td>\n      <td>30</td>\n      <td>جدة</td>\n    </tr>\n    <tr>\n      <td>ليلى</td>\n      <td>22</td>\n      <td>الدمام</td>\n    </tr>\n  </tbody>\n</table>\n```\n\nهل ترغب أن تكون هذه الدالة تتعامل مع بيانات JSON متداخلة (nested) أيضًا؟", "response_time": 32.28, "success": true, "expected_keywords": ["function", "json", "html", "table"], "timestamp": "2025-07-28T04:46:37.004644"}, {"test_name": "قواعد البيانات - SQL", "prompt": "اكتب استعلام SQL لإيجاد أعلى 5 موظفين راتباً من جدول employees", "response": "بالطبع! إليك استعلام SQL لإيجاد أعلى 5 موظفين من حيث الراتب من جدول يُدعى `employees`:\n\n```sql\nSELECT *\nFROM employees\nORDER BY salary DESC\nLIMIT 5;\n```\n\n### شرح الاستعلام:\n\n- `SELECT *`: يُستخدم لاختيار جميع الأعمدة من الجدول.\n- `FROM employees`: يحدد الجدول الذي نستعلم منه، وهو في هذه الحالة `employees`.\n- `ORDER BY salary DESC`: يرتب النتائج تنازليًا بناءً على عمود `salary` (الراتب).\n- `LIMIT 5`: يحد النتائج الخمسة الأولى فقط.\n\n### ملاحظة:\nإذا كنت تستخدم قاعدة بيانات مثل SQL Server، فربما تحتاج إلى استخدام `TOP` بدلاً من `LIMIT`:\n\n```sql\nSELECT TOP 5 *\nFROM employees\nORDER BY salary DESC;\n```\n\nهل ترغب في استرجاع أعمدة معينة فقط مثل الاسم والراتب؟", "response_time": 7.75, "success": true, "expected_keywords": ["select", "top", "order by", "desc"], "timestamp": "2025-07-28T04:46:44.751100"}, {"test_name": "تطوير الويب - HTML/CSS", "prompt": "اكتب صفحة HTML بسيطة مع CSS لعرض قائمة منتجات", "response": "بالطبع! إليك مثالاً بسيطاً على صفحة **HTML** مع **CSS** لعرض قائمة منتجات. ستتضمن الصفحة تصميمًا بسيطًا باستخدام HTML وCSS داخليًا (بدون JavaScript)، وستعرض منتجات بسيطة مع صور، أسماء، وأسعار.\n\n---\n\n### ✅ المثال: صفحة عرض منتجات بسيطة\n\n```html\n<!DOCTYPE html>\n<html lang=\"ar\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>قائمة المنتجات</title>\n    <style>\n        body {\n            font-family: 'Arial', sans-serif;\n            background-color: #f4f4f4;\n            margin: 0;\n            padding: 20px;\n        }\n\n        h1 {\n            text-align: center;\n            color: #333;\n        }\n\n        .products-container {\n            display: flex;\n            flex-wrap: wrap;\n            justify-content: center;\n            gap: 20px;\n        }\n\n        .product-card {\n            background-color: white;\n            border-radius: 8px;\n            box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n            width: 220px;\n            padding: 15px;\n            text-align: center;\n        }\n\n        .product-card img {\n            width: 100%;\n            height: 150px;\n            object-fit: cover;\n            border-radius: 6px;\n        }\n\n        .product-card h2 {\n            font-size: 18px;\n            color: #333;\n            margin: 10px 0;\n        }\n\n        .product-card p {\n            color: #777;\n            font-size: 14px;\n            margin: 0 0 10px;\n        }\n\n        .product-card .price {\n            font-weight: bold;\n            color: #e91e63;\n            font-size: 18px;\n        }\n    </style>\n</head>\n<body>\n\n    <h1>منتجاتنا</h1>\n\n    <div class=\"products-container\">\n\n        <div class=\"product-card\">\n            <img src=\"https://via.placeholder.com/220x150\" alt=\"منتج 1\">\n            <h2>منتج رقم 1</h2>\n            <p>وصف بسيط لهذا المنتج الجميل والمفيد.</p>\n            <div class=\"price\">120 ريال</div>\n        </div>\n\n        <div class=\"product-card\">\n            <img src=\"https://via.placeholder.com/220x150\" alt=\"منتج 2\">\n            <h2>منتج رقم 2</h2>\n            <p>منتج عالي الجودة وبسعر منافس.</p>\n            <div class=\"price\">90 ريال</div>\n        </div>\n\n        <div class=\"product-card\">\n            <img src=\"https://via.placeholder.com/220x150\" alt=\"منتج 3\">\n            <h2>منتج رقم 3</h2>\n            <p>منتج مميز لجميع الفئات العمرية.</p>\n            <div class=\"price\">200 ريال</div>\n        </div>\n\n        <div class=\"product-card\">\n            <img src=\"https://via.placeholder.com/220x150\" alt=\"منتج 4\">\n            <h2>منتج رقم 4</h2>\n            <p>منتج جديد ومتوفر بكميات محدودة.</p>\n            <div class=\"price\">150 ريال</div>\n        </div>\n\n    </div>\n\n</body>\n</html>\n```\n\n---\n\n### 🧾 شرح الكود:\n\n- `<body>`: يحتوي على العناصر الرئيسية مثل العنوان وحاوية المنتجات.\n- `.products-container`: نظام تخطيط مرن (Flexbox) لعرض البطاقات بجانب بعضها.\n- `.product-card`: تصميم بطاقة لكل منتج يحتوي على صورة، اسم، وصف، وسعر.\n- الصور يتم عرضها باستخدام `https://via.placeholder.com/` كصور مؤقتة.\n\n---\n\n### 💡 ملاحظات:\n\n- يمكنك استبدال روابط الصور بروابط حقيقية.\n- يمكن تحسين التصميم بإضافة زر \"إضافة إلى السلة\" أو روابط تفاصيل المنتج.\n- يمكنك تحويل الكود إلى ملفين: `index.html` و `style.css` إذا أردت تنظيم الكود بشكل أفضل.\n\nهل تود أن أساعدك في إضافة خاصية البحث أو التصفية للمنتجات؟", "response_time": 67.67, "success": true, "expected_keywords": ["html", "css", "div", "style"], "timestamp": "2025-07-28T04:47:52.424633"}, {"test_name": "معالجة الأخطاء", "prompt": "اكتب دالة Python تقرأ ملف وتتعامل مع الأخطاء المحتملة", "response": "بالطبع! إليك دالة بسيطة بلغة Python تقرأ محتوى ملف نصي وتتعامل مع الأخطاء المحتملة التي قد تحدث أثناء قراءة الملف، مثل عدم وجود الملف أو عدم وجود الصلاحيات اللازمة لقراءته.\n\n### الكود:\n\n```python\ndef read_file(file_path):\n    try:\n        with open(file_path, 'r', encoding='utf-8') as file:\n            content = file.read()\n            return content\n    except FileNotFoundError:\n        print(f\"الخطأ: الملف '{file_path}' غير موجود.\")\n    except PermissionError:\n        print(f\"الخطأ: لا توجد صلاحيات كافية لقراءة الملف '{file_path}'.\")\n    except Exception as e:\n        print(f\"حدث خطأ غير متوقع: {e}\")\n    return None\n```\n\n### شرح الكود:\n\n1. **`try`**: يُستخدم لتجربة تنفيذ الكود الذي قد يسبب خطأً.\n2. **`with open(...)`**: تُفتح الملف بطريقة آمنة، وستُغلق تلقائيًا بعد الانتهاء.\n3. **`except FileNotFoundError`**: يلتقط الحالة التي لا يوجد فيها الملف.\n4. **`except PermissionError`**: يلتقط الحالة التي لا يملك فيها البرنامج صلاحية قراءة الملف.\n5. **`except Exception as e`**: يلتقط أي خطأ آخر غير متوقع.\n6. **`return None`**: في حال وجود خطأ، تُرجع الدالة `None` للإشارة إلى فشل القراءة.\n\n### مثال على الاستخدام:\n\n```python\ncontent = read_file(\"example.txt\")\nif content:\n    print(\"محتوى الملف:\")\n    print(content)\n```\n\n### ملاحظات:\n- يُفضل دائمًا تحديد نوع الخطأ المتوقع لكي نتمكن من التعامل معه بشكل دقيق.\n- استخدام `with` يضمن أن الملف سيُغلق حتى لو حدث خطأ أثناء قراءته.\n\nهل ترغب في إضافة دعم لقراءة ملفات JSON أو CSV أيضًا؟", "response_time": 28.13, "success": true, "expected_keywords": ["try", "except", "file", "open"], "timestamp": "2025-07-28T04:48:20.563410"}, {"test_name": "البرمجة الكائنية", "prompt": "اكتب كلاس Python لنظام إدارة المكتبة مع الوراثة", "response": "بالطبع! إليك مثالاً على كلاس Python لنظام إدارة مكتبة باستخدام مفهوم **الوراثة (Inheritance)**. سيتم تقسيم النظام إلى كلاسات رئيسية مثل `Person`، `Book`، و `Library`, مع استخدام الوراثة لإنشاء أنواع مختلفة من المستخدمين مثل `Member` و `Librarian`.\n\n---\n\n### 🔧 **أمثلة على الكلاسات:**\n\n1. **كلاس Person (الشخص)** – كلاس أساسي يمثل أي شخص في النظام.\n2. **كلاس Member (العضو)** – يرث من Person ويضيف خصائص خاصة بالأعضاء.\n3. **كلا<PERSON> (أمين المكتبة)** – يرث من Person.\n4. **كلاس Book (الكتاب)** – يمثل كتباً في المكتبة.\n5. **كلاس Library (المكتبة)** – لإدارة الكتب والأعضاء.\n\n---\n\n### ✅ **الكود الكامل:**\n\n```python\n# كلاس أساسي يمثل الشخص\nclass Person:\n    def __init__(self, name, email):\n        self.name = name\n        self.email = email\n\n    def __str__(self):\n        return f\"Name: {self.name}, Email: {self.email}\"\n\n# كلاس العضو - يرث من Person\nclass Member(Person):\n    def __init__(self, name, email, member_id):\n        super().__init__(name, email)\n        self.member_id = member_id\n        self.borrowed_books = []\n\n    def borrow_book(self, book):\n        if book.available:\n            self.borrowed_books.append(book)\n            book.available = False\n            print(f\"{self.name} قام باستعارة الكتاب: {book.title}\")\n        else:\n            print(f\"الكتاب '{book.title}' غير متوفر حالياً.\")\n\n    def return_book(self, book):\n        if book in self.borrowed_books:\n            self.borrowed_books.remove(book)\n            book.available = True\n            print(f\"{self.name} قام بإرجاع الكتاب: {book.title}\")\n        else:\n            print(f\"{self.name} لم يستعير الكتاب '{book.title}'.\")\n\n# كلاس أمين المكتبة - يرث من Person\nclass Librarian(Person):\n    def __init__(self, name, email, employee_id):\n        super().__init__(name, email)\n        self.employee_id = employee_id\n\n    def add_book(self, library, book):\n        library.add_book(book)\n        print(f\"الأمين {self.name} أضاف كتاب: {book.title}\")\n\n# كلاس الكتاب\nclass Book:\n    def __init__(self, title, author):\n        self.title = title\n        self.author = author\n        self.available = True\n\n    def __str__(self):\n        status = \"متوفر\" if self.available else \"غير متوفر\"\n        return f\"Title: {self.title}, Author: {self.author}, Status: {status}\"\n\n# كلاس المكتبة\nclass Library:\n    def __init__(self):\n        self.books = []\n        self.members = []\n\n    def add_book(self, book):\n        self.books.append(book)\n\n    def register_member(self, member):\n        self.members.append(member)\n\n    def list_books(self):\n        print(\"\\n--- قائمة الكتب ---\")\n        for book in self.books:\n            print(book)\n\n    def list_members(self):\n        print(\"\\n--- قائمة الأعضاء ---\")\n        for member in self.members:\n            print(member)\n```\n\n---\n\n### 🧪 **مثال على الاستخدام:**\n\n```python\n# إنشاء كائنات\n\n# إنشاء مكتبة\nlibrary = Library()\n\n# إنشاء كتب\nbook1 = Book(\"1984\", \"George Orwell\")\nbook2 = Book(\"To Kill a Mockingbird\", \"Harper Lee\")\n\n# إنشاء أمين مكتبة\nlibrarian = Librarian(\"أحمد\", \"<EMAIL>\", \"L001\")\n\n# إنشاء أعضاء\nmember1 = Member(\"سارة\", \"<EMAIL>\", \"M001\")\nmember2 = Member(\"ليلى\", \"<EMAIL>\", \"M002\")\n\n# تشغيل العمليات\n\n# الأمين يضيف الكتب\nlibrarian.add_book(library, book1)\nlibrarian.add_book(library, book2)\n\n# تسجيل الأعضاء\nlibrary.register_member(member1)\nlibrary.register_member(member2)\n\n# عرض الكتب والأعضاء\nlibrary.list_books()\nlibrary.list_members()\n\n# العضو يستعير كتاباً\nmember1.borrow_book(book1)\nmember1.borrow_book(book2)\n\n# العضو يعيد كتاباً\nmember1.return_book(book1)\n```\n\n---\n\n### ✅ **النتيجة المتوقعة:**\n\n```\nالأمين أحمد أضاف كتاب: 1984\nالأمين أحمد أضاف كتاب: To Kill a Mockingbird\n\n--- قائمة الكتب ---\nTitle: 1984, Author: George Orwell, Status: متوفر\nTitle: To Kill a Mockingbird, Author: Harper Lee, Status: متوفر\n\n--- قائمة الأعضاء ---\nName: سارة, Email: <EMAIL>\nName: ليلى, Email: <EMAIL>\n\nسارة قام باستعارة الكتاب: 1984\nسارة قام باستعارة الكتاب: To Kill a Mockingbird\nسارة قام بإرجاع الكتاب: 1984\n```\n\n---\n\nهل ترغب في إضافة ميزات مثل **البحث عن كتاب** أو **التسجيل والتاريخ** أو **الغرامات**؟", "response_time": 56.37, "success": true, "expected_keywords": ["class", "inheritance", "library", "__init__"], "timestamp": "2025-07-28T04:49:16.931570"}]}