#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 رفع بسيط على GitHub باستخدام Token
=====================================
"""

import os
import subprocess
import sys

def main():
    print("🚀 رفع مشروع Universal AI Assistants على GitHub")
    print("=" * 60)
    
    # التحقق من وجود المجلد النظيف
    clean_dir = "universal-ai-assistants-clean-upload"
    if not os.path.exists(clean_dir):
        print(f"❌ المجلد {clean_dir} غير موجود!")
        print("🔄 يرجى تشغيل UPLOAD_TO_EXISTING_REPO.py أولاً")
        return
    
    print(f"✅ تم العثور على المجلد النظيف: {clean_dir}")
    
    # طلب بيانات المستخدم
    print("\n📝 أدخل بياناتك:")
    username = input("👤 اسم المستخدم GitHub (amrashour2): ").strip() or "amrashour2"
    email = input("📧 البريد الإلكتروني: ").strip() or f"{username}@example.com"
    
    print(f"\n🔑 أدخل GitHub Personal Access Token:")
    print("   (احصل عليه من: https://github.com/settings/tokens)")
    token = input("🔐 Token (يبدأ بـ ghp_): ").strip()
    
    if not token:
        print("❌ يجب إدخال Token!")
        return
    
    # الانتقال للمجلد النظيف
    os.chdir(clean_dir)
    print(f"\n📁 تم الانتقال إلى: {os.getcwd()}")
    
    try:
        # إعداد Git
        print("\n⚙️ إعداد Git...")
        subprocess.run(['git', 'config', 'user.name', username], check=True)
        subprocess.run(['git', 'config', 'user.email', email], check=True)
        print(f"✅ تم إعداد Git للمستخدم: {username}")
        
        # تهيئة Git إذا لم يكن مُهيأ
        if not os.path.exists('.git'):
            print("🔄 تهيئة Git...")
            subprocess.run(['git', 'init'], check=True)
            subprocess.run(['git', 'add', '.'], check=True)
            subprocess.run(['git', 'commit', '-m', '🎉 Universal AI Assistants - Complete Project'], check=True)
            print("✅ تم إنشاء commit أولي")
        
        # إعداد remote مع Token
        repo_url = f"https://{token}@github.com/{username}/universal-ai-assistants-clean.git"
        
        # إزالة remote القديم إذا كان موجوداً
        try:
            subprocess.run(['git', 'remote', 'remove', 'origin'], capture_output=True)
        except:
            pass
        
        subprocess.run(['git', 'remote', 'add', 'origin', repo_url], check=True)
        print("✅ تم إعداد remote مع Token")
        
        # رفع المشروع
        print("\n🚀 رفع المشروع على GitHub...")
        subprocess.run(['git', 'branch', '-M', 'main'], check=True)
        
        result = subprocess.run(['git', 'push', '-u', 'origin', 'main'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("🎉 تم رفع المشروع بنجاح!")
            print(f"🌐 رابط المستودع: https://github.com/{username}/universal-ai-assistants-clean")
            
            # إزالة Token من remote للأمان
            safe_url = f"https://github.com/{username}/universal-ai-assistants-clean.git"
            subprocess.run(['git', 'remote', 'set-url', 'origin', safe_url], check=True)
            print("🔐 تم إزالة Token من Git للأمان")
            
        else:
            print("❌ فشل في رفع المشروع:")
            print(result.stderr)
            
            if "404" in result.stderr:
                print("\n💡 المستودع غير موجود. يرجى إنشاؤه أولاً:")
                print(f"   🌐 https://github.com/new")
                print(f"   📝 اسم المستودع: universal-ai-assistants-clean")
                print(f"   🔓 نوع: Public")
            elif "403" in result.stderr:
                print("\n💡 مشكلة في الصلاحيات:")
                print("   1. تأكد من صحة الـ Token")
                print("   2. تأكد من صلاحية 'repo' في الـ Token")
                print("   3. تأكد من اسم المستخدم")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في Git: {str(e)}")
    except Exception as e:
        print(f"❌ خطأ عام: {str(e)}")
    finally:
        # العودة للمجلد الأصلي
        os.chdir('..')

if __name__ == "__main__":
    main()
