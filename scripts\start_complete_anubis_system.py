#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 سكريبت تشغيل نظام أنوبيس المتكامل الكامل
Complete Anubis System Startup Script
"""

import os
import sys
import time
import json
import subprocess
import mysql.connector
from pathlib import Path

# ألوان للإخراج
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    NC = '\033[0m'

def print_header():
    print(f"{Colors.PURPLE}{'='*60}{Colors.NC}")
    print(f"{Colors.PURPLE}🏺 نظام أنوبيس المتكامل الكامل{Colors.NC}")
    print(f"{Colors.PURPLE}Complete Anubis Integrated System{Colors.NC}")
    print(f"{Colors.PURPLE}{'='*60}{Colors.NC}")

def print_step(message):
    print(f"{Colors.BLUE}📋 {message}{Colors.NC}")

def print_success(message):
    print(f"{Colors.GREEN}✅ {message}{Colors.NC}")

def print_warning(message):
    print(f"{Colors.YELLOW}⚠️ {message}{Colors.NC}")

def print_error(message):
    print(f"{Colors.RED}❌ {message}{Colors.NC}")

def run_command(cmd, description=""):
    """تشغيل أمر مع معالجة الأخطاء"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            if description:
                print_success(description)
            return True, result.stdout
        else:
            if description:
                print_error(f"{description}: {result.stderr}")
            return False, result.stderr
    except Exception as e:
        if description:
            print_error(f"{description}: {e}")
        return False, str(e)

def test_mysql_connection():
    """اختبار الاتصال بقاعدة البيانات MySQL المحلية"""
    print_step("اختبار الاتصال بقاعدة البيانات MySQL المحلية...")
    
    try:
        connection = mysql.connector.connect(
            host='localhost',
            port=3306,
            user='root',
            password = "[DATABASE_PASSWORD]"',
            database='anubis_system'
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print_success(f"متصل بـ MySQL: {version[0]}")
            
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            print_success(f"عدد الجداول الموجودة: {len(tables)}")
            
            cursor.close()
            connection.close()
            return True
            
    except mysql.connector.Error as e:
        print_error(f"فشل الاتصال بقاعدة البيانات: {e}")
        return False

def cleanup_containers():
    """تنظيف الحاويات الموجودة"""
    print_step("تنظيف الحاويات الموجودة...")
    
    containers = [
        'anubis-core-server', 'anubis-mcp-server', 'horus-team-server',
        'anubis-web-client-server', 'anubis-redis'
    ]
    
    for container in containers:
        run_command(f"docker rm -f {container}")
    
    print_success("تم تنظيف الحاويات")

def setup_network():
    """إعداد شبكة Docker"""
    print_step("إعداد شبكة Docker...")
    
    # حذف الشبكة إذا كانت موجودة
    run_command("docker network rm anubis-network")
    
    # إنشاء شبكة جديدة
    success, _ = run_command("docker network create anubis-network", "إنشاء شبكة anubis-network")
    return success

def start_redis():
    """تشغيل Redis"""
    print_step("تشغيل Redis...")
    
    cmd = """docker run -d \
        --name anubis-redis \
        --network anubis-network \
        -p 6379:6379 \
        redis:7-alpine redis-server --requirepass anubis_redis_pass"""
    
    success, _ = run_command(cmd, "تشغيل Redis")
    if success:
        time.sleep(3)  # انتظار Redis
    return success

def start_anubis_core():
    """تشغيل نظام أنوبيس الأساسي"""
    print_step("تشغيل نظام أنوبيس الأساسي...")
    
    # إنشاء ملف تشغيل مؤقت
    startup_script = """
import sys
import os
sys.path.append('/app')
sys.path.append('/app/src')

# إعداد متغيرات البيئة
os.environ['DATABASE_URL'] = 'mysql://root:[DATABASE_PASSWORD]@host.docker.internal:3306/anubis_system'
os.environ['REDIS_URL'] = 'redis://anubis-redis:6379'
os.environ['ANUBIS_ENV'] = 'production'

try:
    from src.core.main import app
    import uvicorn
    print("🏺 تشغيل نظام أنوبيس الأساسي...")
    uvicorn.run(app, host="0.0.0.0", port=8000)
except ImportError:
    try:
        from main import app
        import uvicorn
        print("🏺 تشغيل نظام أنوبيس الأساسي...")
        uvicorn.run(app, host="0.0.0.0", port=8000)
    except:
        print("🏺 تشغيل خادم بسيط...")
        from http.server import HTTPServer, BaseHTTPRequestHandler
        import json
        
        class AnubisHandler(BaseHTTPRequestHandler):
            def do_GET(self):
                if self.path == '/health':
                    self.send_response(200)
                    self.send_header('Content-type', 'application/json')
                    self.end_headers()
                    response = {"status": "healthy", "service": "anubis-core", "database": "mysql-connected"}
                    self.wfile.write(json.dumps(response).encode())
                else:
                    self.send_response(200)
                    self.send_header('Content-type', 'text/html')
                    self.end_headers()
                    html = '''
                    <html><body style="font-family: Arial; text-align: center; padding: 50px;">
                    <h1>🏺 نظام أنوبيس الأساسي</h1>
                    <p>Anubis Core System</p>
                    <p>متصل بقاعدة البيانات MySQL المحلية</p>
                    <a href="/health">فحص الصحة</a>
                    </body></html>
                    '''
                    self.wfile.write(html.encode())
        
        server = HTTPServer(('0.0.0.0', 8000), AnubisHandler)
        print("🏺 خادم أنوبيس يعمل على المنفذ 8000")
        server.serve_forever()
"""
    
    with open("anubis_startup.py", "w", encoding="utf-8") as f:
        f.write(startup_script)
    
    cmd = f"""docker run -d \
        --name anubis-core-server \
        --network anubis-network \
        -p 8000:8000 \
        -e DATABASE_URL=mysql://root:[DATABASE_PASSWORD]@host.docker.internal:3306/anubis_system \
        -e REDIS_URL=redis://anubis-redis:6379 \
        -e ANUBIS_ENV=production \
        -v {os.getcwd()}/anubis_startup.py:/app/startup.py \
        -v {os.getcwd()}/ANUBIS_SYSTEM:/app \
        anubis-core python /app/startup.py"""
    
    success, _ = run_command(cmd, "تشغيل نظام أنوبيس الأساسي")
    return success

def start_mcp_system():
    """تشغيل نظام MCP"""
    print_step("تشغيل نظام MCP...")
    
    # إنشاء ملف index.js مؤقت
    mcp_script = """
const express = require('express');
const app = express();
const port = 3000;

app.use(express.json());

app.get('/', (req, res) => {
    res.json({
        service: 'Anubis MCP System',
        status: 'running',
        message: 'نظام MCP يعمل بنجاح'
    });
});

app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        service: 'anubis-mcp',
        timestamp: new Date().toISOString()
    });
});

app.listen(port, '0.0.0.0', () => {
    console.log(`🔗 نظام MCP يعمل على المنفذ ${port}`);
});
"""
    
    with open("mcp_index.js", "w", encoding="utf-8") as f:
        f.write(mcp_script)
    
    cmd = f"""docker run -d \
        --name anubis-mcp-server \
        --network anubis-network \
        -p 3000:3000 \
        -e MCP_SECRET_KEY=mcp_secret_key_2024 \
        -v {os.getcwd()}/mcp_index.js:/app/index.js \
        anubis-mcp"""
    
    success, _ = run_command(cmd, "تشغيل نظام MCP")
    return success

def start_horus_team():
    """تشغيل فريق حورس"""
    print_step("تشغيل فريق حورس...")
    
    # إنشاء ملف تشغيل حورس
    horus_script = """
import sys
import os
from http.server import HTTPServer, BaseHTTPRequestHandler
import json

class HorusHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/health':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {
                "status": "healthy", 
                "service": "horus-team",
                "team_members": ["THOTH", "PTAH", "RA", "KHNUM", "SESHAT", "ANUBIS", "MAAT", "HAPI"]
            }
            self.wfile.write(json.dumps(response).encode())
        elif self.path == '/team':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            team = {
                "THOTH": "المحلل السريع",
                "PTAH": "المطور الخبير", 
                "RA": "المستشار الاستراتيجي",
                "KHNUM": "المبدع والمبتكر",
                "SESHAT": "المحللة البصرية",
                "ANUBIS": "حارس الأمان",
                "MAAT": "حارسة العدالة",
                "HAPI": "محلل البيانات"
            }
            self.wfile.write(json.dumps(team, ensure_ascii=False).encode('utf-8'))
        else:
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            html = '''
            <html><body style="font-family: Arial; text-align: center; padding: 50px;">
            <h1>𓅃 فريق حورس</h1>
            <p>Horus AI Team</p>
            <p>فريق الذكاء الاصطناعي التعاوني</p>
            <a href="/team">عرض الفريق</a> | <a href="/health">فحص الصحة</a>
            </body></html>
            '''
            self.wfile.write(html.encode('utf-8'))

server = HTTPServer(('0.0.0.0', 7000), HorusHandler)
print("𓅃 فريق حورس يعمل على المنفذ 7000")
server.serve_forever()
"""
    
    with open("horus_startup.py", "w", encoding="utf-8") as f:
        f.write(horus_script)
    
    cmd = f"""docker run -d \
        --name horus-team-server \
        --network anubis-network \
        -p 7000:7000 \
        -e HORUS_SECRET_KEY=horus_secret_key_2024 \
        -v {os.getcwd()}/horus_startup.py:/app/startup.py \
        horus-team python /app/startup.py"""
    
    success, _ = run_command(cmd, "تشغيل فريق حورس")
    return success

def check_all_services():
    """فحص جميع الخدمات"""
    print_step("فحص جميع الخدمات...")
    
    services = [
        ('أنوبيس الأساسي', 'http://localhost:8000/health'),
        ('نظام MCP', 'http://localhost:3000/health'),
        ('فريق حورس', 'http://localhost:7000/health'),
        ('الواجهة الموحدة', 'http://localhost:5000/health')
    ]
    
    time.sleep(15)  # انتظار حتى تبدأ جميع الخدمات
    
    for name, url in services:
        try:
            import requests
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                print_success(f"{name}: متاح ✅")
            else:
                print_warning(f"{name}: غير متاح (كود: {response.status_code})")
        except:
            print_warning(f"{name}: غير متاح")

def show_final_info():
    """عرض المعلومات النهائية"""
    print_step("معلومات النظام المتكامل:")
    
    print(f"{Colors.CYAN}🌐 جميع الخدمات المتاحة:{Colors.NC}")
    print(f"{Colors.GREEN}🏺 نظام أنوبيس: http://localhost:8000{Colors.NC}")
    print(f"{Colors.GREEN}🔗 نظام MCP: http://localhost:3000{Colors.NC}")
    print(f"{Colors.GREEN}𓅃 فريق حورس: http://localhost:7000{Colors.NC}")
    print(f"{Colors.GREEN}🌐 الواجهة الموحدة: http://localhost:5000{Colors.NC}")
    print(f"{Colors.GREEN}🗄️ قاعدة البيانات: MySQL محلية (6 جداول){Colors.NC}")
    print(f"{Colors.GREEN}💾 Redis: localhost:6379{Colors.NC}")
    
    print(f"\n{Colors.CYAN}🔧 أوامر الإدارة:{Colors.NC}")
    print(f"{Colors.YELLOW}عرض الحاويات: docker ps{Colors.NC}")
    print(f"{Colors.YELLOW}عرض السجلات: docker logs [container-name]{Colors.NC}")
    print(f"{Colors.YELLOW}إيقاف النظام: docker stop anubis-core-server anubis-mcp-server horus-team-server{Colors.NC}")

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # فحص المتطلبات
    if not test_mysql_connection():
        print_error("فشل الاتصال بقاعدة البيانات MySQL المحلية")
        return False
    
    # تنظيف الحاويات
    cleanup_containers()
    
    # إعداد الشبكة
    if not setup_network():
        return False
    
    # تشغيل الخدمات بالتتابع
    if not start_redis():
        return False
    
    if not start_anubis_core():
        return False
    
    if not start_mcp_system():
        return False
    
    if not start_horus_team():
        return False
    
    # فحص جميع الخدمات
    check_all_services()
    
    # عرض المعلومات النهائية
    show_final_info()
    
    print_success("تم تشغيل نظام أنوبيس المتكامل الكامل بنجاح! 🎉")
    
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print_warning("\nتم إيقاف التشغيل بواسطة المستخدم")
    except Exception as e:
        print_error(f"خطأ غير متوقع: {e}")
