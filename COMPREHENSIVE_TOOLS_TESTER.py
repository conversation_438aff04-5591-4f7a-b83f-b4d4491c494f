#!/usr/bin/env python3
"""
🧪 اختبار شامل لجميع أدوات مشروع Universal AI Assistants
نظام اختبار متقدم لفحص جميع المكونات والأدوات
"""

import os
import sys
import json
import time
import subprocess
import importlib.util
from pathlib import Path
from datetime import datetime
import traceback

class ComprehensiveToolsTester:
    def __init__(self):
        self.project_root = Path.cwd()
        self.test_results = {
            "timestamp": datetime.now().isoformat(),
            "total_tests": 0,
            "passed_tests": 0,
            "failed_tests": 0,
            "skipped_tests": 0,
            "test_details": {}
        }
        self.colors = {
            "GREEN": "\033[92m",
            "RED": "\033[91m",
            "YELLOW": "\033[93m",
            "BLUE": "\033[94m",
            "RESET": "\033[0m",
            "BOLD": "\033[1m"
        }
    
    def print_status(self, message, status="INFO"):
        """طباعة الحالة مع الألوان"""
        color_map = {
            "PASS": "GREEN",
            "FAIL": "RED", 
            "SKIP": "YELLOW",
            "INFO": "BLUE"
        }
        color = self.colors.get(color_map.get(status, "BLUE"), "")
        reset = self.colors["RESET"]
        print(f"{color}{message}{reset}")
    
    def test_python_script(self, script_path, test_name):
        """اختبار سكريبت Python"""
        self.test_results["total_tests"] += 1
        
        try:
            if not script_path.exists():
                self.print_status(f"❌ {test_name}: الملف غير موجود", "FAIL")
                self.test_results["failed_tests"] += 1
                self.test_results["test_details"][test_name] = {
                    "status": "FAILED",
                    "error": "File not found",
                    "path": str(script_path)
                }
                return False
            
            # فحص صحة الصيغة
            with open(script_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            try:
                compile(content, script_path, 'exec')
                self.print_status(f"✅ {test_name}: صحة الصيغة", "PASS")
                self.test_results["passed_tests"] += 1
                self.test_results["test_details"][test_name] = {
                    "status": "PASSED",
                    "path": str(script_path),
                    "size_kb": script_path.stat().st_size / 1024
                }
                return True
            except SyntaxError as e:
                self.print_status(f"❌ {test_name}: خطأ في الصيغة - {e}", "FAIL")
                self.test_results["failed_tests"] += 1
                self.test_results["test_details"][test_name] = {
                    "status": "FAILED",
                    "error": f"Syntax Error: {str(e)}",
                    "path": str(script_path)
                }
                return False
                
        except Exception as e:
            self.print_status(f"❌ {test_name}: خطأ - {e}", "FAIL")
            self.test_results["failed_tests"] += 1
            self.test_results["test_details"][test_name] = {
                "status": "FAILED",
                "error": str(e),
                "path": str(script_path)
            }
            return False
    
    def test_json_file(self, json_path, test_name):
        """اختبار ملف JSON"""
        self.test_results["total_tests"] += 1
        
        try:
            if not json_path.exists():
                self.print_status(f"⚠️ {test_name}: الملف غير موجود", "SKIP")
                self.test_results["skipped_tests"] += 1
                return False
            
            with open(json_path, 'r', encoding='utf-8') as f:
                json.load(f)
            
            self.print_status(f"✅ {test_name}: JSON صالح", "PASS")
            self.test_results["passed_tests"] += 1
            self.test_results["test_details"][test_name] = {
                "status": "PASSED",
                "path": str(json_path),
                "size_kb": json_path.stat().st_size / 1024
            }
            return True
            
        except json.JSONDecodeError as e:
            self.print_status(f"❌ {test_name}: JSON غير صالح - {e}", "FAIL")
            self.test_results["failed_tests"] += 1
            self.test_results["test_details"][test_name] = {
                "status": "FAILED",
                "error": f"JSON Error: {str(e)}",
                "path": str(json_path)
            }
            return False
        except Exception as e:
            self.print_status(f"❌ {test_name}: خطأ - {e}", "FAIL")
            self.test_results["failed_tests"] += 1
            return False
    
    def test_directory_structure(self, dir_path, test_name):
        """اختبار هيكل المجلد"""
        self.test_results["total_tests"] += 1
        
        try:
            if not dir_path.exists():
                self.print_status(f"❌ {test_name}: المجلد غير موجود", "FAIL")
                self.test_results["failed_tests"] += 1
                return False
            
            if not dir_path.is_dir():
                self.print_status(f"❌ {test_name}: ليس مجلد", "FAIL")
                self.test_results["failed_tests"] += 1
                return False
            
            file_count = len(list(dir_path.rglob("*")))
            self.print_status(f"✅ {test_name}: {file_count} ملف", "PASS")
            self.test_results["passed_tests"] += 1
            self.test_results["test_details"][test_name] = {
                "status": "PASSED",
                "path": str(dir_path),
                "file_count": file_count
            }
            return True
            
        except Exception as e:
            self.print_status(f"❌ {test_name}: خطأ - {e}", "FAIL")
            self.test_results["failed_tests"] += 1
            return False
    
    def test_horus_team(self):
        """اختبار فريق حورس"""
        self.print_status("\n🤖 اختبار فريق حورس...", "INFO")
        
        # اختبار الهيكل الأساسي
        horus_path = self.project_root / "HORUS_AI_TEAM"
        self.test_directory_structure(horus_path, "HORUS_AI_TEAM Structure")
        
        # اختبار الملفات الرئيسية
        key_files = [
            "summon_horus_assistant.py",
            "quick_start_fixed.py",
            "horus_direct_test.py"
        ]
        
        for file_name in key_files:
            file_path = horus_path / file_name
            self.test_python_script(file_path, f"HORUS {file_name}")
        
        # اختبار المجلدات الفرعية
        subdirs = [
            "01_core", "02_team_members", "03_memory_system",
            "04_collaboration", "05_analysis", "06_documentation"
        ]
        
        for subdir in subdirs:
            subdir_path = horus_path / subdir
            self.test_directory_structure(subdir_path, f"HORUS {subdir}")
    
    def test_anubis_system(self):
        """اختبار نظام أنوبيس"""
        self.print_status("\n🏺 اختبار نظام أنوبيس...", "INFO")
        
        anubis_path = self.project_root / "ANUBIS_SYSTEM"
        self.test_directory_structure(anubis_path, "ANUBIS_SYSTEM Structure")
        
        # اختبار الملفات الرئيسية
        key_files = [
            "main.py",
            "quick_start_anubis.py",
            "startup.py"
        ]
        
        for file_name in key_files:
            file_path = anubis_path / file_name
            self.test_python_script(file_path, f"ANUBIS {file_name}")
        
        # اختبار ملفات التكوين
        config_files = [
            "config/ai_config.json",
            "config/default_config.json"
        ]
        
        for config_file in config_files:
            config_path = anubis_path / config_file
            self.test_json_file(config_path, f"ANUBIS {config_file}")
    
    def test_mcp_system(self):
        """اختبار نظام MCP"""
        self.print_status("\n🔗 اختبار نظام MCP...", "INFO")
        
        mcp_path = self.project_root / "ANUBIS_HORUS_MCP"
        self.test_directory_structure(mcp_path, "ANUBIS_HORUS_MCP Structure")
        
        # اختبار ملفات Node.js
        node_files = [
            "package.json",
            "mcp-config.json"
        ]
        
        for file_name in node_files:
            file_path = mcp_path / file_name
            self.test_json_file(file_path, f"MCP {file_name}")
        
        # اختبار ملفات Python
        python_files = [
            "collaborative_ai_system.py",
            "advanced_collaborative_system.py"
        ]
        
        for file_name in python_files:
            file_path = mcp_path / file_name
            self.test_python_script(file_path, f"MCP {file_name}")
    
    def test_scripts_directory(self):
        """اختبار مجلد السكريبتات"""
        self.print_status("\n🔧 اختبار السكريبتات...", "INFO")
        
        scripts_path = self.project_root / "scripts"
        self.test_directory_structure(scripts_path, "Scripts Directory")
        
        # اختبار عينة من السكريبتات المهمة
        important_scripts = [
            "QUICK_START.py",
            "INTEGRATE_ALL_PROJECTS.py",
            "LAUNCH_ANUBIS_COMPLETE.py",
            "HORUS_PROJECT_ANALYZER.py"
        ]
        
        for script_name in important_scripts:
            script_path = scripts_path / script_name
            self.test_python_script(script_path, f"Script {script_name}")
    
    def test_documentation(self):
        """اختبار التوثيق"""
        self.print_status("\n📚 اختبار التوثيق...", "INFO")
        
        docs_path = self.project_root / "PROJECT_DOCUMENTATION"
        self.test_directory_structure(docs_path, "Documentation Directory")
        
        # فحص وجود ملفات README مهمة
        readme_files = [
            self.project_root / "README.md",
            self.project_root / "HORUS_AI_TEAM" / "README.md",
            self.project_root / "ANUBIS_SYSTEM" / "README.md"
        ]
        
        for readme_path in readme_files:
            if readme_path.exists():
                self.print_status(f"✅ README موجود: {readme_path.name}", "PASS")
                self.test_results["passed_tests"] += 1
            else:
                self.print_status(f"❌ README مفقود: {readme_path}", "FAIL")
                self.test_results["failed_tests"] += 1
            self.test_results["total_tests"] += 1
    
    def test_cloud_deployment_files(self):
        """اختبار ملفات النشر السحابي"""
        self.print_status("\n☁️ اختبار ملفات النشر السحابي...", "INFO")
        
        # اختبار ملفات Docker
        docker_files = [
            self.project_root / "ANUBIS_SYSTEM" / "Dockerfile",
            self.project_root / "HORUS_AI_TEAM" / "Dockerfile",
            self.project_root / "ANUBIS_HORUS_MCP" / "Dockerfile"
        ]
        
        for docker_file in docker_files:
            self.test_results["total_tests"] += 1
            if docker_file.exists():
                self.print_status(f"✅ Dockerfile موجود: {docker_file.parent.name}", "PASS")
                self.test_results["passed_tests"] += 1
            else:
                self.print_status(f"❌ Dockerfile مفقود: {docker_file}", "FAIL")
                self.test_results["failed_tests"] += 1
        
        # اختبار ملفات التكوين السحابي
        cloud_files = [
            "fix_cloud_deployment.py",
            "FINAL_CLOUD_STATUS_REPORT.md"
        ]
        
        for file_name in cloud_files:
            file_path = self.project_root / file_name
            if file_path.exists():
                if file_name.endswith('.py'):
                    self.test_python_script(file_path, f"Cloud {file_name}")
                else:
                    self.test_results["total_tests"] += 1
                    self.print_status(f"✅ ملف النشر موجود: {file_name}", "PASS")
                    self.test_results["passed_tests"] += 1
    
    def run_comprehensive_test(self):
        """تشغيل الاختبار الشامل"""
        self.print_status("🧪 بدء الاختبار الشامل لجميع أدوات المشروع", "INFO")
        self.print_status("=" * 60, "INFO")
        
        start_time = time.time()
        
        # تشغيل جميع الاختبارات
        self.test_horus_team()
        self.test_anubis_system()
        self.test_mcp_system()
        self.test_scripts_directory()
        self.test_documentation()
        self.test_cloud_deployment_files()
        
        end_time = time.time()
        duration = end_time - start_time
        
        # حساب النتائج النهائية
        success_rate = (self.test_results["passed_tests"] / self.test_results["total_tests"]) * 100 if self.test_results["total_tests"] > 0 else 0
        
        # عرض النتائج النهائية
        self.print_status("\n" + "=" * 60, "INFO")
        self.print_status("📊 النتائج النهائية:", "INFO")
        self.print_status("=" * 60, "INFO")
        
        self.print_status(f"⏱️ الوقت المستغرق: {duration:.2f} ثانية", "INFO")
        self.print_status(f"🧪 إجمالي الاختبارات: {self.test_results['total_tests']}", "INFO")
        self.print_status(f"✅ اختبارات ناجحة: {self.test_results['passed_tests']}", "PASS")
        self.print_status(f"❌ اختبارات فاشلة: {self.test_results['failed_tests']}", "FAIL")
        self.print_status(f"⚠️ اختبارات متجاهلة: {self.test_results['skipped_tests']}", "SKIP")
        self.print_status(f"📈 معدل النجاح: {success_rate:.1f}%", "INFO")
        
        # تقييم الحالة العامة
        if success_rate >= 90:
            self.print_status("🏆 التقييم: ممتاز - النظام جاهز للإنتاج", "PASS")
        elif success_rate >= 75:
            self.print_status("👍 التقييم: جيد - يحتاج تحسينات بسيطة", "INFO")
        elif success_rate >= 50:
            self.print_status("⚠️ التقييم: متوسط - يحتاج إصلاحات", "SKIP")
        else:
            self.print_status("❌ التقييم: ضعيف - يحتاج عمل كبير", "FAIL")
        
        # حفظ التقرير
        self.save_test_report(duration, success_rate)
        
        return self.test_results
    
    def save_test_report(self, duration, success_rate):
        """حفظ تقرير الاختبار"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # تقرير JSON
        json_report = {
            **self.test_results,
            "duration_seconds": duration,
            "success_rate": success_rate,
            "summary": {
                "total": self.test_results["total_tests"],
                "passed": self.test_results["passed_tests"],
                "failed": self.test_results["failed_tests"],
                "skipped": self.test_results["skipped_tests"]
            }
        }
        
        json_file = f"comprehensive_tools_test_report_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(json_report, f, indent=2, ensure_ascii=False)
        
        # تقرير Markdown
        md_file = f"COMPREHENSIVE_TOOLS_TEST_REPORT_{timestamp}.md"
        self.create_markdown_report(md_file, duration, success_rate)
        
        self.print_status(f"\n📄 تم حفظ التقرير في: {json_file} و {md_file}", "INFO")
    
    def create_markdown_report(self, file_path, duration, success_rate):
        """إنشاء تقرير Markdown"""
        md_content = f"""# 🧪 تقرير اختبار شامل لأدوات Universal AI Assistants
## تاريخ الاختبار: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

---

## 📊 ملخص النتائج

| المقياس | القيمة |
|---------|--------|
| ⏱️ الوقت المستغرق | {duration:.2f} ثانية |
| 🧪 إجمالي الاختبارات | {self.test_results['total_tests']} |
| ✅ اختبارات ناجحة | {self.test_results['passed_tests']} |
| ❌ اختبارات فاشلة | {self.test_results['failed_tests']} |
| ⚠️ اختبارات متجاهلة | {self.test_results['skipped_tests']} |
| 📈 معدل النجاح | {success_rate:.1f}% |

---

## 🎯 التقييم العام

"""
        
        if success_rate >= 90:
            md_content += "🏆 **ممتاز** - النظام جاهز للإنتاج\n"
        elif success_rate >= 75:
            md_content += "👍 **جيد** - يحتاج تحسينات بسيطة\n"
        elif success_rate >= 50:
            md_content += "⚠️ **متوسط** - يحتاج إصلاحات\n"
        else:
            md_content += "❌ **ضعيف** - يحتاج عمل كبير\n"
        
        md_content += f"""
---

## 📋 تفاصيل الاختبارات

### ✅ الاختبارات الناجحة ({self.test_results['passed_tests']})
"""
        
        for test_name, details in self.test_results["test_details"].items():
            if details["status"] == "PASSED":
                md_content += f"- **{test_name}**: ✅ نجح\n"
        
        md_content += f"""
### ❌ الاختبارات الفاشلة ({self.test_results['failed_tests']})
"""
        
        for test_name, details in self.test_results["test_details"].items():
            if details["status"] == "FAILED":
                error = details.get("error", "Unknown error")
                md_content += f"- **{test_name}**: ❌ فشل - {error}\n"
        
        md_content += """
---

## 🎉 الخلاصة

تم إجراء اختبار شامل لجميع أدوات ومكونات مشروع Universal AI Assistants.
النتائج تظهر الحالة العامة للمشروع وجاهزيته للاستخدام.

**🤖 تم إنشاء هذا التقرير بواسطة نظام الاختبار الشامل**
"""
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(md_content)

def main():
    """الدالة الرئيسية"""
    print("🧪 نظام الاختبار الشامل لأدوات Universal AI Assistants")
    print("=" * 60)
    
    tester = ComprehensiveToolsTester()
    results = tester.run_comprehensive_test()
    
    return results

if __name__ == "__main__":
    main()
