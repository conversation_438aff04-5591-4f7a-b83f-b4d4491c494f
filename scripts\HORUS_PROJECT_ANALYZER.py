#!/usr/bin/env python3
"""
🔍 محلل مشروع Universal AI Assistants مع فريق حورس
تحليل شامل وتنظيم الملفات تلقائياً
"""

import os
import json
import shutil
import time
from pathlib import Path
from datetime import datetime
import subprocess

class HorusProjectAnalyzer:
    def __init__(self):
        self.project_root = Path.cwd()
        self.analysis_results = {}
        self.file_organization_plan = {}
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
    def print_status(self, message, level="INFO"):
        """طباعة حالة مع تنسيق"""
        colors = {
            "INFO": "\033[94m",
            "SUCCESS": "\033[92m",
            "WARNING": "\033[93m", 
            "ERROR": "\033[91m",
            "RESET": "\033[0m"
        }
        print(f"{colors.get(level, '')}{message}{colors['RESET']}")
    
    def analyze_project_structure(self):
        """تحليل هيكل المشروع"""
        self.print_status("🔍 بدء تحليل هيكل المشروع...", "INFO")
        
        # فحص المجلدات الرئيسية
        main_directories = [
            "ANUBIS_SYSTEM",
            "HORUS_AI_TEAM", 
            "ANUBIS_HORUS_MCP",
            "PROJECT_DOCUMENTATION",
            "SHARED_REQUIREMENTS",
            "data",
            "docs",
            "scripts",
            "archive_and_backups"
        ]
        
        structure_analysis = {
            "main_directories": {},
            "loose_files": [],
            "total_files": 0,
            "total_directories": 0
        }
        
        # تحليل المجلدات الرئيسية
        for directory in main_directories:
            dir_path = self.project_root / directory
            if dir_path.exists():
                file_count = len(list(dir_path.rglob("*")))
                structure_analysis["main_directories"][directory] = {
                    "exists": True,
                    "file_count": file_count,
                    "size_mb": self.get_directory_size(dir_path)
                }
                structure_analysis["total_directories"] += 1
            else:
                structure_analysis["main_directories"][directory] = {
                    "exists": False,
                    "file_count": 0,
                    "size_mb": 0
                }
        
        # فحص الملفات المتناثرة في المجلد الرئيسي
        for item in self.project_root.iterdir():
            if item.is_file() and not item.name.startswith('.'):
                structure_analysis["loose_files"].append({
                    "name": item.name,
                    "size_kb": item.stat().st_size / 1024,
                    "extension": item.suffix,
                    "modified": datetime.fromtimestamp(item.stat().st_mtime).isoformat()
                })
                structure_analysis["total_files"] += 1
        
        self.analysis_results["structure"] = structure_analysis
        self.print_status(f"✅ تم تحليل {structure_analysis['total_directories']} مجلد و {len(structure_analysis['loose_files'])} ملف متناثر", "SUCCESS")
        
        return structure_analysis
    
    def get_directory_size(self, directory):
        """حساب حجم المجلد بالميجابايت"""
        try:
            total_size = sum(f.stat().st_size for f in directory.rglob('*') if f.is_file())
            return round(total_size / (1024 * 1024), 2)
        except:
            return 0
    
    def categorize_loose_files(self):
        """تصنيف الملفات المتناثرة"""
        self.print_status("📋 تصنيف الملفات المتناثرة...", "INFO")
        
        categorization_rules = {
            "documentation": {
                "extensions": [".md", ".txt", ".rst"],
                "keywords": ["README", "GUIDE", "REPORT", "ANALYSIS", "STATUS"],
                "target_folder": "PROJECT_DOCUMENTATION"
            },
            "scripts": {
                "extensions": [".py", ".sh", ".bat", ".ps1"],
                "keywords": ["script", "launcher", "start", "fix", "test"],
                "target_folder": "scripts"
            },
            "configuration": {
                "extensions": [".json", ".yml", ".yaml", ".toml", ".ini"],
                "keywords": ["config", "settings", "requirements"],
                "target_folder": "SHARED_REQUIREMENTS/data"
            },
            "data": {
                "extensions": [".db", ".sqlite", ".csv", ".xml"],
                "keywords": ["data", "database", "backup"],
                "target_folder": "data"
            },
            "docker": {
                "extensions": [".dockerfile"],
                "keywords": ["docker", "compose"],
                "target_folder": "ANUBIS_SYSTEM/docker"
            }
        }
        
        categorized_files = {category: [] for category in categorization_rules.keys()}
        uncategorized_files = []
        
        for file_info in self.analysis_results["structure"]["loose_files"]:
            file_name = file_info["name"]
            file_ext = file_info["extension"]
            categorized = False
            
            for category, rules in categorization_rules.items():
                # فحص الامتداد
                if file_ext.lower() in rules["extensions"]:
                    categorized_files[category].append({
                        **file_info,
                        "target_folder": rules["target_folder"],
                        "reason": f"Extension: {file_ext}"
                    })
                    categorized = True
                    break
                
                # فحص الكلمات المفتاحية
                if any(keyword.lower() in file_name.lower() for keyword in rules["keywords"]):
                    categorized_files[category].append({
                        **file_info,
                        "target_folder": rules["target_folder"],
                        "reason": "Keyword match"
                    })
                    categorized = True
                    break
            
            if not categorized:
                uncategorized_files.append(file_info)
        
        self.file_organization_plan = {
            "categorized": categorized_files,
            "uncategorized": uncategorized_files,
            "total_categorized": sum(len(files) for files in categorized_files.values()),
            "total_uncategorized": len(uncategorized_files)
        }
        
        self.print_status(f"✅ تم تصنيف {self.file_organization_plan['total_categorized']} ملف", "SUCCESS")
        self.print_status(f"⚠️ {self.file_organization_plan['total_uncategorized']} ملف غير مصنف", "WARNING")
        
        return self.file_organization_plan
    
    def organize_files(self, dry_run=True):
        """تنظيم الملفات (dry_run=True للمعاينة فقط)"""
        action = "معاينة" if dry_run else "تنفيذ"
        self.print_status(f"📁 {action} تنظيم الملفات...", "INFO")
        
        organization_results = {
            "moved_files": [],
            "failed_moves": [],
            "created_directories": []
        }
        
        for category, files in self.file_organization_plan["categorized"].items():
            for file_info in files:
                source_path = self.project_root / file_info["name"]
                target_folder = self.project_root / file_info["target_folder"]
                target_path = target_folder / file_info["name"]
                
                if dry_run:
                    self.print_status(f"📄 سيتم نقل: {file_info['name']} → {file_info['target_folder']}", "INFO")
                    organization_results["moved_files"].append({
                        "file": file_info["name"],
                        "from": str(source_path),
                        "to": str(target_path),
                        "category": category,
                        "reason": file_info["reason"]
                    })
                else:
                    try:
                        # إنشاء المجلد المستهدف إذا لم يكن موجوداً
                        target_folder.mkdir(parents=True, exist_ok=True)
                        if str(target_folder) not in organization_results["created_directories"]:
                            organization_results["created_directories"].append(str(target_folder))
                        
                        # نقل الملف
                        shutil.move(str(source_path), str(target_path))
                        self.print_status(f"✅ تم نقل: {file_info['name']}", "SUCCESS")
                        organization_results["moved_files"].append({
                            "file": file_info["name"],
                            "from": str(source_path),
                            "to": str(target_path),
                            "category": category,
                            "reason": file_info["reason"]
                        })
                    except Exception as e:
                        self.print_status(f"❌ فشل نقل {file_info['name']}: {str(e)}", "ERROR")
                        organization_results["failed_moves"].append({
                            "file": file_info["name"],
                            "error": str(e)
                        })
        
        return organization_results
    
    def generate_comprehensive_report(self):
        """إنشاء تقرير شامل"""
        self.print_status("📊 إنشاء التقرير الشامل...", "INFO")
        
        report = {
            "timestamp": self.timestamp,
            "project_analysis": self.analysis_results,
            "file_organization": self.file_organization_plan,
            "recommendations": self.generate_recommendations()
        }
        
        # حفظ التقرير JSON
        json_report_path = f"horus_project_analysis_{self.timestamp}.json"
        with open(json_report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        # إنشاء تقرير Markdown
        md_report_path = f"HORUS_PROJECT_ANALYSIS_{self.timestamp}.md"
        self.create_markdown_report(report, md_report_path)
        
        self.print_status(f"✅ تم حفظ التقرير في: {json_report_path} و {md_report_path}", "SUCCESS")
        
        return report
    
    def generate_recommendations(self):
        """إنشاء توصيات للتحسين"""
        recommendations = {
            "immediate_actions": [],
            "medium_term": [],
            "long_term": []
        }
        
        # توصيات فورية
        if self.file_organization_plan["total_uncategorized"] > 0:
            recommendations["immediate_actions"].append(
                f"تصنيف {self.file_organization_plan['total_uncategorized']} ملف غير مصنف يدوياً"
            )
        
        if self.file_organization_plan["total_categorized"] > 10:
            recommendations["immediate_actions"].append(
                "تنفيذ خطة تنظيم الملفات المقترحة"
            )
        
        # توصيات متوسطة المدى
        recommendations["medium_term"].extend([
            "إنشاء نظام تنظيم تلقائي للملفات الجديدة",
            "تطوير معايير تسمية موحدة للملفات",
            "إنشاء نظام نسخ احتياطي منتظم"
        ])
        
        # توصيات طويلة المدى
        recommendations["long_term"].extend([
            "تطوير نظام إدارة محتوى متقدم",
            "تكامل مع أنظمة التحكم في الإصدارات",
            "أتمتة عمليات التنظيف والصيانة"
        ])
        
        return recommendations
    
    def create_markdown_report(self, report, file_path):
        """إنشاء تقرير Markdown"""
        md_content = f"""# 🔍 تقرير تحليل مشروع Universal AI Assistants
## تحليل فريق حورس - {self.timestamp}

---

## 📊 ملخص تنفيذي

**📅 تاريخ التحليل:** {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}  
**🎯 نطاق التحليل:** مشروع Universal AI Assistants الكامل  
**🤖 المحلل:** فريق حورس للذكاء الاصطناعي

---

## 🏗️ هيكل المشروع

### 📁 المجلدات الرئيسية:
"""
        
        for dir_name, info in report["project_analysis"]["structure"]["main_directories"].items():
            status = "✅" if info["exists"] else "❌"
            md_content += f"- **{dir_name}:** {status} ({info['file_count']} ملف، {info['size_mb']} MB)\n"
        
        md_content += f"""
### 📄 الملفات المتناثرة:
- **إجمالي الملفات المتناثرة:** {len(report["project_analysis"]["structure"]["loose_files"])}
- **الملفات المصنفة:** {report["file_organization"]["total_categorized"]}
- **الملفات غير المصنفة:** {report["file_organization"]["total_uncategorized"]}

---

## 📋 خطة التنظيم

### 🎯 التصنيفات المقترحة:
"""
        
        for category, files in report["file_organization"]["categorized"].items():
            if files:
                md_content += f"\n#### {category.title()}:\n"
                for file_info in files[:5]:  # أول 5 ملفات فقط
                    md_content += f"- `{file_info['name']}` → `{file_info['target_folder']}`\n"
                if len(files) > 5:
                    md_content += f"- ... و {len(files) - 5} ملف آخر\n"
        
        md_content += f"""
---

## 🎯 التوصيات

### 🔴 إجراءات فورية:
"""
        for rec in report["recommendations"]["immediate_actions"]:
            md_content += f"- {rec}\n"
        
        md_content += f"""
### 🟡 متوسطة المدى:
"""
        for rec in report["recommendations"]["medium_term"]:
            md_content += f"- {rec}\n"
        
        md_content += f"""
### 🟢 طويلة المدى:
"""
        for rec in report["recommendations"]["long_term"]:
            md_content += f"- {rec}\n"
        
        md_content += f"""
---

## 📞 الخلاصة

تم تحليل المشروع بنجاح وإنشاء خطة تنظيم شاملة. 
يُنصح بتنفيذ التوصيات الفورية أولاً ثم المتابعة مع الخطط طويلة المدى.

**🤖 تم إنشاء هذا التقرير بواسطة فريق حورس للذكاء الاصطناعي**
"""
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(md_content)
    
    def run_full_analysis(self, execute_organization=False):
        """تشغيل التحليل الكامل"""
        self.print_status("🚀 بدء التحليل الشامل لمشروع Universal AI Assistants", "INFO")
        self.print_status("="*60, "INFO")
        
        # 1. تحليل الهيكل
        self.analyze_project_structure()
        
        # 2. تصنيف الملفات
        self.categorize_loose_files()
        
        # 3. معاينة التنظيم
        self.print_status("\n📋 معاينة خطة التنظيم:", "INFO")
        organization_preview = self.organize_files(dry_run=True)
        
        # 4. تنفيذ التنظيم (اختياري)
        if execute_organization:
            self.print_status("\n📁 تنفيذ خطة التنظيم...", "WARNING")
            organization_results = self.organize_files(dry_run=False)
        
        # 5. إنشاء التقرير
        report = self.generate_comprehensive_report()
        
        # 6. عرض النتائج النهائية
        self.print_final_summary()
        
        return report
    
    def print_final_summary(self):
        """عرض الملخص النهائي"""
        self.print_status("="*60, "INFO")
        self.print_status("🎯 ملخص التحليل النهائي:", "INFO")
        self.print_status("="*60, "INFO")
        
        structure = self.analysis_results["structure"]
        organization = self.file_organization_plan
        
        self.print_status(f"📁 المجلدات الرئيسية: {structure['total_directories']}", "INFO")
        self.print_status(f"📄 الملفات المتناثرة: {len(structure['loose_files'])}", "INFO")
        self.print_status(f"✅ ملفات مصنفة: {organization['total_categorized']}", "SUCCESS")
        self.print_status(f"⚠️ ملفات غير مصنفة: {organization['total_uncategorized']}", "WARNING")
        
        self.print_status("="*60, "INFO")
        self.print_status("🎉 تم إكمال التحليل بنجاح!", "SUCCESS")

def main():
    """الدالة الرئيسية"""
    print("🔍 محلل مشروع Universal AI Assistants - فريق حورس")
    print("="*60)
    
    analyzer = HorusProjectAnalyzer()
    
    # تشغيل التحليل (معاينة فقط)
    report = analyzer.run_full_analysis(execute_organization=False)
    
    print("\n🎯 لتنفيذ خطة التنظيم، شغل:")
    print("analyzer.run_full_analysis(execute_organization=True)")
    
    return report

if __name__ == "__main__":
    main()
