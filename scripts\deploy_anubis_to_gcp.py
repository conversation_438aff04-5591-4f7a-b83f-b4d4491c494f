#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 Anubis Deployment to Google Cloud Platform
============================================
نشر شامل لمشروع أنوبيس على Google Cloud بناءً على توصيات Qwen
"""

import subprocess
import os
import time
import json
from datetime import datetime

class AnubisGCPDeployer:
    def __init__(self):
        self.project_id = "anubis-467210"
        self.region = "us-central1"
        self.zone = "us-central1-a"
        self.mysql_instance = "anubis-mysql-db"
        self.bucket_name = "anubis-storage-bucket-unique"
        self.vm_name = "anubis-n8n-ollama-vm"
        
    def print_banner(self):
        """طباعة شعار النشر"""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                🚀 Anubis GCP Deployment                     ║
║                  Based on Qwen Consultation                 ║
╠══════════════════════════════════════════════════════════════╣
║  ☁️ Google Cloud Platform                                   ║
║  🎛️ React Dashboard → Cloud Run                            ║
║  🔄 n8n + Ollama → Compute Engine VM                       ║
║  🗄️ Cloud SQL MySQL                                        ║
║  📦 Cloud Storage                                           ║
║  🔧 Load Balancer & Monitoring                             ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
    
    def run_command(self, command, description=""):
        """تشغيل أمر shell مع معالجة الأخطاء"""
        if description:
            print(f"\n🔧 {description}")
            print(f"📝 الأمر: {command}")
        
        try:
            result = subprocess.run(
                command, 
                shell=True, 
                capture_output=True, 
                text=True,
                timeout=300
            )
            
            if result.returncode == 0:
                print(f"✅ نجح: {description}")
                if result.stdout:
                    print(f"📤 المخرجات: {result.stdout[:200]}...")
                return True
            else:
                print(f"❌ فشل: {description}")
                print(f"🚨 الخطأ: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print(f"⏰ انتهت مهلة الأمر: {description}")
            return False
        except Exception as e:
            print(f"💥 خطأ في تشغيل الأمر: {e}")
            return False
    
    def check_gcloud_auth(self):
        """التحقق من تسجيل الدخول في gcloud"""
        print("\n🔐 التحقق من تسجيل الدخول...")

        result = subprocess.run(
            "gcloud auth list --format='value(account)' --filter='status:ACTIVE'",
            shell=True, capture_output=True, text=True
        )

        if result.returncode == 0 and result.stdout.strip():
            accounts = result.stdout.strip().split('\n')
            active_account = accounts[0] if accounts else ""
            print(f"✅ مسجل الدخول كـ: {active_account}")
            return True
        else:
            print("❌ غير مسجل الدخول. يرجى تسجيل الدخول أولاً:")
            print("gcloud auth login")
            return False
    
    def setup_gcp_project(self):
        """إعداد مشروع Google Cloud"""
        print("\n📋 إعداد مشروع Google Cloud...")
        
        if not self.check_gcloud_auth():
            return False
        
        commands = [
            (f"gcloud config set project {self.project_id}", "تعيين المشروع النشط"),
            ("gcloud services enable container.googleapis.com", "تفعيل Container Registry"),
            ("gcloud services enable run.googleapis.com", "تفعيل Cloud Run"),
            ("gcloud services enable compute.googleapis.com", "تفعيل Compute Engine"),
            ("gcloud services enable sqladmin.googleapis.com", "تفعيل Cloud SQL"),
            ("gcloud services enable storage.googleapis.com", "تفعيل Cloud Storage"),
            ("gcloud services enable monitoring.googleapis.com", "تفعيل Monitoring"),
        ]
        
        success_count = 0
        for command, desc in commands:
            if self.run_command(command, desc):
                success_count += 1
            time.sleep(2)  # انتظار قصير بين الأوامر
        
        print(f"\n📊 تم تفعيل {success_count}/{len(commands)} خدمات")
        return success_count >= len(commands) - 1  # السماح بفشل خدمة واحدة
    
    def create_cloud_storage(self):
        """إنشاء Cloud Storage"""
        print("\n📦 إنشاء Cloud Storage...")
        
        commands = [
            (f"gsutil mb -l {self.region} gs://{self.bucket_name}/", "إنشاء Storage Bucket"),
            (f"gsutil iam ch allUsers:objectViewer gs://{self.bucket_name}", "تعيين صلاحيات القراءة العامة"),
        ]
        
        for command, desc in commands:
            self.run_command(command, desc)
    
    def create_cloud_sql(self):
        """إنشاء Cloud SQL MySQL"""
        print("\n🗄️ إنشاء Cloud SQL MySQL...")
        
        # التحقق من وجود instance مسبقاً
        check_cmd = f"gcloud sql instances describe {self.mysql_instance} --format='value(name)'"
        result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ MySQL instance {self.mysql_instance} موجود مسبقاً")
            return True
        
        commands = [
            (f"gcloud sql instances create {self.mysql_instance} --database-version=MYSQL_8_0 --cpu=1 --memory=4GB --region={self.region} --storage-type=SSD --storage-auto-increase --storage-auto-increase-limit=200", "إنشاء MySQL instance"),
            (f"gcloud sql databases create anubis_db --instance={self.mysql_instance}", "إنشاء قاعدة البيانات"),
            (f"gcloud sql users set-password root --instance={self.mysql_instance} --password=anubis_root_2024", "تعيين كلمة مرور root"),
        ]
        
        for command, desc in commands:
            if not self.run_command(command, desc):
                print(f"⚠️ تحذير: فشل في {desc}")
                time.sleep(5)  # انتظار أطول للعمليات الطويلة
    
    def build_and_deploy_dashboard(self):
        """بناء ونشر الداشبورد على Cloud Run"""
        print("\n🎛️ بناء ونشر الداشبورد...")
        
        # التحقق من وجود مجلد professional_dashboard
        if not os.path.exists("professional_dashboard"):
            print("❌ مجلد professional_dashboard غير موجود")
            return False
        
        # الانتقال إلى مجلد الداشبورد
        os.chdir("professional_dashboard")
        
        try:
            # إنشاء Dockerfile مبسط
            dockerfile_content = '''FROM node:18-alpine as builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
ENV NODE_ENV=production
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/build /usr/share/nginx/html
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
'''
            
            with open('Dockerfile', 'w') as f:
                f.write(dockerfile_content)
            
            commands = [
                (f"gcloud builds submit --tag gcr.io/{self.project_id}/anubis-dashboard .", "بناء صورة Docker"),
                (f"gcloud run deploy anubis-dashboard --image gcr.io/{self.project_id}/anubis-dashboard --platform managed --region {self.region} --allow-unauthenticated --port 80", "نشر على Cloud Run"),
            ]
            
            for command, desc in commands:
                if not self.run_command(command, desc):
                    print(f"⚠️ فشل في {desc}")
            
            # تنظيف الملف المؤقت
            if os.path.exists('Dockerfile'):
                os.remove('Dockerfile')
                
        finally:
            # العودة إلى المجلد الرئيسي
            os.chdir("..")
    
    def create_vm_instance(self):
        """إنشاء VM لـ n8n و Ollama"""
        print("\n🖥️ إنشاء Compute Engine VM...")
        
        # التحقق من وجود VM مسبقاً
        check_cmd = f"gcloud compute instances describe {self.vm_name} --zone={self.zone} --format='value(name)'"
        result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ VM {self.vm_name} موجود مسبقاً")
            return True
        
        startup_script = '''#!/bin/bash
# تحديث النظام
sudo apt-get update && sudo apt-get install -y docker.io curl

# تشغيل Docker
sudo systemctl start docker
sudo systemctl enable docker

# تشغيل n8n
sudo docker run -d --name n8n --restart unless-stopped \\
    -p 5678:5678 \\
    -e N8N_BASIC_AUTH_ACTIVE=true \\
    -e N8N_BASIC_AUTH_USER=admin \\
    -e N8N_BASIC_AUTH_PASSWORD=anubis123 \\
    -v n8n_data:/home/<USER>/.n8n \\
    n8nio/n8n

# تثبيت Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# تشغيل Ollama
sudo systemctl start ollama
sudo systemctl enable ollama

echo "VM setup completed" > /tmp/setup_complete.log
'''
        
        # حفظ startup script في ملف مؤقت
        with open('startup-script.sh', 'w') as f:
            f.write(startup_script)
        
        command = f"""gcloud compute instances create {self.vm_name} \\
            --zone={self.zone} \\
            --machine-type=e2-standard-2 \\
            --metadata-from-file startup-script=startup-script.sh \\
            --boot-disk-size=30GB \\
            --boot-disk-type=pd-standard \\
            --image-family=ubuntu-2004-lts \\
            --image-project=ubuntu-os-cloud \\
            --tags=http-server,https-server"""
        
        success = self.run_command(command, "إنشاء VM instance")
        
        # حذف الملف المؤقت
        if os.path.exists('startup-script.sh'):
            os.remove('startup-script.sh')
        
        return success
    
    def get_deployment_info(self):
        """الحصول على معلومات النشر"""
        print("\n📋 معلومات النشر:")
        print("="*50)
        
        # رابط الداشبورد
        result = subprocess.run(
            f"gcloud run services describe anubis-dashboard --region={self.region} --format='value(status.url)'",
            shell=True, capture_output=True, text=True
        )
        if result.returncode == 0 and result.stdout.strip():
            print(f"🔗 رابط الداشبورد: {result.stdout.strip()}")
        
        # IP الـ VM
        result = subprocess.run(
            f"gcloud compute instances describe {self.vm_name} --zone={self.zone} --format='value(networkInterfaces[0].accessConfigs[0].natIP)'",
            shell=True, capture_output=True, text=True
        )
        if result.returncode == 0 and result.stdout.strip():
            vm_ip = result.stdout.strip()
            print(f"🖥️ IP الـ VM: {vm_ip}")
            print(f"🔄 رابط n8n: http://{vm_ip}:5678")
        
        # معلومات قاعدة البيانات
        result = subprocess.run(
            f"gcloud sql instances describe {self.mysql_instance} --format='value(ipAddresses[0].ipAddress)'",
            shell=True, capture_output=True, text=True
        )
        if result.returncode == 0 and result.stdout.strip():
            print(f"🗄️ IP قاعدة البيانات: {result.stdout.strip()}")
    
    def deploy(self):
        """تشغيل النشر الكامل"""
        try:
            self.print_banner()
            
            print("🚀 بدء نشر مشروع أنوبيس على Google Cloud Platform...")
            print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # خطوات النشر
            if not self.setup_gcp_project():
                print("❌ فشل في إعداد المشروع")
                return
            
            self.create_cloud_storage()
            self.create_cloud_sql()
            self.create_vm_instance()
            self.build_and_deploy_dashboard()
            
            print("\n🎉 تم إكمال النشر بنجاح!")
            self.get_deployment_info()
            
            print("\n💡 الخطوات التالية:")
            print("1. انتظر 5-10 دقائق حتى تكتمل جميع الخدمات")
            print("2. تحقق من الداشبورد على Cloud Run")
            print("3. تحقق من n8n على VM")
            print("4. اختبر الاتصال بقاعدة البيانات")
            
        except KeyboardInterrupt:
            print("\n⏹️ تم إيقاف النشر بواسطة المستخدم")
        except Exception as e:
            print(f"\n💥 خطأ في النشر: {e}")

def main():
    """الدالة الرئيسية"""
    deployer = AnubisGCPDeployer()
    deployer.deploy()

if __name__ == "__main__":
    main()
