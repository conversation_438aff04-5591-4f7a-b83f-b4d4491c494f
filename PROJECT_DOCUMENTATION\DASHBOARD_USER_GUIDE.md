# 🎛️ دليل استخدام داشبورد إدارة Anubis Cloud

## 📋 نظرة عامة

داشبورد إدارة Anubis Cloud هو واجهة ويب شاملة لإدارة ومراقبة جميع خدمات Universal AI Assistants على Google Cloud Platform.

## 🚀 التشغيل السريع

### 1. **تثبيت المتطلبات**
```bash
# تثبيت المكتبات المطلوبة
pip install -r dashboard_requirements.txt

# أو استخدام المشغل التلقائي
python START_DASHBOARD.py
```

### 2. **تشغيل الداشبورد**
```bash
# الطريقة الأولى: المشغل التلقائي (موصى به)
python START_DASHBOARD.py

# الطريقة الثانية: تشغيل مباشر
streamlit run ANUBIS_CLOUD_DASHBOARD.py --server.port 8501
```

### 3. **الوصول للداشبورد**
- **الرابط المحلي**: http://localhost:8501
- **الرابط الخارجي**: http://your-ip:8501

## 🎯 الميزات الرئيسية

### 🏠 **تبويب النظرة العامة**
- **مقاييس سريعة**: عدد VMs، خدمات Cloud Run، قواعد البيانات
- **خريطة الخدمات**: عرض بصري لهيكل النظام
- **الأنشطة الأخيرة**: سجل العمليات الحديثة
- **حالة النظام**: مؤشرات الصحة العامة

### 🚀 **تبويب النشر**
- **خيارات النشر**: النهج المختلط، Ollama كامل، Cloud فقط
- **تكوين النشر**: نوع VM، حجم القرص، حدود الذاكرة
- **معاينة التكوين**: عرض الإعدادات قبل النشر
- **سجل النشر**: تتبع عمليات النشر

### 📊 **تبويب المراقبة**
- **الأداء في الوقت الفعلي**: رسوم بيانية لـ CPU والذاكرة
- **حالة الخدمات**: مراقبة تفصيلية لكل خدمة
- **التنبيهات**: إشعارات المشاكل والأخطاء
- **السجلات**: عرض سجلات النظام

### 🔧 **تبويب الإدارة**
- **إدارة VMs**: تشغيل، إيقاف، إعادة تشغيل
- **إدارة Cloud Run**: إعادة نشر، عرض السجلات
- **إدارة قواعد البيانات**: نسخ احتياطي، إعادة تشغيل
- **إدارة التخزين**: مراقبة المساحة والاستخدام

### 💰 **تبويب التكلفة**
- **ملخص التكلفة**: توزيع التكلفة حسب الخدمة
- **توقعات التكلفة**: رسوم بيانية للتوقعات
- **توصيات التوفير**: نصائح لتقليل التكلفة
- **تحليل الاستخدام**: تحليل كفاءة الموارد

## 🎛️ الشريط الجانبي

### ⚡ **أزرار التحكم السريع**
- **🚀 نشر**: بدء عملية النشر السريع
- **⏹️ إيقاف**: إيقاف جميع الخدمات
- **🔄 إعادة تشغيل**: إعادة تشغيل النظام بالكامل
- **📊 تحديث البيانات**: تحديث جميع المعلومات

### 🔍 **حالة الخدمات**
- **🟢 نشط**: الخدمة تعمل بشكل طبيعي
- **🟡 تحذير**: الخدمة تعمل مع مشاكل بسيطة
- **🔴 خطأ**: الخدمة متوقفة أو بها مشاكل

## 🛠️ العمليات المتاحة

### 🖥️ **إدارة Virtual Machines**
```python
# العمليات المتاحة:
- تشغيل VM
- إيقاف VM  
- إعادة تشغيل VM
- عرض معلومات VM
- مراقبة الأداء
```

### 🚀 **إدارة Cloud Run Services**
```python
# العمليات المتاحة:
- إعادة نشر الخدمة
- عرض السجلات
- تحديث التكوين
- مراقبة الاستخدام
```

### 🗄️ **إدارة قواعد البيانات**
```python
# العمليات المتاحة:
- إنشاء نسخة احتياطية
- إعادة تشغيل قاعدة البيانات
- مراقبة الأداء
- إدارة المستخدمين
```

## 📊 المراقبة والتنبيهات

### 🔍 **مؤشرات الأداء**
- **CPU Usage**: استخدام المعالج (%)
- **Memory Usage**: استخدام الذاكرة (%)
- **Disk Usage**: استخدام القرص (%)
- **Network I/O**: حركة الشبكة
- **Response Time**: زمن الاستجابة

### 🚨 **أنواع التنبيهات**
- **🔴 حرجة**: مشاكل تتطلب تدخل فوري
- **🟡 تحذير**: مشاكل تحتاج متابعة
- **🔵 معلومات**: إشعارات عامة

## 💰 إدارة التكلفة

### 📊 **مراقبة التكلفة**
- **التكلفة الحالية**: المبلغ المستهلك حتى الآن
- **التكلفة المتوقعة**: توقعات نهاية الشهر
- **مقارنة الشهور**: مقارنة مع الأشهر السابقة

### 💡 **توصيات التوفير**
- **Preemptible VMs**: توفير 60-80% من تكلفة VMs
- **تحسين الحجم**: استخدام أحجام مناسبة للحمولة
- **جدولة التشغيل**: إيقاف الخدمات غير المستخدمة
- **تحسين التخزين**: استخدام فئات تخزين مناسبة

## 🔧 استكشاف الأخطاء

### ❌ **مشاكل شائعة وحلولها**

#### 1. **الداشبورد لا يعمل**
```bash
# التحقق من المتطلبات
pip install -r dashboard_requirements.txt

# إعادة تشغيل
python START_DASHBOARD.py
```

#### 2. **مشكلة في المصادقة**
```bash
# تسجيل الدخول مرة أخرى
gcloud auth login

# تعيين المشروع
gcloud config set project anubis-467210
```

#### 3. **البيانات لا تظهر**
```bash
# التحقق من الاتصال
gcloud compute instances list

# تحديث البيانات
اضغط زر "📊 تحديث البيانات"
```

#### 4. **بطء في التحميل**
```bash
# تحسين الأداء
- تقليل فترة التحديث
- إغلاق التبويبات غير المستخدمة
- التحقق من سرعة الإنترنت
```

## 🔐 الأمان

### 🛡️ **إعدادات الأمان**
- **المصادقة**: مطلوبة للوصول لـ Google Cloud
- **الصلاحيات**: محدودة حسب دور المستخدم
- **التشفير**: جميع البيانات مشفرة
- **السجلات**: تسجيل جميع العمليات

### 🔑 **إدارة الصلاحيات**
```yaml
المطلوب للداشبورد:
  - Compute Engine Admin
  - Cloud Run Admin  
  - Cloud SQL Admin
  - Storage Admin
  - Monitoring Viewer
```

## 📱 الاستخدام على الهاتف

الداشبورد متجاوب ويعمل على:
- **📱 الهواتف الذكية**: iOS, Android
- **💻 الأجهزة اللوحية**: iPad, Android Tablets
- **🖥️ أجهزة الكمبيوتر**: Windows, Mac, Linux

## 🆘 الدعم والمساعدة

### 📞 **طرق الحصول على المساعدة**
1. **📖 الوثائق**: راجع هذا الدليل
2. **🔍 البحث**: استخدم محرك البحث في الداشبورد
3. **📧 الدعم**: تواصل مع فريق الدعم
4. **💬 المجتمع**: انضم لمجتمع المطورين

### 🔗 **روابط مفيدة**
- [Google Cloud Console](https://console.cloud.google.com)
- [Streamlit Documentation](https://docs.streamlit.io)
- [Google Cloud SDK](https://cloud.google.com/sdk/docs)

---

## 🎉 الخلاصة

داشبورد إدارة Anubis Cloud يوفر:
- **🎛️ تحكم كامل** في جميع خدمات Google Cloud
- **📊 مراقبة شاملة** للأداء والتكلفة
- **⚡ عمليات سريعة** للنشر والإدارة
- **🔐 أمان متقدم** لحماية البيانات
- **📱 واجهة متجاوبة** تعمل على جميع الأجهزة

**🚀 ابدأ الآن**: `python START_DASHBOARD.py`
