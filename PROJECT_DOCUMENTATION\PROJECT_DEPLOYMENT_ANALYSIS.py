#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 نظام فحص وتحليل نشر مشروع Universal AI Assistants
=======================================================

هذا النظام يقوم بفحص شامل للمشروع وتحليل جاهزيته للنشر
مع تقديم تقارير مفصلة وتوصيات للتحسين.

المطور: نظام أنوبيس للذكاء الاصطناعي
التاريخ: 2025-01-29
الإصدار: 1.0.0
"""

import os
import json
import subprocess
import sys
from datetime import datetime
from pathlib import Path
import shutil
import hashlib
from typing import Dict, List, Any, Tuple
import logging

class ProjectDeploymentAnalyzer:
    """محلل نشر المشروع الشامل"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.report_dir = self.project_root / "deployment_analysis_reports"
        self.report_dir.mkdir(exist_ok=True)
        
        # إعداد نظام السجلات
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.report_dir / f"analysis_{self.timestamp}.log"),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # معايير التحليل
        self.analysis_criteria = {
            "structure": {"weight": 20, "score": 0},
            "documentation": {"weight": 15, "score": 0},
            "dependencies": {"weight": 15, "score": 0},
            "security": {"weight": 20, "score": 0},
            "docker": {"weight": 10, "score": 0},
            "testing": {"weight": 10, "score": 0},
            "deployment": {"weight": 10, "score": 0}
        }
        
        self.results = {
            "timestamp": self.timestamp,
            "project_path": str(self.project_root),
            "analysis_results": {},
            "recommendations": [],
            "deployment_readiness": 0,
            "critical_issues": [],
            "warnings": [],
            "success_indicators": []
        }

    def analyze_project_structure(self) -> Dict[str, Any]:
        """تحليل هيكل المشروع"""
        self.logger.info("🏗️ بدء تحليل هيكل المشروع...")
        
        structure_analysis = {
            "main_components": {},
            "file_count": 0,
            "directory_count": 0,
            "size_mb": 0,
            "organization_score": 0
        }
        
        # فحص المكونات الرئيسية
        main_components = [
            "ANUBIS_SYSTEM",
            "HORUS_AI_TEAM", 
            "ANUBIS_HORUS_MCP",
            "PROJECT_DOCUMENTATION",
            "SHARED_REQUIREMENTS"
        ]
        
        for component in main_components:
            component_path = self.project_root / component
            if component_path.exists():
                structure_analysis["main_components"][component] = {
                    "exists": True,
                    "size_mb": self._get_directory_size(component_path),
                    "file_count": self._count_files(component_path),
                    "subdirs": len([d for d in component_path.iterdir() if d.is_dir()])
                }
            else:
                structure_analysis["main_components"][component] = {"exists": False}
        
        # إحصائيات عامة
        structure_analysis["file_count"] = self._count_files(self.project_root)
        structure_analysis["directory_count"] = self._count_directories(self.project_root)
        structure_analysis["size_mb"] = self._get_directory_size(self.project_root)
        
        # تقييم التنظيم
        organization_score = 0
        if len(structure_analysis["main_components"]) >= 3:
            organization_score += 30
        if structure_analysis["file_count"] > 100:
            organization_score += 20
        if any(comp["exists"] for comp in structure_analysis["main_components"].values()):
            organization_score += 50
            
        structure_analysis["organization_score"] = min(organization_score, 100)
        self.analysis_criteria["structure"]["score"] = structure_analysis["organization_score"]
        
        return structure_analysis

    def analyze_documentation(self) -> Dict[str, Any]:
        """تحليل التوثيق"""
        self.logger.info("📚 بدء تحليل التوثيق...")
        
        doc_analysis = {
            "readme_files": [],
            "documentation_coverage": 0,
            "language_support": {"arabic": False, "english": False},
            "completeness_score": 0
        }
        
        # البحث عن ملفات README
        readme_patterns = ["README.md", "readme.md", "README.txt"]
        for pattern in readme_patterns:
            readme_files = list(self.project_root.rglob(pattern))
            doc_analysis["readme_files"].extend([str(f.relative_to(self.project_root)) for f in readme_files])
        
        # فحص التوثيق في المجلدات الرئيسية
        doc_dirs = ["docs", "documentation", "PROJECT_DOCUMENTATION"]
        doc_count = 0
        for doc_dir in doc_dirs:
            doc_path = self.project_root / doc_dir
            if doc_path.exists():
                doc_count += self._count_files(doc_path, extensions=[".md", ".txt", ".rst"])
        
        doc_analysis["documentation_coverage"] = min((doc_count / 10) * 100, 100)
        
        # فحص دعم اللغات
        for readme_file in doc_analysis["readme_files"][:3]:  # فحص أول 3 ملفات
            try:
                with open(self.project_root / readme_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if any(char in content for char in "العربية"):
                        doc_analysis["language_support"]["arabic"] = True
                    if any(word in content.lower() for word in ["english", "documentation", "installation"]):
                        doc_analysis["language_support"]["english"] = True
            except:
                pass
        
        # حساب نقاط الاكتمال
        completeness_score = 0
        if len(doc_analysis["readme_files"]) >= 3:
            completeness_score += 40
        if doc_analysis["documentation_coverage"] > 50:
            completeness_score += 30
        if doc_analysis["language_support"]["arabic"] and doc_analysis["language_support"]["english"]:
            completeness_score += 30
            
        doc_analysis["completeness_score"] = completeness_score
        self.analysis_criteria["documentation"]["score"] = completeness_score
        
        return doc_analysis

    def analyze_dependencies(self) -> Dict[str, Any]:
        """تحليل التبعيات"""
        self.logger.info("📦 بدء تحليل التبعيات...")
        
        deps_analysis = {
            "requirements_files": [],
            "package_json_files": [],
            "total_python_deps": 0,
            "total_node_deps": 0,
            "dependency_health": 0
        }
        
        # البحث عن ملفات المتطلبات
        req_files = list(self.project_root.rglob("requirements*.txt"))
        deps_analysis["requirements_files"] = [str(f.relative_to(self.project_root)) for f in req_files]
        
        # البحث عن ملفات package.json
        package_files = list(self.project_root.rglob("package.json"))
        deps_analysis["package_json_files"] = [str(f.relative_to(self.project_root)) for f in package_files]
        
        # عد التبعيات Python
        for req_file in req_files:
            try:
                with open(req_file, 'r') as f:
                    lines = [line.strip() for line in f.readlines() if line.strip() and not line.startswith('#')]
                    deps_analysis["total_python_deps"] += len(lines)
            except:
                pass
        
        # عد تبعيات Node.js
        for package_file in package_files:
            try:
                with open(package_file, 'r') as f:
                    package_data = json.load(f)
                    deps = package_data.get("dependencies", {})
                    dev_deps = package_data.get("devDependencies", {})
                    deps_analysis["total_node_deps"] += len(deps) + len(dev_deps)
            except:
                pass
        
        # تقييم صحة التبعيات
        health_score = 0
        if len(deps_analysis["requirements_files"]) > 0:
            health_score += 40
        if len(deps_analysis["package_json_files"]) > 0:
            health_score += 30
        if deps_analysis["total_python_deps"] > 10:
            health_score += 30
            
        deps_analysis["dependency_health"] = health_score
        self.analysis_criteria["dependencies"]["score"] = health_score
        
        return deps_analysis

    def analyze_security(self) -> Dict[str, Any]:
        """تحليل الأمان"""
        self.logger.info("🔒 بدء تحليل الأمان...")
        
        security_analysis = {
            "sensitive_files": [],
            "api_keys_found": 0,
            "security_score": 0,
            "gitignore_exists": False,
            "env_files": []
        }
        
        # فحص الملفات الحساسة
        sensitive_patterns = ["*.key", "*.pem", "*.p12", "*.pfx", "id_rsa*"]
        for pattern in sensitive_patterns:
            sensitive_files = list(self.project_root.rglob(pattern))
            security_analysis["sensitive_files"].extend([str(f.relative_to(self.project_root)) for f in sensitive_files])
        
        # البحث عن مفاتيح API
        api_key_patterns = ["api_key", "secret_key", "access_token", "private_key"]
        for root, dirs, files in os.walk(self.project_root):
            for file in files:
                if file.endswith(('.py', '.js', '.json', '.env')):
                    try:
                        with open(os.path.join(root, file), 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read().lower()
                            for pattern in api_key_patterns:
                                security_analysis["api_keys_found"] += content.count(pattern)
                    except:
                        pass
        
        # فحص .gitignore
        gitignore_path = self.project_root / ".gitignore"
        security_analysis["gitignore_exists"] = gitignore_path.exists()
        
        # البحث عن ملفات .env
        env_files = list(self.project_root.rglob(".env*"))
        security_analysis["env_files"] = [str(f.relative_to(self.project_root)) for f in env_files]
        
        # حساب نقاط الأمان
        security_score = 100  # نبدأ بـ 100 ونخصم
        if len(security_analysis["sensitive_files"]) > 0:
            security_score -= 30
        if security_analysis["api_keys_found"] > 10:
            security_score -= 20
        if not security_analysis["gitignore_exists"]:
            security_score -= 20
        if len(security_analysis["env_files"]) > 3:
            security_score -= 10
            
        security_analysis["security_score"] = max(security_score, 0)
        self.analysis_criteria["security"]["score"] = security_analysis["security_score"]
        
        return security_analysis

    def analyze_docker_setup(self) -> Dict[str, Any]:
        """تحليل إعداد Docker"""
        self.logger.info("🐳 بدء تحليل إعداد Docker...")
        
        docker_analysis = {
            "dockerfiles": [],
            "compose_files": [],
            "docker_score": 0,
            "containerization_ready": False
        }
        
        # البحث عن Dockerfiles
        dockerfiles = list(self.project_root.rglob("Dockerfile*"))
        docker_analysis["dockerfiles"] = [str(f.relative_to(self.project_root)) for f in dockerfiles]
        
        # البحث عن ملفات docker-compose
        compose_patterns = ["docker-compose*.yml", "docker-compose*.yaml"]
        for pattern in compose_patterns:
            compose_files = list(self.project_root.rglob(pattern))
            docker_analysis["compose_files"].extend([str(f.relative_to(self.project_root)) for f in compose_files])
        
        # تقييم جاهزية Docker
        docker_score = 0
        if len(docker_analysis["dockerfiles"]) > 0:
            docker_score += 50
        if len(docker_analysis["compose_files"]) > 0:
            docker_score += 50
            
        docker_analysis["docker_score"] = docker_score
        docker_analysis["containerization_ready"] = docker_score >= 50
        
        self.analysis_criteria["docker"]["score"] = docker_score
        
        return docker_analysis

    def _get_directory_size(self, path: Path) -> float:
        """حساب حجم المجلد بالميجابايت"""
        total_size = 0
        try:
            for dirpath, dirnames, filenames in os.walk(path):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    try:
                        total_size += os.path.getsize(filepath)
                    except:
                        pass
        except:
            pass
        return round(total_size / (1024 * 1024), 2)

    def _count_files(self, path: Path, extensions: List[str] = None) -> int:
        """عد الملفات في المجلد"""
        count = 0
        try:
            for root, dirs, files in os.walk(path):
                for file in files:
                    if extensions:
                        if any(file.endswith(ext) for ext in extensions):
                            count += 1
                    else:
                        count += 1
        except:
            pass
        return count

    def _count_directories(self, path: Path) -> int:
        """عد المجلدات"""
        count = 0
        try:
            for root, dirs, files in os.walk(path):
                count += len(dirs)
        except:
            pass
        return count

    def generate_recommendations(self) -> List[str]:
        """توليد التوصيات"""
        recommendations = []
        
        # توصيات الهيكل
        if self.analysis_criteria["structure"]["score"] < 70:
            recommendations.append("🏗️ تحسين تنظيم هيكل المشروع وإعادة ترتيب الملفات")
        
        # توصيات التوثيق
        if self.analysis_criteria["documentation"]["score"] < 70:
            recommendations.append("📚 تحسين التوثيق وإضافة ملفات README شاملة")
        
        # توصيات الأمان
        if self.analysis_criteria["security"]["score"] < 80:
            recommendations.append("🔒 تحسين الأمان وإزالة المفاتيح الحساسة من الكود")
        
        # توصيات Docker
        if self.analysis_criteria["docker"]["score"] < 50:
            recommendations.append("🐳 إضافة ملفات Docker للنشر المحسن")
        
        return recommendations

    def calculate_deployment_readiness(self) -> int:
        """حساب جاهزية النشر"""
        total_score = 0
        total_weight = 0
        
        for criterion, data in self.analysis_criteria.items():
            total_score += data["score"] * data["weight"]
            total_weight += data["weight"]
        
        return round(total_score / total_weight) if total_weight > 0 else 0

    def run_comprehensive_analysis(self) -> Dict[str, Any]:
        """تشغيل التحليل الشامل"""
        self.logger.info("🚀 بدء التحليل الشامل للمشروع...")
        
        # تشغيل جميع التحليلات
        self.results["analysis_results"]["structure"] = self.analyze_project_structure()
        self.results["analysis_results"]["documentation"] = self.analyze_documentation()
        self.results["analysis_results"]["dependencies"] = self.analyze_dependencies()
        self.results["analysis_results"]["security"] = self.analyze_security()
        self.results["analysis_results"]["docker"] = self.analyze_docker_setup()
        
        # حساب النتائج النهائية
        self.results["deployment_readiness"] = self.calculate_deployment_readiness()
        self.results["recommendations"] = self.generate_recommendations()
        
        # تحديد المشاكل الحرجة والتحذيرات
        if self.results["deployment_readiness"] < 50:
            self.results["critical_issues"].append("جاهزية النشر منخفضة جداً")
        
        if self.analysis_criteria["security"]["score"] < 60:
            self.results["critical_issues"].append("مشاكل أمان حرجة تحتاج حل فوري")
        
        # مؤشرات النجاح
        if self.results["deployment_readiness"] >= 80:
            self.results["success_indicators"].append("المشروع جاهز للنشر")
        
        if self.analysis_criteria["structure"]["score"] >= 80:
            self.results["success_indicators"].append("هيكل المشروع منظم ومثالي")
        
        return self.results

    def save_report(self) -> str:
        """حفظ التقرير"""
        report_file = self.report_dir / f"deployment_analysis_{self.timestamp}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"📄 تم حفظ التقرير في: {report_file}")
        return str(report_file)

def main():
    """الدالة الرئيسية"""
    print("🏺 نظام فحص وتحليل نشر مشروع Universal AI Assistants")
    print("=" * 60)
    
    analyzer = ProjectDeploymentAnalyzer()
    results = analyzer.run_comprehensive_analysis()
    report_file = analyzer.save_report()
    
    # عرض النتائج
    print(f"\n📊 نتائج التحليل:")
    print(f"🎯 جاهزية النشر: {results['deployment_readiness']}%")
    print(f"📁 عدد الملفات: {results['analysis_results']['structure']['file_count']}")
    print(f"📂 عدد المجلدات: {results['analysis_results']['structure']['directory_count']}")
    print(f"💾 حجم المشروع: {results['analysis_results']['structure']['size_mb']} MB")
    
    if results['critical_issues']:
        print(f"\n🚨 مشاكل حرجة:")
        for issue in results['critical_issues']:
            print(f"   ❌ {issue}")
    
    if results['recommendations']:
        print(f"\n💡 التوصيات:")
        for rec in results['recommendations']:
            print(f"   📋 {rec}")
    
    if results['success_indicators']:
        print(f"\n✅ مؤشرات النجاح:")
        for indicator in results['success_indicators']:
            print(f"   🎉 {indicator}")
    
    print(f"\n📄 التقرير المفصل محفوظ في: {report_file}")

if __name__ == "__main__":
    main()
