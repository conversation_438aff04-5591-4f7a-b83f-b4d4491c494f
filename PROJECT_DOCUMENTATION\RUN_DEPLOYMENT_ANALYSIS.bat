@echo off
chcp 65001 >nul
echo.
echo 🏺 نظام فحص وتحليل نشر مشروع Universal AI Assistants
echo ============================================================
echo.

:menu
echo 📋 اختر نوع التحليل المطلوب:
echo.
echo 1. 🔍 فحص متطلبات النظام
echo 2. 🏥 تحليل صحة المشروع  
echo 3. 🐳 فحص حالة Docker
echo 4. 📦 تحليل التبعيات
echo 5. 🔒 فحص الأمان
echo 6. 📋 قائمة فحص النشر
echo 7. 🚀 التحليل الشامل (محلل النشر الرئيسي)
echo 8. 🎯 التحليل الكامل (جميع الأوامر)
echo 9. 📖 عرض الدليل
echo 0. 🚪 خروج
echo.

set /p choice="اختر رقم (0-9): "

if "%choice%"=="1" goto requirements
if "%choice%"=="2" goto health
if "%choice%"=="3" goto docker
if "%choice%"=="4" goto dependencies
if "%choice%"=="5" goto security
if "%choice%"=="6" goto checklist
if "%choice%"=="7" goto main_analyzer
if "%choice%"=="8" goto full_analysis
if "%choice%"=="9" goto guide
if "%choice%"=="0" goto exit

echo ❌ اختيار غير صحيح، حاول مرة أخرى
pause
goto menu

:requirements
echo.
echo 🔍 تشغيل فحص متطلبات النظام...
echo =====================================
python DEPLOYMENT_COMMANDS.py --command requirements
echo.
pause
goto menu

:health
echo.
echo 🏥 تشغيل تحليل صحة المشروع...
echo ===============================
python DEPLOYMENT_COMMANDS.py --command health
echo.
pause
goto menu

:docker
echo.
echo 🐳 تشغيل فحص حالة Docker...
echo =============================
python DEPLOYMENT_COMMANDS.py --command docker
echo.
pause
goto menu

:dependencies
echo.
echo 📦 تشغيل تحليل التبعيات...
echo ===========================
python DEPLOYMENT_COMMANDS.py --command dependencies
echo.
pause
goto menu

:security
echo.
echo 🔒 تشغيل فحص الأمان...
echo ======================
python DEPLOYMENT_COMMANDS.py --command security
echo.
pause
goto menu

:checklist
echo.
echo 📋 توليد قائمة فحص النشر...
echo ============================
python DEPLOYMENT_COMMANDS.py --command checklist
echo.
pause
goto menu

:main_analyzer
echo.
echo 🚀 تشغيل المحلل الرئيسي للنشر...
echo =================================
python PROJECT_DEPLOYMENT_ANALYSIS.py
echo.
pause
goto menu

:full_analysis
echo.
echo 🎯 تشغيل التحليل الكامل...
echo ===========================
python DEPLOYMENT_COMMANDS.py --command full
echo.
pause
goto menu

:guide
echo.
echo 📖 عرض الدليل...
echo =================
if exist "DEPLOYMENT_ANALYSIS_GUIDE.md" (
    type "DEPLOYMENT_ANALYSIS_GUIDE.md"
) else (
    echo ❌ ملف الدليل غير موجود
)
echo.
pause
goto menu

:exit
echo.
echo 👋 شكراً لاستخدام نظام فحص وتحليل النشر!
echo.
pause
exit
