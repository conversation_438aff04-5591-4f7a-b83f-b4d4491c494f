# 🏺 نظام فحص وتحليل نشر مشروع Universal AI Assistants
# PowerShell Script for Deployment Analysis

# تعيين ترميز UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

function Show-Menu {
    Clear-Host
    Write-Host ""
    Write-Host "🏺 نظام فحص وتحليل نشر مشروع Universal AI Assistants" -ForegroundColor Cyan
    Write-Host "============================================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "📋 اختر نوع التحليل المطلوب:" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "1. 🔍 فحص متطلبات النظام" -ForegroundColor White
    Write-Host "2. 🏥 تحليل صحة المشروع" -ForegroundColor White  
    Write-Host "3. 🐳 فحص حالة Docker" -ForegroundColor White
    Write-Host "4. 📦 تحليل التبعيات" -ForegroundColor White
    Write-Host "5. 🔒 فحص الأمان" -ForegroundColor White
    Write-Host "6. 📋 قائمة فحص النشر" -ForegroundColor White
    Write-Host "7. 🚀 التحليل الشامل (محلل النشر الرئيسي)" -ForegroundColor Green
    Write-Host "8. 🎯 التحليل الكامل (جميع الأوامر)" -ForegroundColor Green
    Write-Host "9. 📖 عرض الدليل" -ForegroundColor White
    Write-Host "0. 🚪 خروج" -ForegroundColor Red
    Write-Host ""
}

function Run-Requirements {
    Write-Host ""
    Write-Host "🔍 تشغيل فحص متطلبات النظام..." -ForegroundColor Yellow
    Write-Host "=====================================" -ForegroundColor Yellow
    python DEPLOYMENT_COMMANDS.py --command requirements
    Write-Host ""
    Read-Host "اضغط Enter للمتابعة"
}

function Run-Health {
    Write-Host ""
    Write-Host "🏥 تشغيل تحليل صحة المشروع..." -ForegroundColor Yellow
    Write-Host "===============================" -ForegroundColor Yellow
    python DEPLOYMENT_COMMANDS.py --command health
    Write-Host ""
    Read-Host "اضغط Enter للمتابعة"
}

function Run-Docker {
    Write-Host ""
    Write-Host "🐳 تشغيل فحص حالة Docker..." -ForegroundColor Yellow
    Write-Host "=============================" -ForegroundColor Yellow
    python DEPLOYMENT_COMMANDS.py --command docker
    Write-Host ""
    Read-Host "اضغط Enter للمتابعة"
}

function Run-Dependencies {
    Write-Host ""
    Write-Host "📦 تشغيل تحليل التبعيات..." -ForegroundColor Yellow
    Write-Host "===========================" -ForegroundColor Yellow
    python DEPLOYMENT_COMMANDS.py --command dependencies
    Write-Host ""
    Read-Host "اضغط Enter للمتابعة"
}

function Run-Security {
    Write-Host ""
    Write-Host "🔒 تشغيل فحص الأمان..." -ForegroundColor Yellow
    Write-Host "======================" -ForegroundColor Yellow
    python DEPLOYMENT_COMMANDS.py --command security
    Write-Host ""
    Read-Host "اضغط Enter للمتابعة"
}

function Run-Checklist {
    Write-Host ""
    Write-Host "📋 توليد قائمة فحص النشر..." -ForegroundColor Yellow
    Write-Host "============================" -ForegroundColor Yellow
    python DEPLOYMENT_COMMANDS.py --command checklist
    Write-Host ""
    Read-Host "اضغط Enter للمتابعة"
}

function Run-MainAnalyzer {
    Write-Host ""
    Write-Host "🚀 تشغيل المحلل الرئيسي للنشر..." -ForegroundColor Green
    Write-Host "=================================" -ForegroundColor Green
    python PROJECT_DEPLOYMENT_ANALYSIS.py
    Write-Host ""
    Read-Host "اضغط Enter للمتابعة"
}

function Run-FullAnalysis {
    Write-Host ""
    Write-Host "🎯 تشغيل التحليل الكامل..." -ForegroundColor Green
    Write-Host "===========================" -ForegroundColor Green
    python DEPLOYMENT_COMMANDS.py --command full
    Write-Host ""
    Read-Host "اضغط Enter للمتابعة"
}

function Show-Guide {
    Write-Host ""
    Write-Host "📖 عرض الدليل..." -ForegroundColor Yellow
    Write-Host "=================" -ForegroundColor Yellow
    
    if (Test-Path "DEPLOYMENT_ANALYSIS_GUIDE.md") {
        Get-Content "DEPLOYMENT_ANALYSIS_GUIDE.md" | Write-Host
    } else {
        Write-Host "❌ ملف الدليل غير موجود" -ForegroundColor Red
    }
    Write-Host ""
    Read-Host "اضغط Enter للمتابعة"
}

# الحلقة الرئيسية
do {
    Show-Menu
    $choice = Read-Host "اختر رقم (0-9)"
    
    switch ($choice) {
        "1" { Run-Requirements }
        "2" { Run-Health }
        "3" { Run-Docker }
        "4" { Run-Dependencies }
        "5" { Run-Security }
        "6" { Run-Checklist }
        "7" { Run-MainAnalyzer }
        "8" { Run-FullAnalysis }
        "9" { Show-Guide }
        "0" { 
            Write-Host ""
            Write-Host "👋 شكراً لاستخدام نظام فحص وتحليل النشر!" -ForegroundColor Green
            Write-Host ""
            break
        }
        default { 
            Write-Host "❌ اختيار غير صحيح، حاول مرة أخرى" -ForegroundColor Red
            Start-Sleep -Seconds 2
        }
    }
} while ($true)
