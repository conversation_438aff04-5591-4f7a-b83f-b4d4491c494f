#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🆕 إنشاء مستودع جديد ورفع المشروع
==================================

سكريبت لإنشاء مستودع GitHub جديد نظيف ورفع المشروع عليه
بدون أي تاريخ commits يحتوي على أسرار.

📅 تاريخ الإنشاء: 2025-07-29
🔧 الإصدار: 1.0.0
"""

import os
import shutil
import subprocess
import json
from datetime import datetime
from pathlib import Path

class NewRepoCreator:
    def __init__(self):
        self.project_root = os.getcwd()
        self.new_repo_name = "universal-ai-assistants-clean"
        self.github_username = "amrashour2"
        self.temp_dir = f"temp_clean_repo_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # ملفات ومجلدات للتجاهل
        self.ignore_patterns = {
            '.git', '__pycache__', 'node_modules', '.venv', 'anubis_env',
            'secrets_backup_*', '*.pyc', '*.pyo', '*.pyd', '.DS_Store',
            'Thumbs.db', '*.log', '*.tmp', '*.bak', '*.swp'
        }
        
        # ملفات مهمة للتضمين
        self.important_files = [
            'README.md', 'LICENSE', '.gitignore', '.env.template',
            'requirements.txt', 'package.json', 'docker-compose.yml'
        ]
    
    def print_header(self):
        """طباعة رأس السكريبت"""
        print("=" * 80)
        print("🆕 إنشاء مستودع GitHub جديد ونظيف")
        print("=" * 80)
        print(f"📅 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📁 المشروع الحالي: {self.project_root}")
        print(f"🆕 المستودع الجديد: {self.new_repo_name}")
        print(f"👤 المستخدم: {self.github_username}")
        print("=" * 80)
    
    def create_temp_directory(self):
        """إنشاء مجلد مؤقت للمشروع النظيف"""
        print("\n📁 إنشاء مجلد مؤقت...")
        
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
        
        os.makedirs(self.temp_dir)
        print(f"✅ تم إنشاء: {self.temp_dir}")
    
    def should_ignore(self, path):
        """تحديد ما إذا كان يجب تجاهل مسار معين"""
        path_name = os.path.basename(path)
        
        # تجاهل الأنماط المحددة
        for pattern in self.ignore_patterns:
            if pattern.startswith('*'):
                if path_name.endswith(pattern[1:]):
                    return True
            elif pattern in path_name or path_name.startswith(pattern):
                return True
        
        return False
    
    def copy_clean_files(self):
        """نسخ الملفات النظيفة إلى المجلد المؤقت"""
        print("\n📋 نسخ الملفات النظيفة...")
        
        copied_files = 0
        copied_dirs = 0
        
        for root, dirs, files in os.walk(self.project_root):
            # تجاهل المجلدات غير المرغوبة
            dirs[:] = [d for d in dirs if not self.should_ignore(os.path.join(root, d))]
            
            # تجاهل المجلد المؤقت نفسه
            if self.temp_dir in root:
                continue
            
            # إنشاء هيكل المجلدات
            rel_path = os.path.relpath(root, self.project_root)
            if rel_path != '.':
                dest_dir = os.path.join(self.temp_dir, rel_path)
                if not os.path.exists(dest_dir):
                    os.makedirs(dest_dir)
                    copied_dirs += 1
            
            # نسخ الملفات
            for file in files:
                file_path = os.path.join(root, file)
                
                if self.should_ignore(file_path):
                    continue
                
                rel_file_path = os.path.relpath(file_path, self.project_root)
                dest_file_path = os.path.join(self.temp_dir, rel_file_path)
                
                # إنشاء المجلد إذا لم يكن موجوداً
                dest_dir = os.path.dirname(dest_file_path)
                if not os.path.exists(dest_dir):
                    os.makedirs(dest_dir)
                
                # نسخ الملف
                try:
                    shutil.copy2(file_path, dest_file_path)
                    copied_files += 1
                    
                    if copied_files % 100 == 0:
                        print(f"   📄 تم نسخ {copied_files} ملف...")
                        
                except Exception as e:
                    print(f"   ⚠️ تعذر نسخ {rel_file_path}: {str(e)}")
        
        print(f"✅ تم نسخ {copied_files} ملف و {copied_dirs} مجلد")
        return copied_files, copied_dirs
    
    def create_enhanced_gitignore(self):
        """إنشاء .gitignore محسن"""
        print("\n🛡️ إنشاء .gitignore محسن...")
        
        gitignore_content = """# 🔐 حماية الأسرار والمفاتيح الحساسة
**/*api_key*
**/*secret*
**/*password*
**/*token*
**/test_*_keys.py
**/model_testers/**/*.py
**/*_caller.py
.env
.env.local
.env.production
secrets_backup_*/
api_keys_vault/
**/api_keys_collection.json

# 🔑 مفاتيح API محددة
**/*openrouter*
**/*gemini*
**/*claude*
**/*openai*

# 🗄️ قواعد البيانات الحساسة
**/*.db
**/*.sqlite
**/*.sqlite3
**/database_config.json

# 🐍 Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 🌐 Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# 🐳 Docker
.dockerignore
docker-compose.override.yml

# 🔧 IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# 🖥️ OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 📝 Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 🧪 Testing
.coverage
.pytest_cache/
.tox/
.coverage.*
coverage.xml
*.cover
.hypothesis/

# 🔄 Temporary
*.tmp
*.temp
*.bak
*.backup
temp_*/
"""
        
        gitignore_path = os.path.join(self.temp_dir, '.gitignore')
        with open(gitignore_path, 'w', encoding='utf-8') as f:
            f.write(gitignore_content)
        
        print("✅ تم إنشاء .gitignore محسن")
    
    def create_main_readme(self):
        """إنشاء README رئيسي للمشروع"""
        print("\n📖 إنشاء README رئيسي...")
        
        readme_content = f"""# 🤖 Universal AI Assistants

<div align="center">

![Version](https://img.shields.io/badge/Version-2.0.0-blue?style=for-the-badge)
![Status](https://img.shields.io/badge/Status-Production%20Ready-green?style=for-the-badge)
![Security](https://img.shields.io/badge/Security-Enhanced-brightgreen?style=for-the-badge)
![License](https://img.shields.io/badge/License-MIT-yellow?style=for-the-badge)

**منصة متكاملة للذكاء الاصطناعي التعاوني**

[🚀 البدء السريع](#-البدء-السريع) • [📚 التوثيق](#-التوثيق) • [🤝 المساهمة](#-المساهمة) • [📞 الدعم](#-الدعم)

</div>

---

## 🌟 نظرة عامة

**Universal AI Assistants** هو نظام متكامل وشامل للذكاء الاصطناعي التعاوني يجمع بين أقوى النماذج والتقنيات الحديثة في منصة واحدة متطورة.

### 🎯 المكونات الرئيسية:

#### 🏺 ANUBIS_SYSTEM
- **النظام الأساسي المتقدم** للذكاء الاصطناعي
- دعم Docker والحاويات المتعددة
- تكامل مع قواعد البيانات المختلفة
- واجهات API شاملة ومتطورة

#### 𓅃 HORUS_AI_TEAM  
- **فريق من 8 وكلاء ذكيين** بأسماء مصرية أسطورية
- تعاون ذكي وتوزيع المهام التلقائي
- نظام ذاكرة جماعية للتعلم المستمر
- تخصصات متنوعة (تحليل، تطوير، أمان، إبداع)

#### 🔗 ANUBIS_HORUS_MCP
- **نظام MCP متقدم** (Model Context Protocol)
- تكامل سلس بين النماذج المختلفة
- إدارة آمنة لمفاتيح API
- أدوات تطوير متقدمة

---

## 🚀 البدء السريع

### 📋 المتطلبات
- Python 3.8+
- Node.js 16+
- Docker (اختياري)
- Git

### ⚡ التثبيت السريع

```bash
# 1. استنساخ المشروع
git clone https://github.com/{self.github_username}/{self.new_repo_name}.git
cd {self.new_repo_name}

# 2. إعداد البيئة الافتراضية
python -m venv .venv
source .venv/bin/activate  # Linux/Mac
# أو
.venv\\Scripts\\activate  # Windows

# 3. تثبيت المتطلبات
pip install -r requirements.txt

# 4. إعداد متغيرات البيئة
cp .env.template .env
# قم بتحرير .env وإضافة مفاتيح API الخاصة بك

# 5. التشغيل
python QUICK_START.py
```

### 🐳 التشغيل باستخدام Docker

```bash
# تشغيل النظام الكامل
docker-compose up -d

# الوصول للواجهات
# النظام الأساسي: http://localhost:8000
# فريق حورس: http://localhost:7000
# الواجهة الموحدة: http://localhost:5000
```

---

## 🌟 الميزات الرئيسية

### 🤖 الذكاء الاصطناعي المتقدم
- **8 وكلاء متخصصين** بقدرات فريدة
- **تعاون ذكي** بين النماذج المختلفة
- **تعلم مستمر** من التفاعلات
- **استجابة فورية** أقل من ثانية واحدة

### 🔐 الأمان المتقدم
- **تشفير AES-256** لجميع البيانات الحساسة
- **إدارة آمنة** لمفاتيح API
- **مراقبة مستمرة** للأنشطة المشبوهة
- **نسخ احتياطية** تلقائية ومشفرة

### 🌐 التكامل الشامل
- **دعم متعدد المنصات** (OpenAI, Google, Anthropic, إلخ)
- **واجهات متنوعة** (Web, CLI, API)
- **تكامل Docker** للنشر السهل
- **قواعد بيانات متعددة** (SQLite, MySQL, PostgreSQL)

### 📊 المراقبة والتحليل
- **لوحات تحكم** تفاعلية ومتقدمة
- **تقارير مفصلة** للأداء والاستخدام
- **تحليل ذكي** للبيانات والأنماط
- **تنبيهات فورية** للمشاكل والأخطاء

---

## 📚 التوثيق

### 📖 أدلة المستخدم
- [🚀 دليل البدء السريع](docs/quick-start.md)
- [🔧 دليل التثبيت المفصل](docs/installation.md)
- [🎯 أمثلة الاستخدام](docs/examples.md)
- [❓ الأسئلة الشائعة](docs/faq.md)

### 👨‍💻 أدلة المطورين
- [🏗️ هيكل المشروع](docs/architecture.md)
- [🔌 تطوير الإضافات](docs/plugins.md)
- [🧪 دليل الاختبار](docs/testing.md)
- [📡 مرجع API](docs/api-reference.md)

---

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) قبل البدء.

### 🔄 خطوات المساهمة
1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push للفرع (`git push origin feature/amazing-feature`)
5. فتح Pull Request

---

## 📞 الدعم

### 🆘 الحصول على المساعدة
- 📧 **البريد الإلكتروني**: <EMAIL>
- 💬 **Discord**: [انضم لخادمنا](https://discord.gg/universal-ai)
- 📱 **Telegram**: [@UniversalAISupport](https://t.me/UniversalAISupport)
- 🐛 **تقارير الأخطاء**: [GitHub Issues](https://github.com/{self.github_username}/{self.new_repo_name}/issues)

### 📈 الحالة والإحصائيات
- 🌟 **النجوم**: ![GitHub stars](https://img.shields.io/github/stars/{self.github_username}/{self.new_repo_name})
- 🍴 **Forks**: ![GitHub forks](https://img.shields.io/github/forks/{self.github_username}/{self.new_repo_name})
- 🐛 **المشاكل**: ![GitHub issues](https://img.shields.io/github/issues/{self.github_username}/{self.new_repo_name})
- 📥 **التحميلات**: ![GitHub downloads](https://img.shields.io/github/downloads/{self.github_username}/{self.new_repo_name}/total)

---

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

<div align="center">

**صُنع بـ ❤️ بواسطة فريق Universal AI Assistants**

⭐ **إذا أعجبك المشروع، لا تنس إعطاؤه نجمة!** ⭐

</div>
"""
        
        readme_path = os.path.join(self.temp_dir, 'README.md')
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print("✅ تم إنشاء README رئيسي شامل")
    
    def initialize_git_repo(self):
        """تهيئة مستودع Git جديد"""
        print("\n🔄 تهيئة مستودع Git جديد...")
        
        os.chdir(self.temp_dir)
        
        # تهيئة Git
        subprocess.run(['git', 'init'], check=True)
        subprocess.run(['git', 'config', 'user.name', 'Universal AI Assistants'], check=True)
        subprocess.run(['git', 'config', 'user.email', '<EMAIL>'], check=True)
        
        # إضافة الملفات
        subprocess.run(['git', 'add', '.'], check=True)
        
        # Commit أولي
        commit_message = "🎉 Initial commit: Universal AI Assistants - Clean and Secure"
        subprocess.run(['git', 'commit', '-m', commit_message], check=True)
        
        print("✅ تم تهيئة Git وعمل commit أولي")
    
    def create_github_repo(self):
        """إنشاء مستودع على GitHub"""
        print("\n🌐 إنشاء مستودع على GitHub...")
        
        # إنشاء مستودع باستخدام GitHub CLI إذا كان متوفراً
        try:
            repo_description = "🤖 Universal AI Assistants - Advanced Collaborative AI Platform with 8 Specialized Agents"
            
            subprocess.run([
                'gh', 'repo', 'create', self.new_repo_name,
                '--description', repo_description,
                '--public',
                '--clone=false'
            ], check=True)
            
            print("✅ تم إنشاء المستودع على GitHub")
            return True
            
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("⚠️ GitHub CLI غير متوفر - سيتم إنشاء المستودع يدوياً")
            return False
    
    def push_to_github(self):
        """رفع المشروع على GitHub"""
        print("\n🚀 رفع المشروع على GitHub...")
        
        # إضافة remote origin
        repo_url = f"https://github.com/{self.github_username}/{self.new_repo_name}.git"
        subprocess.run(['git', 'remote', 'add', 'origin', repo_url], check=True)
        
        # رفع المشروع
        subprocess.run(['git', 'branch', '-M', 'main'], check=True)
        subprocess.run(['git', 'push', '-u', 'origin', 'main'], check=True)
        
        print("✅ تم رفع المشروع بنجاح!")
        print(f"🌐 رابط المستودع: {repo_url}")
    
    def cleanup(self):
        """تنظيف الملفات المؤقتة"""
        print("\n🧹 تنظيف الملفات المؤقتة...")
        
        os.chdir(self.project_root)
        
        try:
            shutil.rmtree(self.temp_dir)
            print("✅ تم حذف المجلد المؤقت")
        except Exception as e:
            print(f"⚠️ تعذر حذف المجلد المؤقت: {str(e)}")
    
    def generate_success_report(self):
        """إنشاء تقرير نجاح العملية"""
        print("\n📊 إنشاء تقرير النجاح...")
        
        report = f"""# 🎉 تقرير نجاح إنشاء المستودع الجديد

## 📊 ملخص العملية
- **📅 التاريخ**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- **🆕 المستودع الجديد**: {self.new_repo_name}
- **👤 المستخدم**: {self.github_username}
- **🌐 الرابط**: https://github.com/{self.github_username}/{self.new_repo_name}

## ✅ الإجراءات المكتملة
- [x] إنشاء مجلد مؤقت نظيف
- [x] نسخ الملفات المهمة فقط
- [x] إنشاء .gitignore محسن
- [x] إنشاء README شامل
- [x] تهيئة Git جديد
- [x] إنشاء مستودع GitHub
- [x] رفع المشروع بنجاح

## 🎯 النتيجة
تم بنجاح إنشاء مستودع GitHub جديد ونظيف بدون أي أسرار أو تاريخ commits مشبوه.

المشروع الآن متاح على: https://github.com/{self.github_username}/{self.new_repo_name}
"""
        
        report_path = f"new_repo_success_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"✅ تم حفظ تقرير النجاح في: {report_path}")
    
    def run_complete_process(self):
        """تشغيل العملية الكاملة"""
        try:
            self.print_header()
            
            # 1. إنشاء مجلد مؤقت
            self.create_temp_directory()
            
            # 2. نسخ الملفات النظيفة
            copied_files, copied_dirs = self.copy_clean_files()
            
            # 3. إنشاء .gitignore محسن
            self.create_enhanced_gitignore()
            
            # 4. إنشاء README رئيسي
            self.create_main_readme()
            
            # 5. تهيئة Git
            self.initialize_git_repo()
            
            # 6. إنشاء مستودع GitHub
            github_created = self.create_github_repo()
            
            # 7. رفع المشروع
            self.push_to_github()
            
            # 8. إنشاء تقرير النجاح
            self.generate_success_report()
            
            # 9. تنظيف
            self.cleanup()
            
            # النتائج النهائية
            print("\n" + "=" * 80)
            print("🎉 تم إنشاء المستودع الجديد بنجاح!")
            print("=" * 80)
            print(f"📊 الإحصائيات:")
            print(f"   📁 الملفات المنسوخة: {copied_files}")
            print(f"   📂 المجلدات المنسوخة: {copied_dirs}")
            print(f"   🆕 المستودع: {self.new_repo_name}")
            print(f"   🌐 الرابط: https://github.com/{self.github_username}/{self.new_repo_name}")
            print("\n✅ المشروع الآن متاح على GitHub بدون أي أسرار!")
            print("=" * 80)
            
        except Exception as e:
            print(f"\n❌ خطأ في العملية: {str(e)}")
            print("🔄 يرجى المحاولة مرة أخرى أو التحقق من الإعدادات")
            
            # تنظيف في حالة الخطأ
            try:
                self.cleanup()
            except:
                pass

def main():
    """الدالة الرئيسية"""
    creator = NewRepoCreator()
    creator.run_complete_process()

if __name__ == "__main__":
    main()
