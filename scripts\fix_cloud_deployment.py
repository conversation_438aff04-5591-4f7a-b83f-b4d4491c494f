#!/usr/bin/env python3
"""
🔧 سكريبت إصلاح النشر السحابي
Universal AI Assistants - إصلاح مشاكل الوكلاء والنماذج
"""

import subprocess
import os
import sys
import time
from pathlib import Path
import json

class CloudDeploymentFixer:
    def __init__(self):
        self.bucket = "universal-ai-models-2025-storage"
        self.project_id = "universal-ai-assistants-2025"
        self.service_name = "universal-ai-assistants"
        self.region = "us-central1"
        
    def print_status(self, message, status="INFO"):
        """طباعة حالة مع تنسيق ملون"""
        colors = {
            "INFO": "\033[94m",
            "SUCCESS": "\033[92m", 
            "WARNING": "\033[93m",
            "ERROR": "\033[91m",
            "RESET": "\033[0m"
        }
        print(f"{colors.get(status, '')}{message}{colors['RESET']}")
    
    def run_command(self, command, description=""):
        """تشغيل أمر مع معالجة الأخطاء"""
        self.print_status(f"🔄 {description}", "INFO")
        self.print_status(f"Command: {command}", "INFO")
        
        try:
            result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                self.print_status(f"✅ {description} - نجح", "SUCCESS")
                return True, result.stdout
            else:
                self.print_status(f"❌ {description} - فشل", "ERROR")
                self.print_status(f"Error: {result.stderr}", "ERROR")
                return False, result.stderr
                
        except subprocess.TimeoutExpired:
            self.print_status(f"⏰ {description} - انتهت المهلة الزمنية", "WARNING")
            return False, "Timeout"
        except Exception as e:
            self.print_status(f"💥 {description} - خطأ: {str(e)}", "ERROR")
            return False, str(e)
    
    def check_prerequisites(self):
        """فحص المتطلبات الأساسية"""
        self.print_status("🔍 فحص المتطلبات الأساسية...", "INFO")
        
        # فحص gcloud
        success, _ = self.run_command("gcloud --version", "فحص Google Cloud SDK")
        if not success:
            self.print_status("❌ Google Cloud SDK غير مثبت", "ERROR")
            return False
            
        # فحص المصادقة
        success, _ = self.run_command("gcloud auth list", "فحص المصادقة")
        if not success:
            self.print_status("❌ لم يتم تسجيل الدخول إلى Google Cloud", "ERROR")
            return False
            
        # فحص المشروع
        success, _ = self.run_command(f"gcloud config set project {self.project_id}", "تعيين المشروع")
        if not success:
            self.print_status(f"❌ لا يمكن الوصول للمشروع {self.project_id}", "ERROR")
            return False
            
        self.print_status("✅ جميع المتطلبات متوفرة", "SUCCESS")
        return True
    
    def upload_files_to_storage(self):
        """رفع الملفات إلى Cloud Storage"""
        self.print_status("📤 بدء رفع الملفات إلى Cloud Storage...", "INFO")
        
        # قائمة المجلدات للرفع
        folders_to_upload = [
            "ANUBIS_SYSTEM",
            "HORUS_AI_TEAM", 
            "ANUBIS_HORUS_MCP",
            "PROJECT_DOCUMENTATION",
            "SHARED_REQUIREMENTS"
        ]
        
        uploaded_count = 0
        total_folders = len(folders_to_upload)
        
        for folder in folders_to_upload:
            if Path(folder).exists():
                self.print_status(f"📁 رفع مجلد {folder}...", "INFO")
                
                # رفع المجلد
                command = f"gcloud storage cp -r {folder} gs://{self.bucket}/"
                success, output = self.run_command(command, f"رفع {folder}")
                
                if success:
                    uploaded_count += 1
                    self.print_status(f"✅ تم رفع {folder} بنجاح", "SUCCESS")
                else:
                    self.print_status(f"❌ فشل رفع {folder}", "ERROR")
            else:
                self.print_status(f"⚠️ المجلد {folder} غير موجود", "WARNING")
        
        self.print_status(f"📊 تم رفع {uploaded_count}/{total_folders} مجلدات", "INFO")
        return uploaded_count > 0
    
    def update_environment_variables(self):
        """تحديث متغيرات البيئة في Cloud Run"""
        self.print_status("⚙️ تحديث متغيرات البيئة...", "INFO")
        
        env_vars = {
            "GEMINI_API_KEY": "AIzaSyDJADkYbl6Hmhg76mfpPSl1yTE_jIVvo54",
            "HORUS_TEAM_ENABLED": "true",
            "ANUBIS_STORAGE_BUCKET": self.bucket,
            "GOOGLE_CLOUD_PROJECT": self.project_id,
            "ANUBIS_SYSTEM_PATH": f"gs://{self.bucket}/ANUBIS_SYSTEM",
            "HORUS_TEAM_PATH": f"gs://{self.bucket}/HORUS_AI_TEAM",
            "MCP_SYSTEM_PATH": f"gs://{self.bucket}/ANUBIS_HORUS_MCP"
        }
        
        # تحويل متغيرات البيئة إلى نص
        env_string = ",".join([f"{k}={v}" for k, v in env_vars.items()])
        
        command = f"""gcloud run services update {self.service_name} \
            --region={self.region} \
            --set-env-vars="{env_string}" """
        
        success, output = self.run_command(command, "تحديث متغيرات البيئة")
        return success
    
    def redeploy_service(self):
        """إعادة نشر الخدمة مع التحديثات"""
        self.print_status("🚀 إعادة نشر الخدمة...", "INFO")
        
        command = f"""gcloud run deploy {self.service_name} \
            --source . \
            --region={self.region} \
            --platform=managed \
            --allow-unauthenticated \
            --memory=1Gi \
            --cpu=1 \
            --max-instances=5"""
        
        success, output = self.run_command(command, "إعادة نشر الخدمة")
        return success
    
    def test_deployment(self):
        """اختبار النشر الجديد"""
        self.print_status("🧪 اختبار النشر الجديد...", "INFO")
        
        service_url = f"https://{self.service_name}-554716410816.{self.region}.run.app"
        
        # اختبار الصفحة الرئيسية
        success, _ = self.run_command(f"curl -s -o /dev/null -w '%{{http_code}}' {service_url}", "اختبار الصفحة الرئيسية")
        
        if success:
            # اختبار API الوكلاء
            test_data = '{"message": "مرحبا من حورس", "agent": "horus"}'
            curl_command = f"""curl -X POST {service_url}/api/chat \
                -H "Content-Type: application/json" \
                -d '{test_data}' \
                -w "\\nHTTP Status: %{{http_code}}\\n" """
            
            success, output = self.run_command(curl_command, "اختبار API الوكلاء")
            
            if "200" in output:
                self.print_status("✅ الوكلاء يعملون بنجاح", "SUCCESS")
                return True
            else:
                self.print_status("⚠️ الوكلاء لا يردون بشكل صحيح", "WARNING")
                return False
        
        return False
    
    def verify_storage_upload(self):
        """التحقق من رفع الملفات"""
        self.print_status("🔍 التحقق من رفع الملفات...", "INFO")
        
        command = f"gcloud storage ls -r gs://{self.bucket}/"
        success, output = self.run_command(command, "فحص محتويات التخزين")
        
        if success and output.strip():
            self.print_status("✅ الملفات مرفوعة بنجاح", "SUCCESS")
            self.print_status(f"📁 المحتويات:\n{output[:500]}...", "INFO")
            return True
        else:
            self.print_status("❌ لم يتم رفع الملفات", "ERROR")
            return False
    
    def generate_report(self, results):
        """إنشاء تقرير النتائج"""
        self.print_status("📋 إنشاء تقرير النتائج...", "INFO")
        
        report = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "project_id": self.project_id,
            "service_name": self.service_name,
            "bucket": self.bucket,
            "results": results,
            "service_url": f"https://{self.service_name}-554716410816.{self.region}.run.app"
        }
        
        # حفظ التقرير
        with open("cloud_fix_report.json", "w", encoding="utf-8") as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        self.print_status("📄 تم حفظ التقرير في cloud_fix_report.json", "SUCCESS")
        return report
    
    def run_full_fix(self):
        """تشغيل الإصلاح الكامل"""
        self.print_status("🚀 بدء إصلاح النشر السحابي...", "INFO")
        self.print_status("="*60, "INFO")
        
        results = {}
        
        # 1. فحص المتطلبات
        results["prerequisites"] = self.check_prerequisites()
        if not results["prerequisites"]:
            self.print_status("❌ فشل في فحص المتطلبات", "ERROR")
            return results
        
        # 2. رفع الملفات
        results["file_upload"] = self.upload_files_to_storage()
        
        # 3. التحقق من الرفع
        results["upload_verification"] = self.verify_storage_upload()
        
        # 4. تحديث متغيرات البيئة
        results["env_update"] = self.update_environment_variables()
        
        # 5. إعادة النشر
        results["redeploy"] = self.redeploy_service()
        
        # 6. اختبار النشر
        if results["redeploy"]:
            time.sleep(30)  # انتظار حتى يصبح النشر جاهز
            results["deployment_test"] = self.test_deployment()
        else:
            results["deployment_test"] = False
        
        # 7. إنشاء التقرير
        report = self.generate_report(results)
        
        # 8. عرض النتائج النهائية
        self.print_final_results(results)
        
        return results
    
    def print_final_results(self, results):
        """عرض النتائج النهائية"""
        self.print_status("="*60, "INFO")
        self.print_status("🎯 النتائج النهائية:", "INFO")
        self.print_status("="*60, "INFO")
        
        success_count = sum(1 for v in results.values() if v)
        total_count = len(results)
        
        for step, success in results.items():
            status = "✅" if success else "❌"
            self.print_status(f"{status} {step}: {'نجح' if success else 'فشل'}", 
                            "SUCCESS" if success else "ERROR")
        
        self.print_status("="*60, "INFO")
        self.print_status(f"📊 النتيجة الإجمالية: {success_count}/{total_count}", "INFO")
        
        if success_count == total_count:
            self.print_status("🎉 تم إصلاح جميع المشاكل بنجاح!", "SUCCESS")
            self.print_status(f"🌐 الخدمة متاحة على: https://{self.service_name}-554716410816.{self.region}.run.app", "SUCCESS")
        elif success_count >= total_count * 0.7:
            self.print_status("⚠️ تم إصلاح معظم المشاكل", "WARNING")
        else:
            self.print_status("❌ فشل في إصلاح المشاكل", "ERROR")

def main():
    """الدالة الرئيسية"""
    print("🔧 Universal AI Assistants - إصلاح النشر السحابي")
    print("="*60)
    
    fixer = CloudDeploymentFixer()
    results = fixer.run_full_fix()
    
    return 0 if all(results.values()) else 1

if __name__ == "__main__":
    sys.exit(main())
