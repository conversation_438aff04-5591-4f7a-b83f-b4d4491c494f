#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 اختبار شامل للمشروع الكامل - Final Complete System Test
==========================================================

هذا الملف يقوم بإجراء اختبار شامل لجميع مكونات المشروع:
- نظام أنوبيس (ANUBIS_SYSTEM)
- فريق حورس (HORUS_AI_TEAM)  
- نظام MCP المتكامل (ANUBIS_HORUS_MCP)
- التكامل والتوثيق

📅 تاريخ الإنشاء: 2025-01-27
🔧 الإصدار: 3.0.0
👨‍💻 المطور: Universal AI Assistants Team
"""

import os
import sys
import json
import time
import subprocess
import platform
import importlib.util
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Any

class CompleteSystemTester:
    """فئة الاختبار الشامل للنظام الكامل"""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.test_results = {
            "metadata": {
                "timestamp": self.start_time.isoformat(),
                "version": "3.0.0",
                "tester": "Complete System Tester"
            },
            "system_info": self._get_system_info(),
            "tests": {},
            "summary": {},
            "recommendations": []
        }
        self.passed_tests = 0
        self.failed_tests = 0
        self.warning_tests = 0
        self.total_tests = 0
        
    def _get_system_info(self) -> Dict[str, Any]:
        """جمع معلومات النظام الشاملة"""
        try:
            import psutil
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('.')
            system_info = {
                "platform": platform.system(),
                "platform_version": platform.version(),
                "python_version": platform.python_version(),
                "architecture": platform.architecture()[0],
                "processor": platform.processor(),
                "hostname": platform.node(),
                "memory_total_gb": round(memory.total / (1024**3), 2),
                "memory_available_gb": round(memory.available / (1024**3), 2),
                "disk_total_gb": round(disk.total / (1024**3), 2),
                "disk_free_gb": round(disk.free / (1024**3), 2)
            }
        except ImportError:
            system_info = {
                "platform": platform.system(),
                "platform_version": platform.version(),
                "python_version": platform.python_version(),
                "architecture": platform.architecture()[0],
                "processor": platform.processor(),
                "hostname": platform.node(),
                "note": "psutil not available - limited system info"
            }
        
        return system_info
    
    def _print_header(self):
        """طباعة رأس الاختبار المحسن"""
        print("=" * 100)
        print("🎯 اختبار شامل للمشروع الكامل - Final Complete System Test")
        print("=" * 100)
        print(f"📅 وقت البدء: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"💻 النظام: {self.test_results['system_info']['platform']} {self.test_results['system_info']['architecture']}")
        print(f"🐍 Python: {self.test_results['system_info']['python_version']}")
        print(f"🖥️ المعالج: {self.test_results['system_info']['processor']}")
        if 'memory_total_gb' in self.test_results['system_info']:
            print(f"💾 الذاكرة: {self.test_results['system_info']['memory_available_gb']:.1f}GB متاحة من {self.test_results['system_info']['memory_total_gb']:.1f}GB")
            print(f"💿 القرص: {self.test_results['system_info']['disk_free_gb']:.1f}GB متاحة من {self.test_results['system_info']['disk_total_gb']:.1f}GB")
        print("=" * 100)
    
    def _print_test_result(self, test_name: str, status: str, details: str = "", score: int = 0):
        """طباعة نتيجة اختبار محسنة"""
        if status == "pass":
            status_icon = "✅"
            self.passed_tests += 1
        elif status == "fail":
            status_icon = "❌"
            self.failed_tests += 1
        elif status == "warning":
            status_icon = "⚠️"
            self.warning_tests += 1
        else:
            status_icon = "❓"
        
        score_text = f" ({score}/100)" if score > 0 else ""
        print(f"{status_icon} {test_name}{score_text}")
        
        if details:
            for line in details.split('\n'):
                if line.strip():
                    print(f"   📝 {line.strip()}")
        
        self.total_tests += 1
    
    def test_project_structure(self) -> Dict[str, Any]:
        """اختبار هيكل المشروع الشامل"""
        print("\n🏗️ اختبار هيكل المشروع...")
        
        required_dirs = {
            "ANUBIS_SYSTEM": "نظام أنوبيس الأساسي",
            "HORUS_AI_TEAM": "فريق حورس للذكاء الاصطناعي", 
            "ANUBIS_HORUS_MCP": "نظام MCP المتكامل",
            "PROJECT_DOCUMENTATION": "التوثيق العام",
            "SHARED_REQUIREMENTS": "المتطلبات المشتركة"
        }
        
        structure_results = {
            "required_dirs": {},
            "file_counts": {},
            "total_size_mb": 0,
            "score": 0
        }
        
        # فحص المجلدات المطلوبة
        required_score = 0
        total_size = 0
        
        for dir_name, description in required_dirs.items():
            if os.path.exists(dir_name):
                file_count = sum(len(files) for _, _, files in os.walk(dir_name))
                try:
                    dir_size = sum(os.path.getsize(os.path.join(dirpath, filename))
                                 for dirpath, _, filenames in os.walk(dir_name)
                                 for filename in filenames) / (1024 * 1024)  # MB
                except (OSError, PermissionError):
                    dir_size = 0
                
                structure_results["required_dirs"][dir_name] = {
                    "exists": True,
                    "description": description,
                    "file_count": file_count,
                    "size_mb": round(dir_size, 2)
                }
                total_size += dir_size
                required_score += 1
                
                self._print_test_result(
                    f"مجلد {dir_name}",
                    "pass",
                    f"{description}\nعدد الملفات: {file_count}\nالحجم: {dir_size:.1f} MB"
                )
            else:
                structure_results["required_dirs"][dir_name] = {
                    "exists": False,
                    "description": description
                }
                self._print_test_result(f"مجلد {dir_name}", "fail", f"{description} - غير موجود")
        
        # حساب النقاط
        total_score = int((required_score / len(required_dirs)) * 100)
        structure_results["score"] = total_score
        structure_results["total_size_mb"] = round(total_size, 2)
        
        self._print_test_result(
            "تقييم هيكل المشروع",
            "pass" if total_score >= 80 else "warning" if total_score >= 60 else "fail",
            f"النقاط: {total_score}/100\nحجم المشروع الإجمالي: {structure_results['total_size_mb']:.1f} MB",
            total_score
        )
        
        self.test_results["tests"]["project_structure"] = structure_results
        return structure_results
    
    def test_anubis_system(self) -> Dict[str, Any]:
        """اختبار نظام أنوبيس"""
        print("\n🏺 اختبار نظام أنوبيس...")
        
        anubis_results = {"essential_files": {}, "score": 0}
        
        essential_files = {
            "ANUBIS_SYSTEM/main.py": "الملف الرئيسي",
            "ANUBIS_SYSTEM/README.md": "ملف التوثيق",
            "ANUBIS_SYSTEM/requirements.txt": "متطلبات Python"
        }
        
        essential_score = 0
        for file_path, description in essential_files.items():
            if os.path.exists(file_path):
                size = os.path.getsize(file_path) if os.path.isfile(file_path) else 0
                anubis_results["essential_files"][file_path] = {
                    "exists": True,
                    "size_bytes": size,
                    "description": description
                }
                essential_score += 1
                self._print_test_result(f"ملف {os.path.basename(file_path)}", "pass", description)
            else:
                anubis_results["essential_files"][file_path] = {
                    "exists": False,
                    "description": description
                }
                self._print_test_result(f"ملف {os.path.basename(file_path)}", "fail", f"{description} - غير موجود")
        
        total_score = int((essential_score / len(essential_files)) * 100)
        anubis_results["score"] = total_score
        
        self._print_test_result(
            "تقييم نظام أنوبيس",
            "pass" if total_score >= 80 else "warning" if total_score >= 60 else "fail",
            f"النقاط: {total_score}/100",
            total_score
        )
        
        self.test_results["tests"]["anubis_system"] = anubis_results
        return anubis_results
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        self._print_header()
        
        # تشغيل الاختبارات الأساسية
        self.test_project_structure()
        self.test_anubis_system()
        
        # إنشاء الملخص
        self._generate_summary()
        
        # حفظ النتائج
        self._save_results()
        
        return self.failed_tests == 0
    
    def _generate_summary(self):
        """إنشاء ملخص النتائج"""
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        success_rate = (self.passed_tests / self.total_tests * 100) if self.total_tests > 0 else 0
        
        self.test_results["summary"] = {
            "total_tests": self.total_tests,
            "passed_tests": self.passed_tests,
            "failed_tests": self.failed_tests,
            "warning_tests": self.warning_tests,
            "success_rate": round(success_rate, 1),
            "duration_seconds": round(duration.total_seconds(), 2),
            "end_time": end_time.isoformat()
        }
        
        print("\n" + "=" * 100)
        print("📊 ملخص نتائج الاختبار الشامل")
        print("=" * 100)
        print(f"🎯 معدل النجاح: {success_rate:.1f}%")
        print(f"✅ اختبارات ناجحة: {self.passed_tests}")
        print(f"⚠️ تحذيرات: {self.warning_tests}")
        print(f"❌ اختبارات فاشلة: {self.failed_tests}")
        print(f"📊 إجمالي الاختبارات: {self.total_tests}")
        print(f"⏱️ مدة الاختبار: {duration.total_seconds():.2f} ثانية")
        print("=" * 100)
    
    def _save_results(self):
        """حفظ نتائج الاختبار"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"final_complete_system_test_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, ensure_ascii=False, indent=2)
            print(f"💾 تم حفظ النتائج في: {filename}")
        except Exception as e:
            print(f"❌ خطأ في حفظ النتائج: {str(e)}")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار شامل للمشروع الكامل...")
    
    tester = CompleteSystemTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 تم اجتياز جميع الاختبارات بنجاح!")
        print("🚀 المشروع جاهز للاستخدام الفوري!")
    else:
        print("\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة النتائج أعلاه.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
