#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 أوامر فحص وتحليل نشر مشروع Universal AI Assistants
========================================================

مجموعة شاملة من الأوامر لفحص وتحليل وتحضير المشروع للنشر
مع دعم متعدد المنصات وتقارير مفصلة.

المطور: نظام أنوبيس للذكاء الاصطناعي
التاريخ: 2025-01-29
الإصدار: 1.0.0
"""

import os
import sys
import subprocess
import json
import shutil
from pathlib import Path
from datetime import datetime
import argparse
import platform

class DeploymentCommands:
    """مدير أوامر النشر والتحليل"""
    
    def __init__(self):
        self.project_root = Path.cwd()
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.system = platform.system().lower()
        
        # مسارات مهمة
        self.reports_dir = self.project_root / "deployment_reports"
        self.reports_dir.mkdir(exist_ok=True)
        
        # أوامر النظام حسب المنصة
        self.commands = {
            "windows": {
                "docker": "docker",
                "git": "git",
                "python": "python",
                "pip": "pip"
            },
            "linux": {
                "docker": "docker",
                "git": "git", 
                "python": "python3",
                "pip": "pip3"
            },
            "darwin": {  # macOS
                "docker": "docker",
                "git": "git",
                "python": "python3", 
                "pip": "pip3"
            }
        }

    def run_command(self, command: str, capture_output: bool = True) -> dict:
        """تشغيل أمر النظام مع معالجة الأخطاء"""
        try:
            if capture_output:
                result = subprocess.run(
                    command, 
                    shell=True, 
                    capture_output=True, 
                    text=True,
                    timeout=300
                )
                return {
                    "success": result.returncode == 0,
                    "stdout": result.stdout,
                    "stderr": result.stderr,
                    "returncode": result.returncode
                }
            else:
                result = subprocess.run(command, shell=True)
                return {
                    "success": result.returncode == 0,
                    "returncode": result.returncode
                }
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "error": "Command timeout",
                "returncode": -1
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "returncode": -1
            }

    def check_system_requirements(self) -> dict:
        """فحص متطلبات النظام"""
        print("🔍 فحص متطلبات النظام...")
        
        requirements = {
            "python": {"required": True, "installed": False, "version": ""},
            "docker": {"required": True, "installed": False, "version": ""},
            "git": {"required": True, "installed": False, "version": ""},
            "node": {"required": False, "installed": False, "version": ""},
            "pip": {"required": True, "installed": False, "version": ""}
        }
        
        # فحص Python
        python_cmd = self.commands[self.system]["python"]
        result = self.run_command(f"{python_cmd} --version")
        if result["success"]:
            requirements["python"]["installed"] = True
            requirements["python"]["version"] = result["stdout"].strip()
        
        # فحص Docker
        result = self.run_command("docker --version")
        if result["success"]:
            requirements["docker"]["installed"] = True
            requirements["docker"]["version"] = result["stdout"].strip()
        
        # فحص Git
        result = self.run_command("git --version")
        if result["success"]:
            requirements["git"]["installed"] = True
            requirements["git"]["version"] = result["stdout"].strip()
        
        # فحص Node.js
        result = self.run_command("node --version")
        if result["success"]:
            requirements["node"]["installed"] = True
            requirements["node"]["version"] = result["stdout"].strip()
        
        # فحص pip
        pip_cmd = self.commands[self.system]["pip"]
        result = self.run_command(f"{pip_cmd} --version")
        if result["success"]:
            requirements["pip"]["installed"] = True
            requirements["pip"]["version"] = result["stdout"].strip()
        
        return requirements

    def analyze_project_health(self) -> dict:
        """تحليل صحة المشروع"""
        print("🏥 تحليل صحة المشروع...")
        
        health_report = {
            "timestamp": self.timestamp,
            "overall_health": 0,
            "components": {},
            "issues": [],
            "recommendations": []
        }
        
        # فحص المكونات الرئيسية
        main_components = [
            "ANUBIS_SYSTEM",
            "HORUS_AI_TEAM",
            "ANUBIS_HORUS_MCP",
            "PROJECT_DOCUMENTATION"
        ]
        
        for component in main_components:
            component_path = self.project_root / component
            if component_path.exists():
                health_report["components"][component] = {
                    "status": "healthy",
                    "files": len(list(component_path.rglob("*"))),
                    "size_mb": self._get_dir_size(component_path)
                }
            else:
                health_report["components"][component] = {
                    "status": "missing",
                    "files": 0,
                    "size_mb": 0
                }
                health_report["issues"].append(f"مكون مفقود: {component}")
        
        # حساب الصحة العامة
        healthy_components = sum(1 for comp in health_report["components"].values() if comp["status"] == "healthy")
        health_report["overall_health"] = (healthy_components / len(main_components)) * 100
        
        return health_report

    def check_docker_status(self) -> dict:
        """فحص حالة Docker"""
        print("🐳 فحص حالة Docker...")
        
        docker_status = {
            "docker_installed": False,
            "docker_running": False,
            "containers": [],
            "images": [],
            "compose_files": []
        }
        
        # فحص تثبيت Docker
        result = self.run_command("docker --version")
        docker_status["docker_installed"] = result["success"]
        
        if docker_status["docker_installed"]:
            # فحص تشغيل Docker
            result = self.run_command("docker info")
            docker_status["docker_running"] = result["success"]
            
            if docker_status["docker_running"]:
                # قائمة الحاويات
                result = self.run_command("docker ps -a --format json")
                if result["success"] and result["stdout"].strip():
                    try:
                        containers = []
                        for line in result["stdout"].strip().split('\n'):
                            if line.strip():
                                containers.append(json.loads(line))
                        docker_status["containers"] = containers
                    except:
                        pass
                
                # قائمة الصور
                result = self.run_command("docker images --format json")
                if result["success"] and result["stdout"].strip():
                    try:
                        images = []
                        for line in result["stdout"].strip().split('\n'):
                            if line.strip():
                                images.append(json.loads(line))
                        docker_status["images"] = images
                    except:
                        pass
        
        # البحث عن ملفات docker-compose
        compose_files = list(self.project_root.rglob("docker-compose*.yml"))
        compose_files.extend(list(self.project_root.rglob("docker-compose*.yaml")))
        docker_status["compose_files"] = [str(f.relative_to(self.project_root)) for f in compose_files]
        
        return docker_status

    def analyze_dependencies(self) -> dict:
        """تحليل التبعيات"""
        print("📦 تحليل التبعيات...")
        
        deps_analysis = {
            "python_requirements": [],
            "node_packages": [],
            "outdated_packages": [],
            "security_vulnerabilities": []
        }
        
        # تحليل متطلبات Python
        req_files = list(self.project_root.rglob("requirements*.txt"))
        for req_file in req_files:
            try:
                with open(req_file, 'r') as f:
                    requirements = [line.strip() for line in f.readlines() 
                                  if line.strip() and not line.startswith('#')]
                    deps_analysis["python_requirements"].extend(requirements)
            except:
                pass
        
        # تحليل حزم Node.js
        package_files = list(self.project_root.rglob("package.json"))
        for package_file in package_files:
            try:
                with open(package_file, 'r') as f:
                    package_data = json.load(f)
                    deps = package_data.get("dependencies", {})
                    dev_deps = package_data.get("devDependencies", {})
                    deps_analysis["node_packages"].extend(list(deps.keys()) + list(dev_deps.keys()))
            except:
                pass
        
        # فحص الحزم القديمة (Python)
        pip_cmd = self.commands[self.system]["pip"]
        result = self.run_command(f"{pip_cmd} list --outdated --format=json")
        if result["success"] and result["stdout"].strip():
            try:
                outdated = json.loads(result["stdout"])
                deps_analysis["outdated_packages"] = outdated
            except:
                pass
        
        return deps_analysis

    def generate_deployment_checklist(self) -> dict:
        """توليد قائمة فحص النشر"""
        print("📋 توليد قائمة فحص النشر...")
        
        checklist = {
            "pre_deployment": [
                {"task": "فحص متطلبات النظام", "completed": False},
                {"task": "تحديث التبعيات", "completed": False},
                {"task": "تشغيل الاختبارات", "completed": False},
                {"task": "فحص الأمان", "completed": False},
                {"task": "تحديث التوثيق", "completed": False}
            ],
            "deployment": [
                {"task": "بناء صور Docker", "completed": False},
                {"task": "اختبار الحاويات", "completed": False},
                {"task": "إعداد قواعد البيانات", "completed": False},
                {"task": "تكوين المتغيرات البيئية", "completed": False},
                {"task": "نشر التطبيق", "completed": False}
            ],
            "post_deployment": [
                {"task": "فحص الصحة", "completed": False},
                {"task": "مراقبة الأداء", "completed": False},
                {"task": "اختبار الوظائف", "completed": False},
                {"task": "إعداد النسخ الاحتياطية", "completed": False},
                {"task": "توثيق النشر", "completed": False}
            ]
        }
        
        return checklist

    def run_security_scan(self) -> dict:
        """تشغيل فحص الأمان"""
        print("🔒 تشغيل فحص الأمان...")
        
        security_report = {
            "sensitive_files": [],
            "exposed_secrets": [],
            "permissions_issues": [],
            "recommendations": []
        }
        
        # البحث عن الملفات الحساسة
        sensitive_patterns = [
            "*.key", "*.pem", "*.p12", "*.pfx", 
            "id_rsa*", "*.crt", "*.cer"
        ]
        
        for pattern in sensitive_patterns:
            files = list(self.project_root.rglob(pattern))
            security_report["sensitive_files"].extend([
                str(f.relative_to(self.project_root)) for f in files
            ])
        
        # البحث عن الأسرار المكشوفة
        secret_patterns = [
            "password", "secret", "token", "key", "api_key"
        ]
        
        for root, dirs, files in os.walk(self.project_root):
            # تجاهل مجلدات معينة
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['node_modules', '__pycache__']]
            
            for file in files:
                if file.endswith(('.py', '.js', '.json', '.env', '.yml', '.yaml')):
                    file_path = Path(root) / file
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read().lower()
                            for pattern in secret_patterns:
                                if pattern in content and '=' in content:
                                    security_report["exposed_secrets"].append({
                                        "file": str(file_path.relative_to(self.project_root)),
                                        "pattern": pattern
                                    })
                                    break
                    except:
                        pass
        
        # توصيات الأمان
        if security_report["sensitive_files"]:
            security_report["recommendations"].append("إزالة أو تأمين الملفات الحساسة")
        
        if security_report["exposed_secrets"]:
            security_report["recommendations"].append("إزالة الأسرار المكشوفة من الكود")
        
        return security_report

    def _get_dir_size(self, path: Path) -> float:
        """حساب حجم المجلد بالميجابايت"""
        total_size = 0
        try:
            for dirpath, dirnames, filenames in os.walk(path):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    try:
                        total_size += os.path.getsize(filepath)
                    except:
                        pass
        except:
            pass
        return round(total_size / (1024 * 1024), 2)

    def save_report(self, report_data: dict, report_name: str) -> str:
        """حفظ التقرير"""
        report_file = self.reports_dir / f"{report_name}_{self.timestamp}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        return str(report_file)

    def run_full_analysis(self) -> dict:
        """تشغيل التحليل الكامل"""
        print("🚀 بدء التحليل الشامل للمشروع...")
        print("=" * 60)
        
        full_report = {
            "timestamp": self.timestamp,
            "project_path": str(self.project_root),
            "system_info": {
                "platform": platform.platform(),
                "python_version": platform.python_version()
            }
        }
        
        # تشغيل جميع التحليلات
        full_report["system_requirements"] = self.check_system_requirements()
        full_report["project_health"] = self.analyze_project_health()
        full_report["docker_status"] = self.check_docker_status()
        full_report["dependencies"] = self.analyze_dependencies()
        full_report["security_scan"] = self.run_security_scan()
        full_report["deployment_checklist"] = self.generate_deployment_checklist()
        
        # حفظ التقرير
        report_file = self.save_report(full_report, "full_deployment_analysis")
        
        print(f"\n📄 التقرير الشامل محفوظ في: {report_file}")
        
        return full_report

def main():
    """الدالة الرئيسية"""
    parser = argparse.ArgumentParser(description="أوامر فحص وتحليل نشر المشروع")
    parser.add_argument("--command", "-c", choices=[
        "requirements", "health", "docker", "dependencies", 
        "security", "checklist", "full"
    ], default="full", help="نوع التحليل المطلوب")
    
    args = parser.parse_args()
    
    print("🏺 أوامر فحص وتحليل نشر مشروع Universal AI Assistants")
    print("=" * 60)
    
    commands = DeploymentCommands()
    
    if args.command == "requirements":
        result = commands.check_system_requirements()
        print(json.dumps(result, ensure_ascii=False, indent=2))
    elif args.command == "health":
        result = commands.analyze_project_health()
        print(json.dumps(result, ensure_ascii=False, indent=2))
    elif args.command == "docker":
        result = commands.check_docker_status()
        print(json.dumps(result, ensure_ascii=False, indent=2))
    elif args.command == "dependencies":
        result = commands.analyze_dependencies()
        print(json.dumps(result, ensure_ascii=False, indent=2))
    elif args.command == "security":
        result = commands.run_security_scan()
        print(json.dumps(result, ensure_ascii=False, indent=2))
    elif args.command == "checklist":
        result = commands.generate_deployment_checklist()
        print(json.dumps(result, ensure_ascii=False, indent=2))
    else:  # full
        result = commands.run_full_analysis()
        
        # عرض ملخص النتائج
        print("\n📊 ملخص النتائج:")
        print(f"🏥 صحة المشروع: {result['project_health']['overall_health']:.1f}%")
        print(f"🐳 Docker متاح: {'نعم' if result['docker_status']['docker_installed'] else 'لا'}")
        print(f"📦 متطلبات Python: {len(result['dependencies']['python_requirements'])}")
        print(f"🔒 مشاكل أمان: {len(result['security_scan']['exposed_secrets'])}")

if __name__ == "__main__":
    main()
