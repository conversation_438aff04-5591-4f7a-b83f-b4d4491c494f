{"timestamp": "2025-08-01T00:06:47.778909", "total_tests": 33, "passed_tests": 32, "failed_tests": 1, "skipped_tests": 0, "test_details": {"HORUS_AI_TEAM Structure": {"status": "PASSED", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\HORUS_AI_TEAM", "file_count": 886}, "HORUS summon_horus_assistant.py": {"status": "PASSED", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\HORUS_AI_TEAM\\summon_horus_assistant.py", "size_kb": 9.7822265625}, "HORUS quick_start_fixed.py": {"status": "PASSED", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\HORUS_AI_TEAM\\quick_start_fixed.py", "size_kb": 13.732421875}, "HORUS horus_direct_test.py": {"status": "PASSED", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\HORUS_AI_TEAM\\horus_direct_test.py", "size_kb": 8.728515625}, "HORUS 01_core": {"status": "PASSED", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\HORUS_AI_TEAM\\01_core", "file_count": 27}, "HORUS 02_team_members": {"status": "PASSED", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\HORUS_AI_TEAM\\02_team_members", "file_count": 13}, "HORUS 03_memory_system": {"status": "PASSED", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\HORUS_AI_TEAM\\03_memory_system", "file_count": 23}, "HORUS 04_collaboration": {"status": "PASSED", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\HORUS_AI_TEAM\\04_collaboration", "file_count": 21}, "HORUS 05_analysis": {"status": "PASSED", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\HORUS_AI_TEAM\\05_analysis", "file_count": 75}, "HORUS 06_documentation": {"status": "PASSED", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\HORUS_AI_TEAM\\06_documentation", "file_count": 35}, "ANUBIS_SYSTEM Structure": {"status": "PASSED", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\ANUBIS_SYSTEM", "file_count": 3120}, "ANUBIS main.py": {"status": "PASSED", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\ANUBIS_SYSTEM\\main.py", "size_kb": 2.171875}, "ANUBIS quick_start_anubis.py": {"status": "PASSED", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\ANUBIS_SYSTEM\\quick_start_anubis.py", "size_kb": 15.2216796875}, "ANUBIS startup.py": {"status": "PASSED", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\ANUBIS_SYSTEM\\startup.py", "size_kb": 0.0}, "ANUBIS config/ai_config.json": {"status": "PASSED", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\ANUBIS_SYSTEM\\config\\ai_config.json", "size_kb": 1.1943359375}, "ANUBIS config/default_config.json": {"status": "PASSED", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\ANUBIS_SYSTEM\\config\\default_config.json", "size_kb": 4.0849609375}, "ANUBIS_HORUS_MCP Structure": {"status": "PASSED", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\ANUBIS_HORUS_MCP", "file_count": 1050}, "MCP package.json": {"status": "PASSED", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\ANUBIS_HORUS_MCP\\package.json", "size_kb": 0.6220703125}, "MCP mcp-config.json": {"status": "PASSED", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\ANUBIS_HORUS_MCP\\mcp-config.json", "size_kb": 0.2138671875}, "MCP collaborative_ai_system.py": {"status": "PASSED", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\ANUBIS_HORUS_MCP\\collaborative_ai_system.py", "size_kb": 38.0283203125}, "MCP advanced_collaborative_system.py": {"status": "PASSED", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\ANUBIS_HORUS_MCP\\advanced_collaborative_system.py", "size_kb": 21.33984375}, "Scripts Directory": {"status": "PASSED", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\scripts", "file_count": 75}, "Script QUICK_START.py": {"status": "PASSED", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\scripts\\QUICK_START.py", "size_kb": 5.52734375}, "Script INTEGRATE_ALL_PROJECTS.py": {"status": "PASSED", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\scripts\\INTEGRATE_ALL_PROJECTS.py", "size_kb": 10.732421875}, "Script LAUNCH_ANUBIS_COMPLETE.py": {"status": "PASSED", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\scripts\\LAUNCH_ANUBIS_COMPLETE.py", "size_kb": 8.669921875}, "Script HORUS_PROJECT_ANALYZER.py": {"status": "PASSED", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\scripts\\HORUS_PROJECT_ANALYZER.py", "size_kb": 17.1455078125}, "Documentation Directory": {"status": "PASSED", "path": "C:\\Users\\<USER>\\Universal-AI-Assistants\\PROJECT_DOCUMENTATION", "file_count": 125}}, "duration_seconds": 0.25139284133911133, "success_rate": 96.96969696969697, "summary": {"total": 33, "passed": 32, "failed": 1, "skipped": 0}}