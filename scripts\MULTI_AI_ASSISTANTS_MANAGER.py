#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 مدير المساعدين الذكيين المتعددين
Multi AI Assistants Manager for Ollama Cloud Migration Project
"""

import os
import json
import requests
import logging
from datetime import datetime
from pathlib import Path

class MultiAIAssistantsManager:
    """مدير المساعدين الذكيين المتعددين"""
    
    def __init__(self):
        self.setup_logging()
        self.setup_api_keys()
        self.assistants = {}
        self.active_tasks = {}
        
    def setup_logging(self):
        """إعداد نظام السجلات"""
        log_dir = Path("multi_ai_logs")
        log_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = log_dir / f"multi_ai_manager_{timestamp}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_api_keys(self):
        """إعداد مفاتيح API للنماذج المختلفة"""
        self.api_keys = {
            'gemini': {
                'key': 'AIzaSyBc4zdWiwluNW-2_aScTNY3ZA9k4k15g9k',
                'endpoint': 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent',
                'model': 'gemini-pro'
            },
            'qwen': {
                'key': '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',
                'endpoint': 'https://codestral.mistral.ai/v1/chat/completions',
                'model': 'qwen-72b-chat'
            }
        }
        
        # حفظ المفاتيح في متغيرات البيئة
        os.environ['GEMINI_API_KEY'] = self.api_keys['gemini']['key']
        os.environ['QWEN_API_KEY'] = self.api_keys['qwen']['key']
        
        self.logger.info("🔑 تم إعداد مفاتيح API للنماذج المختلفة")
        
    def test_gemini_connection(self):
        """اختبار الاتصال مع Gemini"""
        try:
            headers = {
                'Content-Type': 'application/json',
                'x-goog-api-key': self.api_keys['gemini']['key']
            }
            
            data = {
                "contents": [{
                    "parts": [{
                        "text": "مرحبا، هل تعمل بشكل صحيح؟"
                    }]
                }]
            }
            
            response = requests.post(
                self.api_keys['gemini']['endpoint'],
                headers=headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and result['candidates']:
                    text = result['candidates'][0]['content']['parts'][0]['text']
                    self.logger.info("✅ Gemini متصل ويعمل بنجاح")
                    return True, text
                    
            self.logger.error(f"❌ خطأ في Gemini: {response.status_code}")
            return False, None
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في اتصال Gemini: {e}")
            return False, None
            
    def test_qwen_connection(self):
        """اختبار الاتصال مع Qwen"""
        try:
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {self.api_keys["qwen"]["key"]}'
            }
            
            data = {
                "model": "qwen-72b-chat",
                "messages": [{
                    "role": "user",
                    "content": "مرحبا، هل تعمل بشكل صحيح؟"
                }],
                "max_tokens": 100
            }
            
            response = requests.post(
                self.api_keys['qwen']['endpoint'],
                headers=headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if 'choices' in result and result['choices']:
                    text = result['choices'][0]['message']['content']
                    self.logger.info("✅ Qwen متصل ويعمل بنجاح")
                    return True, text
                    
            self.logger.error(f"❌ خطأ في Qwen: {response.status_code}")
            return False, None
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في اتصال Qwen: {e}")
            return False, None
            
    def call_gemini(self, prompt):
        """استدعاء Gemini مع prompt"""
        try:
            headers = {
                'Content-Type': 'application/json',
                'x-goog-api-key': self.api_keys['gemini']['key']
            }
            
            data = {
                "contents": [{
                    "parts": [{
                        "text": prompt
                    }]
                }]
            }
            
            response = requests.post(
                self.api_keys['gemini']['endpoint'],
                headers=headers,
                json=data,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                if 'candidates' in result and result['candidates']:
                    return result['candidates'][0]['content']['parts'][0]['text']
                    
            return None
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في استدعاء Gemini: {e}")
            return None
            
    def call_qwen(self, prompt):
        """استدعاء Qwen مع prompt"""
        try:
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {self.api_keys["qwen"]["key"]}'
            }
            
            data = {
                "model": "qwen-72b-chat",
                "messages": [{
                    "role": "user",
                    "content": prompt
                }],
                "max_tokens": 2000
            }
            
            response = requests.post(
                self.api_keys['qwen']['endpoint'],
                headers=headers,
                json=data,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                if 'choices' in result and result['choices']:
                    return result['choices'][0]['message']['content']
                    
            return None
            
        except Exception as e:
            self.logger.error(f"❌ خطأ في استدعاء Qwen: {e}")
            return None
            
    def check_horus_team(self):
        """فحص فريق حورس المحلي"""
        try:
            horus_path = Path("HORUS_AI_TEAM")
            if horus_path.exists():
                self.logger.info("✅ فريق حورس متوفر محلياً")
                return True
            else:
                self.logger.warning("⚠️ فريق حورس غير متوفر")
                return False
        except Exception as e:
            self.logger.error(f"❌ خطأ في فحص فريق حورس: {e}")
            return False
            
    def initialize_assistants(self):
        """تهيئة جميع المساعدين"""
        self.logger.info("🤖 تهيئة المساعدين الذكيين...")
        
        # اختبار الاتصالات
        gemini_status, gemini_response = self.test_gemini_connection()
        qwen_status, qwen_response = self.test_qwen_connection()
        horus_status = self.check_horus_team()
        
        self.assistants = {
            'gemini': {
                'name': 'Google Gemini Pro',
                'status': gemini_status,
                'specialties': ['تحليل شامل', 'البرمجة', 'التخطيط'],
                'response': gemini_response,
                'available': gemini_status
            },
            'qwen': {
                'name': 'Qwen 72B Chat',
                'status': qwen_status,
                'specialties': ['البرمجة المتقدمة', 'حل المشاكل', 'التحليل التقني'],
                'response': qwen_response,
                'available': qwen_status
            },
            'horus_team': {
                'name': 'فريق حورس المحلي',
                'status': horus_status,
                'specialties': ['إدارة المشاريع', 'التحليل المحلي', 'التنسيق'],
                'response': 'فريق حورس جاهز للعمل' if horus_status else None,
                'available': horus_status
            }
        }
        
        return self.assistants
        
    def assign_ollama_cloud_tasks(self):
        """توزيع مهام مشروع Ollama Cloud"""
        self.logger.info("📋 توزيع مهام مشروع تخزين Ollama على Google Cloud...")
        
        # المهام الرئيسية
        tasks = {
            'infrastructure_analysis': {
                'title': 'تحليل البنية التحتية الحالية',
                'assigned_to': 'horus_team',
                'description': 'فحص النماذج المحلية وتحليل الأحجام والأولويات',
                'priority': 'high'
            },
            'cloud_setup_planning': {
                'title': 'تخطيط إعداد Google Cloud',
                'assigned_to': 'gemini',
                'description': 'إنشاء خطة شاملة لإعداد البنية التحتية على Google Cloud',
                'priority': 'high'
            },
            'migration_tool_development': {
                'title': 'تطوير أدوات الترحيل',
                'assigned_to': 'qwen',
                'description': 'كتابة الكود اللازم لترحيل النماذج تلقائياً',
                'priority': 'high'
            },
            'security_implementation': {
                'title': 'تطبيق الأمان والتشفير',
                'assigned_to': 'gemini',
                'description': 'تصميم نظام أمان شامل للنماذج المخزنة',
                'priority': 'high'
            },
            'monitoring_system': {
                'title': 'نظام المراقبة والتتبع',
                'assigned_to': 'qwen',
                'description': 'تطوير نظام لمراقبة عملية الترحيل والأداء',
                'priority': 'medium'
            },
            'cost_optimization': {
                'title': 'تحسين التكاليف',
                'assigned_to': 'horus_team',
                'description': 'تحليل وتحسين تكاليف التخزين والاستخدام',
                'priority': 'medium'
            }
        }
        
        self.active_tasks = tasks
        return tasks
        
    def execute_task(self, task_id):
        """تنفيذ مهمة محددة"""
        if task_id not in self.active_tasks:
            self.logger.error(f"❌ المهمة {task_id} غير موجودة")
            return None
            
        task = self.active_tasks[task_id]
        assistant_id = task['assigned_to']
        
        if not self.assistants[assistant_id]['available']:
            self.logger.error(f"❌ المساعد {assistant_id} غير متوفر")
            return None
            
        self.logger.info(f"🚀 تنفيذ مهمة: {task['title']}")
        self.logger.info(f"👤 المساعد: {self.assistants[assistant_id]['name']}")
        
        # إنشاء prompt مفصل للمهمة
        prompt = f"""
أنت مساعد ذكي متخصص في {', '.join(self.assistants[assistant_id]['specialties'])}.

المهمة: {task['title']}
الوصف: {task['description']}
الأولوية: {task['priority']}

السياق: نحن نعمل على مشروع لتخزين نماذج Ollama على Google Cloud. المشروع يتطلب:
1. تحليل النماذج المحلية الموجودة
2. إعداد البنية التحتية على Google Cloud
3. تطوير أدوات الترحيل التلقائي
4. تطبيق أفضل ممارسات الأمان
5. إنشاء نظام مراقبة شامل

يرجى تقديم تحليل مفصل وخطة عمل لهذه المهمة، مع التركيز على:
- الخطوات العملية المطلوبة
- الأدوات والتقنيات المناسبة
- التحديات المحتملة والحلول
- التوقيتات المقترحة
- المتطلبات التقنية

قدم الإجابة باللغة العربية مع تفاصيل تقنية دقيقة.
"""

        # استدعاء المساعد المناسب
        if assistant_id == 'gemini':
            result = self.call_gemini(prompt)
        elif assistant_id == 'qwen':
            result = self.call_qwen(prompt)
        elif assistant_id == 'horus_team':
            result = "فريق حورس: سيتم تنفيذ هذه المهمة محلياً باستخدام أدوات التحليل المتاحة"
        else:
            result = None
            
        if result:
            self.logger.info(f"✅ تم إكمال المهمة: {task['title']}")
            
            # حفظ النتيجة
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            result_file = Path(f"task_results/task_{task_id}_{timestamp}.md")
            result_file.parent.mkdir(exist_ok=True)
            
            with open(result_file, 'w', encoding='utf-8') as f:
                f.write(f"# {task['title']}\n\n")
                f.write(f"**المساعد:** {self.assistants[assistant_id]['name']}\n")
                f.write(f"**التاريخ:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"**الأولوية:** {task['priority']}\n\n")
                f.write(f"## الوصف\n{task['description']}\n\n")
                f.write(f"## النتيجة\n{result}\n")
                
            self.logger.info(f"💾 تم حفظ النتيجة في: {result_file}")
            
        return result
        
    def print_status_report(self):
        """طباعة تقرير حالة المساعدين"""
        print("\n" + "=" * 80)
        print("🏺 تقرير حالة المساعدين الذكيين - مشروع تخزين Ollama على Google Cloud")
        print("=" * 80)
        
        print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 المشروع: تخزين نماذج Ollama على Google Cloud")
        
        print("\n🤖 حالة المساعدين:")
        print("-" * 80)
        
        for assistant_id, assistant in self.assistants.items():
            status_emoji = "✅" if assistant['available'] else "❌"
            print(f"\n{status_emoji} {assistant['name']}")
            print(f"   📋 التخصصات: {', '.join(assistant['specialties'])}")
            print(f"   🔗 الحالة: {'متصل ويعمل' if assistant['available'] else 'غير متوفر'}")
            if assistant['response']:
                print(f"   💬 الاستجابة: {assistant['response'][:100]}...")
                
        print("\n📋 المهام المُعينة:")
        print("-" * 80)
        
        for task_id, task in self.active_tasks.items():
            priority_emoji = {"high": "🔴", "medium": "🟡", "low": "🟢"}
            emoji = priority_emoji.get(task['priority'], "⚪")
            assistant_name = self.assistants[task['assigned_to']]['name']
            
            print(f"{emoji} {task['title']}")
            print(f"   👤 المساعد: {assistant_name}")
            print(f"   📝 الوصف: {task['description']}")
            print()
            
        available_assistants = sum(1 for a in self.assistants.values() if a['available'])
        total_assistants = len(self.assistants)
        
        print(f"📊 الإحصائيات:")
        print(f"   🤖 المساعدين المتاحين: {available_assistants}/{total_assistants}")
        print(f"   📋 إجمالي المهام: {len(self.active_tasks)}")
        print(f"   🎯 المهام عالية الأولوية: {sum(1 for t in self.active_tasks.values() if t['priority'] == 'high')}")
        
        print("\n" + "=" * 80)
        
    def run_project_coordination(self):
        """تشغيل تنسيق المشروع"""
        print("🏺 مدير المساعدين الذكيين المتعددين")
        print("مشروع: تخزين نماذج Ollama على Google Cloud")
        print("=" * 70)
        
        # تهيئة المساعدين
        self.initialize_assistants()
        
        # توزيع المهام
        self.assign_ollama_cloud_tasks()
        
        # طباعة تقرير الحالة
        self.print_status_report()
        
        self.logger.info("✅ تم إعداد تنسيق المشروع بنجاح!")
        
        return {
            'assistants': self.assistants,
            'tasks': self.active_tasks,
            'available_assistants': [aid for aid, a in self.assistants.items() if a['available']]
        }

def main():
    """الدالة الرئيسية"""
    manager = MultiAIAssistantsManager()
    coordination = manager.run_project_coordination()
    
    print("\n🎉 تم إعداد فريق المساعدين بنجاح!")
    print("\n📋 الخطوات التالية:")
    print("1. مراجعة تقرير حالة المساعدين")
    print("2. بدء تنفيذ المهام عالية الأولوية")
    print("3. متابعة التقدم وتحديث الحالة")
    
    # تنفيذ مهمة تجريبية
    if coordination['available_assistants']:
        print(f"\n🚀 تنفيذ مهمة تجريبية مع {coordination['available_assistants'][0]}...")
        first_task = list(manager.active_tasks.keys())[0]
        result = manager.execute_task(first_task)
        if result:
            print("✅ تم تنفيذ المهمة التجريبية بنجاح!")

if __name__ == "__main__":
    main()
