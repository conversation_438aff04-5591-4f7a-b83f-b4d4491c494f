#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏺 نظام توزيع المهام على المساعدين الذكيين
AI Assistants Task Distribution System
"""

import os
import json
import logging
from datetime import datetime
from pathlib import Path

class AIAssistantsTaskDistributor:
    """نظام توزيع المهام على المساعدين الذكيين"""
    
    def __init__(self):
        self.setup_logging()
        self.assistants = self.load_assistants()
        self.tasks = []
        self.assignments = {}
        
    def setup_logging(self):
        """إعداد نظام السجلات"""
        log_dir = Path("task_distribution_logs")
        log_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = log_dir / f"task_distribution_{timestamp}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def load_assistants(self):
        """تحميل قائمة المساعدين المتاحين"""
        assistants = {
            'horus_team': {
                'name': 'فريق حورس',
                'specialties': ['تحليل المشاريع', 'التخطيط الاستراتيجي', 'إدارة الفرق'],
                'capabilities': ['تحليل الكود', 'فحص الأمان', 'تحليل البيانات'],
                'availability': True,
                'max_concurrent_tasks': 3,
                'current_tasks': 0
            },
            'gemini_cli': {
                'name': 'Gemini CLI',
                'specialties': ['البرمجة', 'التحليل التقني', 'حل المشاكل'],
                'capabilities': ['كتابة الكود', 'تحليل الأخطاء', 'التوثيق'],
                'availability': True,
                'max_concurrent_tasks': 2,
                'current_tasks': 0
            },
            'claude_assistant': {
                'name': 'Claude Assistant',
                'specialties': ['التحليل المعمق', 'التخطيط', 'الكتابة التقنية'],
                'capabilities': ['تحليل شامل', 'إنشاء الوثائق', 'مراجعة الكود'],
                'availability': True,
                'max_concurrent_tasks': 2,
                'current_tasks': 0
            },
            'deployment_analyzer': {
                'name': 'محلل النشر',
                'specialties': ['تحليل النشر', 'فحص الجاهزية', 'تقييم الأمان'],
                'capabilities': ['فحص المشاريع', 'تحليل التبعيات', 'تقييم Docker'],
                'availability': True,
                'max_concurrent_tasks': 1,
                'current_tasks': 0
            }
        }
        return assistants
        
    def define_ollama_cloud_tasks(self):
        """تعريف مهام مشروع تخزين Ollama على Cloud"""
        tasks = [
            {
                'id': 'TASK_001',
                'title': 'تحليل البنية التحتية الحالية',
                'description': 'فحص وتحليل النماذج المحلية الموجودة وتقييم حجمها وأولوياتها',
                'priority': 'high',
                'estimated_hours': 2,
                'required_skills': ['تحليل النظم', 'فحص الملفات'],
                'dependencies': [],
                'deliverables': ['تقرير النماذج المحلية', 'تحليل الأحجام', 'تصنيف الأولويات']
            },
            {
                'id': 'TASK_002', 
                'title': 'إعداد Google Cloud Infrastructure',
                'description': 'إنشاء وتكوين البنية التحتية على Google Cloud للتخزين',
                'priority': 'high',
                'estimated_hours': 3,
                'required_skills': ['Google Cloud', 'إدارة التخزين', 'الأمان'],
                'dependencies': ['TASK_001'],
                'deliverables': ['سكريبت إعداد GCP', 'تكوين Buckets', 'إعداد الصلاحيات']
            },
            {
                'id': 'TASK_003',
                'title': 'تطوير نظام الترحيل التلقائي',
                'description': 'إنشاء أدوات لترحيل النماذج تلقائياً مع ضغط وتشفير',
                'priority': 'high',
                'estimated_hours': 4,
                'required_skills': ['Python', 'Docker', 'Google Cloud APIs'],
                'dependencies': ['TASK_002'],
                'deliverables': ['أداة الترحيل', 'نظام الضغط', 'آلية التشفير']
            },
            {
                'id': 'TASK_004',
                'title': 'إنشاء نظام المراقبة والتتبع',
                'description': 'تطوير نظام لمراقبة عملية الترحيل وتتبع التقدم',
                'priority': 'medium',
                'estimated_hours': 2,
                'required_skills': ['مراقبة النظم', 'APIs', 'التقارير'],
                'dependencies': ['TASK_003'],
                'deliverables': ['لوحة مراقبة', 'تقارير التقدم', 'تنبيهات']
            },
            {
                'id': 'TASK_005',
                'title': 'تحسين الأمان والنسخ الاحتياطية',
                'description': 'تطبيق أفضل ممارسات الأمان وإنشاء نظام النسخ الاحتياطية',
                'priority': 'high',
                'estimated_hours': 3,
                'required_skills': ['أمان السحابة', 'التشفير', 'النسخ الاحتياطية'],
                'dependencies': ['TASK_002'],
                'deliverables': ['سياسات الأمان', 'نظام النسخ الاحتياطية', 'تشفير البيانات']
            },
            {
                'id': 'TASK_006',
                'title': 'تطوير واجهة إدارة النماذج',
                'description': 'إنشاء واجهة ويب لإدارة النماذج المخزنة على Cloud',
                'priority': 'medium',
                'estimated_hours': 4,
                'required_skills': ['تطوير الويب', 'APIs', 'قواعد البيانات'],
                'dependencies': ['TASK_003'],
                'deliverables': ['واجهة ويب', 'API للإدارة', 'قاعدة بيانات النماذج']
            },
            {
                'id': 'TASK_007',
                'title': 'اختبار الأداء والتحسين',
                'description': 'اختبار أداء النظام وتحسين سرعة الرفع والتحميل',
                'priority': 'medium',
                'estimated_hours': 2,
                'required_skills': ['اختبار الأداء', 'تحسين الشبكات', 'التحليل'],
                'dependencies': ['TASK_003', 'TASK_006'],
                'deliverables': ['تقرير الأداء', 'تحسينات السرعة', 'معايير الجودة']
            },
            {
                'id': 'TASK_008',
                'title': 'توثيق النظام وإنشاء الأدلة',
                'description': 'إنشاء توثيق شامل وأدلة المستخدم للنظام',
                'priority': 'medium',
                'estimated_hours': 3,
                'required_skills': ['الكتابة التقنية', 'التوثيق', 'إنشاء الأدلة'],
                'dependencies': ['TASK_006'],
                'deliverables': ['دليل المستخدم', 'توثيق API', 'أدلة الصيانة']
            },
            {
                'id': 'TASK_009',
                'title': 'تطوير نظام التكلفة والفوترة',
                'description': 'إنشاء نظام لتتبع التكاليف وتحسين الاستخدام',
                'priority': 'low',
                'estimated_hours': 2,
                'required_skills': ['تحليل التكاليف', 'Google Cloud Billing', 'التقارير'],
                'dependencies': ['TASK_004'],
                'deliverables': ['تتبع التكاليف', 'تقارير الاستخدام', 'تحسين التكلفة']
            },
            {
                'id': 'TASK_010',
                'title': 'إنشاء نظام النشر والتحديث',
                'description': 'تطوير نظام لنشر التحديثات وإدارة الإصدارات',
                'priority': 'low',
                'estimated_hours': 3,
                'required_skills': ['CI/CD', 'إدارة الإصدارات', 'النشر التلقائي'],
                'dependencies': ['TASK_007'],
                'deliverables': ['نظام CI/CD', 'إدارة الإصدارات', 'النشر التلقائي']
            }
        ]
        
        self.tasks = tasks
        return tasks
        
    def calculate_assistant_suitability(self, task, assistant_id):
        """حساب مدى ملاءمة المساعد للمهمة"""
        assistant = self.assistants[assistant_id]
        
        if not assistant['availability']:
            return 0
            
        if assistant['current_tasks'] >= assistant['max_concurrent_tasks']:
            return 0
            
        # حساب التطابق في المهارات
        skill_match = 0
        for skill in task['required_skills']:
            for specialty in assistant['specialties']:
                if skill.lower() in specialty.lower():
                    skill_match += 1
            for capability in assistant['capabilities']:
                if skill.lower() in capability.lower():
                    skill_match += 1
                    
        # حساب النقاط بناءً على الأولوية والمهارات
        priority_weight = {'high': 3, 'medium': 2, 'low': 1}
        priority_score = priority_weight.get(task['priority'], 1)
        
        suitability = (skill_match * 10) + (priority_score * 5)
        
        # تقليل النقاط بناءً على العبء الحالي
        load_factor = 1 - (assistant['current_tasks'] / assistant['max_concurrent_tasks'])
        suitability *= load_factor
        
        return suitability
        
    def assign_tasks(self):
        """توزيع المهام على المساعدين"""
        self.logger.info("🎯 بدء توزيع المهام على المساعدين...")
        
        # ترتيب المهام حسب الأولوية والتبعيات
        sorted_tasks = self.sort_tasks_by_priority()
        
        for task in sorted_tasks:
            best_assistant = None
            best_score = 0
            
            # البحث عن أفضل مساعد للمهمة
            for assistant_id in self.assistants:
                score = self.calculate_assistant_suitability(task, assistant_id)
                if score > best_score:
                    best_score = score
                    best_assistant = assistant_id
                    
            if best_assistant:
                # تعيين المهمة للمساعد
                if best_assistant not in self.assignments:
                    self.assignments[best_assistant] = []
                    
                self.assignments[best_assistant].append(task)
                self.assistants[best_assistant]['current_tasks'] += 1
                
                self.logger.info(f"✅ تم تعيين {task['title']} إلى {self.assistants[best_assistant]['name']}")
            else:
                self.logger.warning(f"⚠️ لم يتم العثور على مساعد مناسب لـ {task['title']}")
                
    def sort_tasks_by_priority(self):
        """ترتيب المهام حسب الأولوية والتبعيات"""
        priority_order = {'high': 3, 'medium': 2, 'low': 1}
        
        # ترتيب أولي حسب الأولوية
        sorted_tasks = sorted(
            self.tasks, 
            key=lambda x: priority_order.get(x['priority'], 0), 
            reverse=True
        )
        
        # إعادة ترتيب بناءً على التبعيات
        final_order = []
        remaining_tasks = sorted_tasks.copy()
        
        while remaining_tasks:
            for task in remaining_tasks[:]:
                # التحقق من أن جميع التبعيات تم إنجازها
                dependencies_met = all(
                    any(completed_task['id'] == dep for completed_task in final_order)
                    for dep in task['dependencies']
                )
                
                if dependencies_met or not task['dependencies']:
                    final_order.append(task)
                    remaining_tasks.remove(task)
                    
            # تجنب الحلقة اللانهائية
            if len(remaining_tasks) == len([t for t in remaining_tasks]):
                # إضافة المهام المتبقية حتى لو لم تكتمل التبعيات
                final_order.extend(remaining_tasks)
                break
                
        return final_order
        
    def generate_assignment_report(self):
        """توليد تقرير توزيع المهام"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = Path(f"task_assignment_report_{timestamp}.json")
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'total_tasks': len(self.tasks),
            'assigned_tasks': sum(len(tasks) for tasks in self.assignments.values()),
            'unassigned_tasks': len(self.tasks) - sum(len(tasks) for tasks in self.assignments.values()),
            'assistants': self.assistants,
            'assignments': self.assignments,
            'summary': self.generate_summary()
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
            
        self.logger.info(f"📄 تم حفظ تقرير التوزيع: {report_file}")
        return report_file
        
    def generate_summary(self):
        """توليد ملخص التوزيع"""
        summary = {}
        
        for assistant_id, tasks in self.assignments.items():
            assistant_name = self.assistants[assistant_id]['name']
            total_hours = sum(task['estimated_hours'] for task in tasks)
            
            summary[assistant_id] = {
                'name': assistant_name,
                'assigned_tasks': len(tasks),
                'total_estimated_hours': total_hours,
                'task_titles': [task['title'] for task in tasks]
            }
            
        return summary
        
    def print_assignment_summary(self):
        """طباعة ملخص التوزيع"""
        print("\n" + "=" * 80)
        print("📋 ملخص توزيع مهام مشروع تخزين Ollama على Google Cloud")
        print("=" * 80)
        
        total_tasks = len(self.tasks)
        assigned_tasks = sum(len(tasks) for tasks in self.assignments.values())
        
        print(f"📊 إجمالي المهام: {total_tasks}")
        print(f"✅ المهام المُعينة: {assigned_tasks}")
        print(f"⏳ المهام المعلقة: {total_tasks - assigned_tasks}")
        
        print("\n🤖 توزيع المهام على المساعدين:")
        print("-" * 80)
        
        for assistant_id, tasks in self.assignments.items():
            assistant = self.assistants[assistant_id]
            total_hours = sum(task['estimated_hours'] for task in tasks)
            
            print(f"\n👤 {assistant['name']}")
            print(f"   📋 عدد المهام: {len(tasks)}")
            print(f"   ⏱️ إجمالي الساعات: {total_hours}")
            print(f"   🎯 التخصصات: {', '.join(assistant['specialties'])}")
            
            for i, task in enumerate(tasks, 1):
                priority_emoji = {'high': '🔴', 'medium': '🟡', 'low': '🟢'}
                emoji = priority_emoji.get(task['priority'], '⚪')
                print(f"   {i}. {emoji} {task['title']} ({task['estimated_hours']}ساعة)")
                
        print("\n" + "=" * 80)
        
    def run_task_distribution(self):
        """تشغيل نظام توزيع المهام"""
        print("🏺 نظام توزيع المهام على المساعدين الذكيين")
        print("=" * 70)
        
        # تعريف مهام المشروع
        self.define_ollama_cloud_tasks()
        self.logger.info(f"📋 تم تعريف {len(self.tasks)} مهمة")
        
        # توزيع المهام
        self.assign_tasks()
        
        # توليد التقرير
        self.generate_assignment_report()
        
        # طباعة الملخص
        self.print_assignment_summary()
        
        self.logger.info("✅ تم إكمال توزيع المهام بنجاح!")
        
        return self.assignments

def main():
    """الدالة الرئيسية"""
    distributor = AIAssistantsTaskDistributor()
    assignments = distributor.run_task_distribution()
    
    print("\n🎉 تم توزيع المهام بنجاح!")
    print("\n📋 الخطوات التالية:")
    print("1. مراجعة تقرير التوزيع المحفوظ")
    print("2. التواصل مع كل مساعد لبدء المهام المُعينة")
    print("3. متابعة التقدم وتحديث الحالة")

if __name__ == "__main__":
    main()
