version: '3.8'

services:
  n8n:
    build:
      context: .
      dockerfile: Dockerfile.n8n
    container_name: n8n-automation
    ports:
      - "5678:5678"
    environment:
      - N8N_HOST=0.0.0.0
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - NODE_ENV=production
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=anubis_n8n_2025
      - WEBHOOK_URL=https://n8n-automation-dot-universal-ai-assistants-2025.uc.r.appspot.com/
      - N8N_EDITOR_BASE_URL=https://n8n-automation-dot-universal-ai-assistants-2025.uc.r.appspot.com/
    volumes:
      - n8n_data:/home/<USER>/.n8n
    restart: unless-stopped
    networks:
      - n8n_network

volumes:
  n8n_data:

networks:
  n8n_network:
    driver: bridge
