#!/usr/bin/env python3
"""
🔄 N8N Cloud Setup - إعداد n8n في Google Cloud
إعداد وتشغيل n8n للأتمتة والتكامل
"""

import os
import subprocess
import json
import time
from datetime import datetime
from pathlib import Path

class N8NCloudSetup:
    def __init__(self):
        self.project_id = "universal-ai-assistants-2025"
        self.service_name = "n8n-automation"
        self.region = "us-central1"
        self.port = 5678
        self.setup_log = []
        
    def log_message(self, message, level="INFO"):
        """تسجيل الرسائل مع الوقت"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}"
        print(log_entry)
        self.setup_log.append(log_entry)
    
    def check_prerequisites(self):
        """فحص المتطلبات الأساسية"""
        self.log_message("🔍 فحص متطلبات n8n...")
        
        # فحص Google Cloud CLI
        try:
            result = subprocess.run("gcloud --version", shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                self.log_message("✅ Google Cloud CLI متوفر")
            else:
                self.log_message("❌ Google Cloud CLI غير متوفر", "ERROR")
                return False
        except Exception as e:
            self.log_message(f"❌ خطأ في فحص Google Cloud CLI: {e}", "ERROR")
            return False
        
        # فحص Docker
        try:
            result = subprocess.run("docker --version", shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                self.log_message("✅ Docker متوفر")
            else:
                self.log_message("❌ Docker غير متوفر", "ERROR")
                return False
        except Exception as e:
            self.log_message(f"❌ خطأ في فحص Docker: {e}", "ERROR")
            return False
        
        return True
    
    def create_n8n_dockerfile(self):
        """إنشاء Dockerfile لـ n8n"""
        self.log_message("📝 إنشاء Dockerfile لـ n8n...")
        
        dockerfile_content = """FROM n8nio/n8n:latest

# تثبيت المتطلبات الإضافية
USER root
RUN apk add --no-cache \
    python3 \
    py3-pip \
    curl \
    git \
    bash

# إعداد متغيرات البيئة
ENV N8N_HOST=0.0.0.0
ENV N8N_PORT=5678
ENV N8N_PROTOCOL=http
ENV NODE_ENV=production
ENV WEBHOOK_URL=https://n8n-automation-dot-universal-ai-assistants-2025.uc.r.appspot.com/

# إنشاء مجلد البيانات
RUN mkdir -p /home/<USER>/.n8n
RUN chown -R node:node /home/<USER>/.n8n

# العودة إلى مستخدم node
USER node

# تعيين مجلد العمل
WORKDIR /home/<USER>

# تشغيل n8n
CMD ["n8n", "start"]
"""
        
        with open("Dockerfile.n8n", "w", encoding="utf-8") as f:
            f.write(dockerfile_content)
        
        self.log_message("✅ تم إنشاء Dockerfile.n8n")
        return True
    
    def create_n8n_config(self):
        """إنشاء ملفات التكوين لـ n8n"""
        self.log_message("⚙️ إنشاء ملفات تكوين n8n...")
        
        # إنشاء مجلد التكوين
        config_dir = Path("n8n_config")
        config_dir.mkdir(exist_ok=True)
        
        # ملف docker-compose لـ n8n
        docker_compose_content = """version: '3.8'

services:
  n8n:
    build:
      context: .
      dockerfile: Dockerfile.n8n
    container_name: n8n-automation
    ports:
      - "5678:5678"
    environment:
      - N8N_HOST=0.0.0.0
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - NODE_ENV=production
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=anubis_n8n_2025
      - WEBHOOK_URL=https://n8n-automation-dot-universal-ai-assistants-2025.uc.r.appspot.com/
      - N8N_EDITOR_BASE_URL=https://n8n-automation-dot-universal-ai-assistants-2025.uc.r.appspot.com/
    volumes:
      - n8n_data:/home/<USER>/.n8n
    restart: unless-stopped
    networks:
      - n8n_network

volumes:
  n8n_data:

networks:
  n8n_network:
    driver: bridge
"""
        
        with open("docker-compose.n8n.yml", "w", encoding="utf-8") as f:
            f.write(docker_compose_content)
        
        # ملف app.yaml لـ Google App Engine
        app_yaml_content = """runtime: custom
env: flex

service: n8n-automation

automatic_scaling:
  min_num_instances: 1
  max_num_instances: 3
  cool_down_period: 120s
  cpu_utilization:
    target_utilization: 0.6

resources:
  cpu: 1
  memory_gb: 2
  disk_size_gb: 10

network:
  forwarded_ports:
    - 5678

env_variables:
  N8N_HOST: "0.0.0.0"
  N8N_PORT: "5678"
  N8N_PROTOCOL: "https"
  NODE_ENV: "production"
  N8N_BASIC_AUTH_ACTIVE: "true"
  N8N_BASIC_AUTH_USER: "admin"
  N8N_BASIC_AUTH_PASSWORD: "anubis_n8n_2025"
"""
        
        with open("app.n8n.yaml", "w", encoding="utf-8") as f:
            f.write(app_yaml_content)
        
        self.log_message("✅ تم إنشاء ملفات التكوين")
        return True
    
    def create_n8n_workflows(self):
        """إنشاء workflows أساسية لـ n8n"""
        self.log_message("🔄 إنشاء workflows أساسية...")
        
        # إنشاء مجلد workflows
        workflows_dir = Path("n8n_workflows")
        workflows_dir.mkdir(exist_ok=True)
        
        # Workflow لمراقبة النماذج
        models_monitoring_workflow = {
            "name": "Models Monitoring",
            "nodes": [
                {
                    "parameters": {
                        "rule": {
                            "interval": [{"field": "hours", "value": 1}]
                        }
                    },
                    "name": "Schedule Trigger",
                    "type": "n8n-nodes-base.scheduleTrigger",
                    "typeVersion": 1,
                    "position": [250, 300]
                },
                {
                    "parameters": {
                        "command": "gsutil ls -l gs://universal-ai-models-2025-storage/"
                    },
                    "name": "Check Models",
                    "type": "n8n-nodes-base.executeCommand",
                    "typeVersion": 1,
                    "position": [450, 300]
                },
                {
                    "parameters": {
                        "url": "https://universal-ai-assistants-2025.uc.r.appspot.com/api/models/status",
                        "options": {}
                    },
                    "name": "Update Status",
                    "type": "n8n-nodes-base.httpRequest",
                    "typeVersion": 1,
                    "position": [650, 300]
                }
            ],
            "connections": {
                "Schedule Trigger": {
                    "main": [
                        [
                            {
                                "node": "Check Models",
                                "type": "main",
                                "index": 0
                            }
                        ]
                    ]
                },
                "Check Models": {
                    "main": [
                        [
                            {
                                "node": "Update Status",
                                "type": "main",
                                "index": 0
                            }
                        ]
                    ]
                }
            }
        }
        
        with open(workflows_dir / "models_monitoring.json", "w", encoding="utf-8") as f:
            json.dump(models_monitoring_workflow, f, indent=2)
        
        # Workflow لنشر النماذج
        deployment_workflow = {
            "name": "Models Deployment",
            "nodes": [
                {
                    "parameters": {},
                    "name": "Webhook",
                    "type": "n8n-nodes-base.webhook",
                    "typeVersion": 1,
                    "position": [250, 300],
                    "webhookId": "deploy-models"
                },
                {
                    "parameters": {
                        "command": "python enhanced_models_uploader.py"
                    },
                    "name": "Upload Models",
                    "type": "n8n-nodes-base.executeCommand",
                    "typeVersion": 1,
                    "position": [450, 300]
                },
                {
                    "parameters": {
                        "command": "gcloud app deploy --quiet"
                    },
                    "name": "Deploy App",
                    "type": "n8n-nodes-base.executeCommand",
                    "typeVersion": 1,
                    "position": [650, 300]
                }
            ],
            "connections": {
                "Webhook": {
                    "main": [
                        [
                            {
                                "node": "Upload Models",
                                "type": "main",
                                "index": 0
                            }
                        ]
                    ]
                },
                "Upload Models": {
                    "main": [
                        [
                            {
                                "node": "Deploy App",
                                "type": "main",
                                "index": 0
                            }
                        ]
                    ]
                }
            }
        }
        
        with open(workflows_dir / "deployment_workflow.json", "w", encoding="utf-8") as f:
            json.dump(deployment_workflow, f, indent=2)
        
        self.log_message("✅ تم إنشاء workflows أساسية")
        return True
    
    def deploy_n8n_local(self):
        """نشر n8n محلياً باستخدام Docker"""
        self.log_message("🐳 نشر n8n محلياً...")
        
        try:
            # بناء وتشغيل n8n
            cmd = "docker-compose -f docker-compose.n8n.yml up -d"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.log_message("✅ تم تشغيل n8n محلياً بنجاح")
                self.log_message(f"🌐 الوصول عبر: http://localhost:{self.port}")
                self.log_message("👤 المستخدم: admin")
                self.log_message("🔑 كلمة المرور: anubis_n8n_2025")
                return True
            else:
                self.log_message(f"❌ فشل تشغيل n8n: {result.stderr}", "ERROR")
                return False
                
        except Exception as e:
            self.log_message(f"❌ خطأ في تشغيل n8n: {e}", "ERROR")
            return False
    
    def deploy_n8n_cloud(self):
        """نشر n8n في Google Cloud"""
        self.log_message("☁️ نشر n8n في Google Cloud...")
        
        try:
            # نشر في App Engine
            cmd = f"gcloud app deploy app.n8n.yaml --project={self.project_id} --quiet"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.log_message("✅ تم نشر n8n في Google Cloud بنجاح")
                cloud_url = f"https://{self.service_name}-dot-{self.project_id}.uc.r.appspot.com"
                self.log_message(f"🌐 الوصول عبر: {cloud_url}")
                return True
            else:
                self.log_message(f"❌ فشل نشر n8n في Cloud: {result.stderr}", "ERROR")
                return False
                
        except Exception as e:
            self.log_message(f"❌ خطأ في نشر n8n: {e}", "ERROR")
            return False
    
    def setup_complete(self):
        """إعداد n8n الكامل"""
        start_time = time.time()
        self.log_message("🚀 بدء إعداد n8n للأتمتة")
        self.log_message("=" * 60)
        
        # فحص المتطلبات
        if not self.check_prerequisites():
            self.log_message("❌ فشل في فحص المتطلبات", "ERROR")
            return False
        
        # إنشاء الملفات
        if not self.create_n8n_dockerfile():
            return False
        
        if not self.create_n8n_config():
            return False
        
        if not self.create_n8n_workflows():
            return False
        
        # نشر محلي
        local_success = self.deploy_n8n_local()
        
        # نشر سحابي (اختياري)
        cloud_success = False
        try:
            cloud_success = self.deploy_n8n_cloud()
        except Exception as e:
            self.log_message(f"⚠️ تخطي النشر السحابي: {e}", "WARNING")
        
        # ملخص النتائج
        end_time = time.time()
        duration = end_time - start_time
        
        self.log_message("\n" + "=" * 60)
        self.log_message("📊 ملخص إعداد n8n")
        self.log_message("=" * 60)
        self.log_message(f"⏱️ المدة: {duration/60:.1f} دقيقة")
        self.log_message(f"🏠 النشر المحلي: {'✅ نجح' if local_success else '❌ فشل'}")
        self.log_message(f"☁️ النشر السحابي: {'✅ نجح' if cloud_success else '⚠️ تخطي'}")
        
        if local_success:
            self.log_message(f"🌐 الوصول المحلي: http://localhost:{self.port}")
            self.log_message("👤 المستخدم: admin | 🔑 كلمة المرور: anubis_n8n_2025")
        
        # حفظ سجل الإعداد
        log_file = f"n8n_setup_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(log_file, "w", encoding="utf-8") as f:
            f.write("\n".join(self.setup_log))
        
        self.log_message(f"📄 تم حفظ سجل الإعداد في: {log_file}")
        
        return local_success or cloud_success

if __name__ == "__main__":
    n8n_setup = N8NCloudSetup()
    success = n8n_setup.setup_complete()
    
    if success:
        print("\n🎉 تم إعداد n8n بنجاح!")
        print("🔄 يمكنك الآن إنشاء workflows للأتمتة")
    else:
        print("\n❌ فشل إعداد n8n!")
