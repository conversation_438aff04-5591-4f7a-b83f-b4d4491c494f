#!/bin/bash

# تحديث النظام
sudo apt-get update && sudo apt-get install -y docker.io curl

# تشغيل Docker
sudo systemctl start docker
sudo systemctl enable docker

# إضافة المستخدم إلى مجموعة docker
sudo usermod -aG docker $USER

# تشغيل n8n
sudo docker run -d --name n8n --restart unless-stopped \
    -p 5678:5678 \
    -e N8N_BASIC_AUTH_ACTIVE=true \
    -e N8N_BASIC_AUTH_USER=admin \
    -e N8N_BASIC_AUTH_PASSWORD=anubis123 \
    -e N8N_HOST=0.0.0.0 \
    -e N8N_PORT=5678 \
    -e N8N_PROTOCOL=http \
    -v n8n_data:/home/<USER>/.n8n \
    n8nio/n8n

# تثبيت Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# تشغيل Ollama كخدمة
sudo systemctl start ollama
sudo systemctl enable ollama

# انتظار حتى يصبح Ollama جاهز
sleep 30

# تحميل النماذج المحلية
ollama pull phi3:mini
ollama pull mistral:7b

# تشغيل Redis للتخزين المؤقت
sudo docker run -d --name redis --restart unless-stopped \
    -p 6379:6379 \
    redis:7-alpine redis-server --requirepass anubis_redis_2024

# إنشاء ملف لتأكيد اكتمال الإعداد
echo "VM setup completed at $(date)" > /tmp/setup_complete.log

# تسجيل معلومات الخدمات
echo "n8n: http://$(curl -s ifconfig.me):5678" >> /tmp/setup_complete.log
echo "Ollama: http://$(curl -s ifconfig.me):11434" >> /tmp/setup_complete.log
echo "Redis: $(curl -s ifconfig.me):6379" >> /tmp/setup_complete.log
