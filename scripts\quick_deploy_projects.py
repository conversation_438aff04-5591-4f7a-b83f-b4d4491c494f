#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import subprocess
import time
import os

class QuickProjectDeploy:
    def __init__(self):
        self.vm_ip = "**************"
        self.vm_name = "anubis-n8n-ollama-vm"
        self.zone = "us-central1-a"
    
    def create_docker_compose(self):
        """إنشاء docker-compose للمشاريع"""
        print("🐳 إنشاء Docker Compose للمشاريع...")
        
        docker_compose = """version: '3.8'

services:
  anubis-system:
    image: python:3.11-slim
    container_name: anubis-system
    ports:
      - "8000:8000"
    working_dir: /app
    command: >
      bash -c "
        pip install fastapi uvicorn sqlalchemy mysql-connector-python redis &&
        python -c \"
from fastapi import FastAPI
import uvicorn

app = FastAPI(title='ANUBIS SYSTEM', description='نظام أنوبيس للذكاء الاصطناعي')

@app.get('/')
def read_root():
    return {
        'message': 'مرحباً بك في نظام أنوبيس',
        'status': 'يعمل بنجاح',
        'services': ['AI Models', 'Database', 'API'],
        'version': '2.0'
    }

@app.get('/status')
def get_status():
    return {
        'system': 'ANUBIS_SYSTEM',
        'status': 'running',
        'models': ['phi3:mini', 'mistral:7b', 'llama3:8b'],
        'database': 'MySQL Connected',
        'cache': 'Redis Active'
    }

@app.get('/models')
def get_models():
    return {
        'local_models': [
            {'name': 'phi3:mini', 'status': 'available', 'size': '2.2GB'},
            {'name': 'mistral:7b', 'status': 'available', 'size': '4.1GB'},
            {'name': 'llama3:8b', 'status': 'available', 'size': '4.7GB'}
        ],
        'cloud_models': [
            {'name': 'gemini-pro', 'status': 'available'},
            {'name': 'gpt-4', 'status': 'available'},
            {'name': 'claude-3', 'status': 'available'}
        ]
    }

if __name__ == '__main__':
    uvicorn.run(app, host='0.0.0.0', port=8000)
        \"
      "
    restart: unless-stopped
    networks:
      - anubis-network

  horus-team:
    image: python:3.11-slim
    container_name: horus-team
    ports:
      - "7000:7000"
    working_dir: /app
    command: >
      bash -c "
        pip install fastapi uvicorn requests &&
        python -c \"
from fastapi import FastAPI
import uvicorn

app = FastAPI(title='HORUS AI TEAM', description='فريق حورس للذكاء الاصطناعي')

agents = [
    {'name': 'THOTH', 'role': 'المحلل السريع', 'model': 'phi3:mini', 'status': 'active'},
    {'name': 'PTAH', 'role': 'المطور الخبير', 'model': 'mistral:7b', 'status': 'active'},
    {'name': 'RA', 'role': 'المستشار الاستراتيجي', 'model': 'llama3:8b', 'status': 'active'},
    {'name': 'ANUBIS', 'role': 'حارس الأمان', 'model': 'claude-3', 'status': 'active'},
    {'name': 'MAAT', 'role': 'حارسة العدالة', 'model': 'gpt-4', 'status': 'active'},
    {'name': 'HAPI', 'role': 'محلل البيانات', 'model': 'gemini-pro', 'status': 'active'},
    {'name': 'SESHAT', 'role': 'المحللة البصرية', 'model': 'qwen2.5-vl', 'status': 'active'},
    {'name': 'HORUS', 'role': 'المنسق الأعلى', 'model': 'gemini-pro', 'status': 'active'}
]

@app.get('/')
def read_root():
    return {
        'message': 'مرحباً بك في فريق حورس',
        'team_size': len(agents),
        'status': 'جميع الوكلاء نشطين',
        'capabilities': ['تحليل', 'برمجة', 'استراتيجية', 'أمان', 'عدالة', 'بيانات', 'رؤية', 'تنسيق']
    }

@app.get('/agents')
def get_agents():
    return {'agents': agents}

@app.get('/agents/{agent_name}')
def get_agent(agent_name: str):
    agent = next((a for a in agents if a['name'].upper() == agent_name.upper()), None)
    if agent:
        return agent
    return {'error': 'الوكيل غير موجود'}

@app.post('/ask/{agent_name}')
def ask_agent(agent_name: str, question: dict):
    agent = next((a for a in agents if a['name'].upper() == agent_name.upper()), None)
    if agent:
        return {
            'agent': agent['name'],
            'role': agent['role'],
            'question': question.get('text', ''),
            'answer': f'إجابة من {agent[\"name\"]}: هذا مثال على الإجابة. النظام يعمل بنجاح!',
            'model_used': agent['model']
        }
    return {'error': 'الوكيل غير موجود'}

if __name__ == '__main__':
    uvicorn.run(app, host='0.0.0.0', port=7000)
        \"
      "
    restart: unless-stopped
    networks:
      - anubis-network

  anubis-mcp:
    image: node:18-slim
    container_name: anubis-mcp
    ports:
      - "3000:3000"
    working_dir: /app
    command: >
      bash -c "
        npm init -y &&
        npm install express cors &&
        node -e \"
const express = require('express');
const cors = require('cors');
const app = express();

app.use(cors());
app.use(express.json());

const mcpData = {
  name: 'ANUBIS_HORUS_MCP',
  description: 'نظام التكامل بين النماذج',
  version: '1.0.0',
  protocols: ['MCP', 'REST', 'WebSocket'],
  models: {
    local: ['phi3:mini', 'mistral:7b', 'llama3:8b'],
    cloud: ['gemini-pro', 'gpt-4', 'claude-3']
  },
  tools: [
    'API Key Management',
    'Model Router', 
    'Context Manager',
    'Response Aggregator'
  ]
};

app.get('/', (req, res) => {
  res.json({
    message: 'مرحباً بك في نظام MCP',
    system: mcpData.name,
    status: 'يعمل بنجاح',
    endpoints: ['/models', '/tools', '/status']
  });
});

app.get('/models', (req, res) => {
  res.json(mcpData.models);
});

app.get('/tools', (req, res) => {
  res.json(mcpData.tools);
});

app.get('/status', (req, res) => {
  res.json({
    system: mcpData.name,
    version: mcpData.version,
    status: 'running',
    uptime: process.uptime(),
    protocols: mcpData.protocols
  });
});

app.listen(3000, '0.0.0.0', () => {
  console.log('ANUBIS_HORUS_MCP running on port 3000');
});
        \"
      "
    restart: unless-stopped
    networks:
      - anubis-network

  nginx-proxy:
    image: nginx:alpine
    container_name: nginx-proxy
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - anubis-system
      - horus-team
      - anubis-mcp
    restart: unless-stopped
    networks:
      - anubis-network

networks:
  anubis-network:
    driver: bridge
"""
        
        with open('docker-compose-projects.yml', 'w', encoding='utf-8') as f:
            f.write(docker_compose)
        
        print("   ✅ تم إنشاء docker-compose")
    
    def create_nginx_config(self):
        """إنشاء تكوين nginx"""
        print("🌐 إنشاء تكوين nginx...")
        
        nginx_conf = """events {
    worker_connections 1024;
}

http {
    upstream anubis {
        server anubis-system:8000;
    }
    
    upstream horus {
        server horus-team:7000;
    }
    
    upstream mcp {
        server anubis-mcp:3000;
    }
    
    server {
        listen 80;
        server_name _;
        
        location / {
            return 200 '🏺 مرحباً بك في نظام أنوبيس\\n\\n📍 المشاريع المتاحة:\\n• /anubis - نظام أنوبيس الأساسي\\n• /horus - فريق حورس للذكاء الاصطناعي\\n• /mcp - نظام التكامل بين النماذج\\n• /n8n - منصة الأتمتة\\n\\n🚀 جميع الأنظمة تعمل بنجاح!';
            add_header Content-Type text/plain;
        }
        
        location /anubis/ {
            proxy_pass http://anubis/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
        
        location /horus/ {
            proxy_pass http://horus/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
        
        location /mcp/ {
            proxy_pass http://mcp/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
        
        location /n8n/ {
            proxy_pass http://localhost:5678/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }
}"""
        
        with open('nginx.conf', 'w', encoding='utf-8') as f:
            f.write(nginx_conf)
        
        print("   ✅ تم إنشاء تكوين nginx")
    
    def deploy_to_vm(self):
        """نشر المشاريع إلى VM"""
        print("🚀 نشر المشاريع إلى VM...")
        
        try:
            # رفع الملفات
            files = ['docker-compose-projects.yml', 'nginx.conf']
            for file in files:
                subprocess.run([
                    'gcloud', 'compute', 'scp', file,
                    f'{self.vm_name}:~/', '--zone', self.zone
                ], check=True)
            
            # تشغيل المشاريع
            commands = [
                "sudo docker-compose -f docker-compose-projects.yml down || true",
                "sudo docker-compose -f docker-compose-projects.yml up -d",
                "sudo docker ps"
            ]
            
            for cmd in commands:
                print(f"🔄 تنفيذ: {cmd}")
                result = subprocess.run([
                    'gcloud', 'compute', 'ssh', self.vm_name,
                    '--zone', self.zone, '--command', cmd
                ], capture_output=True, text=True, timeout=120)
                
                if result.returncode == 0:
                    print(f"   ✅ نجح")
                    if result.stdout:
                        print(f"   📄 {result.stdout[:200]}")
                else:
                    print(f"   ⚠️ {result.stderr[:200]}")
                
                time.sleep(3)
            
            print("   ✅ تم نشر المشاريع")
            
        except Exception as e:
            print(f"   ❌ خطأ في النشر: {e}")
    
    def open_ports(self):
        """فتح المنافذ المطلوبة"""
        print("🔥 فتح المنافذ...")
        
        ports = ["80", "8000", "7000", "3000"]
        for port in ports:
            try:
                subprocess.run([
                    'gcloud', 'compute', 'firewall-rules', 'create',
                    f'allow-port-{port}',
                    '--allow', f'tcp:{port}',
                    '--source-ranges', '0.0.0.0/0'
                ], check=True)
                print(f"   ✅ فتح منفذ {port}")
            except:
                print(f"   ⚠️ منفذ {port} مفتوح مسبقاً")
    
    def quick_deploy(self):
        """نشر سريع للمشاريع"""
        print("🚀 بدء النشر السريع للمشاريع...")
        print("=" * 50)
        
        self.create_docker_compose()
        self.create_nginx_config()
        self.open_ports()
        self.deploy_to_vm()
        
        print("\n" + "=" * 50)
        print("🎉 تم النشر السريع!")
        print(f"🌐 المشاريع متاحة على:")
        print(f"   • الصفحة الرئيسية: http://{self.vm_ip}")
        print(f"   • نظام أنوبيس: http://{self.vm_ip}/anubis")
        print(f"   • فريق حورس: http://{self.vm_ip}/horus")
        print(f"   • نظام MCP: http://{self.vm_ip}/mcp")
        print(f"   • n8n: http://{self.vm_ip}/n8n")

if __name__ == "__main__":
    deployer = QuickProjectDeploy()
    deployer.quick_deploy()
