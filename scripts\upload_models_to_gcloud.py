#!/usr/bin/env python3
"""
🚀 Universal AI Assistants - Models Upload to Google Cloud
رفع نماذج Ollama إلى Google Cloud Storage
"""

import os
import subprocess
import json
from pathlib import Path

class ModelsUploader:
    def __init__(self):
        self.project_id = "universal-ai-assistants-2025"
        self.bucket_name = "universal-ai-models-2025-storage"
        self.ollama_models_path = Path.home() / ".ollama" / "models"
        
    def create_storage_bucket(self):
        """إنشاء bucket لتخزين النماذج"""
        print("🪣 إنشاء Google Cloud Storage bucket...")
        
        try:
            # إنشاء bucket
            cmd = f"gsutil mb -p {self.project_id} gs://{self.bucket_name}"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ تم إنشاء bucket: gs://{self.bucket_name}")
            else:
                print(f"ℹ️ Bucket موجود مسبقاً أو خطأ: {result.stderr}")
                
        except Exception as e:
            print(f"❌ خطأ في إنشاء bucket: {e}")
    
    def get_ollama_models(self):
        """الحصول على قائمة نماذج Ollama"""
        print("📋 جمع معلومات نماذج Ollama...")
        
        try:
            result = subprocess.run("ollama list", shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]  # تجاهل header
                models = []
                
                for line in lines:
                    if line.strip():
                        parts = line.split()
                        if len(parts) >= 3:
                            model_name = parts[0]
                            model_id = parts[1] if len(parts) > 1 else ""
                            size = parts[2] if len(parts) > 2 else ""
                            models.append({
                                'name': model_name,
                                'id': model_id,
                                'size': size
                            })
                
                print(f"✅ تم العثور على {len(models)} نموذج")
                return models
            else:
                print(f"❌ خطأ في الحصول على قائمة النماذج: {result.stderr}")
                return []
                
        except Exception as e:
            print(f"❌ خطأ: {e}")
            return []
    
    def upload_models_metadata(self, models):
        """رفع metadata للنماذج"""
        print("📤 رفع metadata للنماذج...")
        
        metadata = {
            "project": "Universal AI Assistants",
            "upload_date": subprocess.run("date", shell=True, capture_output=True, text=True).stdout.strip(),
            "models": models,
            "total_models": len(models),
            "storage_location": f"gs://{self.bucket_name}/ollama/"
        }
        
        # حفظ metadata محلياً
        with open("models_metadata.json", "w", encoding="utf-8") as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)
        
        # رفع metadata إلى Cloud Storage
        try:
            cmd = f"gsutil cp models_metadata.json gs://{self.bucket_name}/metadata/"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ تم رفع metadata بنجاح")
            else:
                print(f"❌ خطأ في رفع metadata: {result.stderr}")
                
        except Exception as e:
            print(f"❌ خطأ: {e}")
    
    def upload_ollama_models(self):
        """رفع نماذج Ollama إلى Cloud Storage"""
        print("🚀 بدء رفع نماذج Ollama...")
        
        if not self.ollama_models_path.exists():
            print(f"❌ مجلد النماذج غير موجود: {self.ollama_models_path}")
            return
        
        try:
            # رفع جميع ملفات النماذج
            cmd = f"gsutil -m cp -r {self.ollama_models_path}/* gs://{self.bucket_name}/ollama/"
            print(f"📤 تنفيذ: {cmd}")
            
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ تم رفع النماذج بنجاح!")
                print(result.stdout)
            else:
                print(f"❌ خطأ في رفع النماذج: {result.stderr}")
                
        except Exception as e:
            print(f"❌ خطأ: {e}")
    
    def create_download_script(self):
        """إنشاء سكريبت لتحميل النماذج"""
        download_script = f"""#!/bin/bash
# 📥 سكريبت تحميل نماذج Universal AI Assistants

echo "🚀 تحميل نماذج Ollama من Google Cloud Storage..."

# إنشاء مجلد النماذج
mkdir -p ~/.ollama/models

# تحميل النماذج
gsutil -m cp -r gs://{self.bucket_name}/ollama/* ~/.ollama/models/

# تحميل metadata
gsutil cp gs://{self.bucket_name}/metadata/models_metadata.json ./

echo "✅ تم تحميل النماذج بنجاح!"
echo "📋 لعرض النماذج المتاحة: ollama list"
"""
        
        with open("download_models.sh", "w") as f:
            f.write(download_script)
        
        # جعل السكريبت قابل للتنفيذ
        os.chmod("download_models.sh", 0o755)
        
        # رفع السكريبت إلى Cloud Storage
        try:
            cmd = f"gsutil cp download_models.sh gs://{self.bucket_name}/scripts/"
            subprocess.run(cmd, shell=True)
            print("✅ تم إنشاء سكريبت التحميل")
        except Exception as e:
            print(f"❌ خطأ في رفع سكريبت التحميل: {e}")
    
    def run_upload(self):
        """تنفيذ عملية الرفع الكاملة"""
        print("🎯 بدء رفع نماذج Universal AI Assistants إلى Google Cloud")
        print("=" * 60)
        
        # 1. إنشاء bucket
        self.create_storage_bucket()
        
        # 2. الحصول على قائمة النماذج
        models = self.get_ollama_models()
        
        if not models:
            print("❌ لم يتم العثور على نماذج للرفع")
            return
        
        # 3. رفع metadata
        self.upload_models_metadata(models)
        
        # 4. رفع النماذج
        self.upload_ollama_models()
        
        # 5. إنشاء سكريبت التحميل
        self.create_download_script()
        
        print("\n🎉 تم إكمال عملية الرفع!")
        print(f"📍 موقع النماذج: gs://{self.bucket_name}/ollama/")
        print(f"📋 Metadata: gs://{self.bucket_name}/metadata/models_metadata.json")
        print(f"📥 سكريبت التحميل: gs://{self.bucket_name}/scripts/download_models.sh")

def main():
    uploader = ModelsUploader()
    uploader.run_upload()

if __name__ == "__main__":
    main()
