#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
⚡ نشر النهج المختلط لـ Universal AI Assistants على Google Cloud
==============================================================
"""

import os
import subprocess
import json
import time
from datetime import datetime

class HybridDeployer:
    def __init__(self):
        self.project_id = "anubis-467210"
        self.project_number = "726154816710"
        self.region = "us-central1"
        self.zone = "us-central1-a"
        self.github_repo = "https://github.com/amrashour1/universal-ai-assistants-agent.git"
        
    def main(self):
        print("⚡ نشر النهج المختلط - Universal AI Assistants")
        print("=" * 70)
        print(f"📅 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🆔 Project ID: {self.project_id}")
        print(f"🌍 Region: {self.region}")
        print("=" * 70)
        print("🎯 الاستراتيجية: نهج مختلط")
        print("   • نماذج أساسية على Ollama VM")
        print("   • نماذج متقدمة على Vertex AI")
        print("   • تكلفة متوقعة: $200-350/شهر")
        print("=" * 70)
        
        try:
            # التحقق من المتطلبات
            self.check_prerequisites()
            
            # تأكيد المتابعة
            confirm = input("\n🚀 هل تريد المتابعة مع النشر؟ (y/n): ").strip().lower()
            if confirm != 'y':
                print("❌ تم إلغاء النشر")
                return
            
            # بدء النشر
            self.deploy_hybrid_system()
            
        except Exception as e:
            print(f"❌ خطأ في النشر: {str(e)}")
    
    def check_prerequisites(self):
        """التحقق من المتطلبات الأساسية"""
        print("\n🔍 التحقق من المتطلبات...")
        
        # التحقق من gcloud
        try:
            result = subprocess.run(['gcloud', '--version'], 
                                  capture_output=True, text=True, shell=True)
            if result.returncode == 0:
                print("✅ Google Cloud SDK متوفر")
                print(f"   الإصدار: {result.stdout.split()[3]}")
            else:
                raise Exception("Google Cloud SDK غير متوفر")
        except Exception as e:
            print(f"❌ مشكلة في Google Cloud SDK: {str(e)}")
            raise
        
        # التحقق من المصادقة
        try:
            result = subprocess.run(['gcloud', 'auth', 'list', '--filter=status:ACTIVE'], 
                                  capture_output=True, text=True, shell=True)
            if "<EMAIL>" in result.stdout:
                print("✅ المصادقة متوفرة (<EMAIL>)")
            else:
                print("⚠️ يجب تسجيل الدخول أولاً...")
                subprocess.run(['gcloud', 'auth', 'login'], shell=True)
        except Exception as e:
            print(f"❌ مشكلة في المصادقة: {str(e)}")
            raise
        
        # تعيين المشروع
        try:
            subprocess.run(['gcloud', 'config', 'set', 'project', self.project_id], 
                         check=True, shell=True)
            print(f"✅ تم تعيين المشروع: {self.project_id}")
        except Exception as e:
            print(f"❌ لا يمكن الوصول للمشروع {self.project_id}: {str(e)}")
            raise
    
    def deploy_hybrid_system(self):
        """نشر النظام المختلط"""
        print("\n⚡ بدء نشر النظام المختلط...")
        
        # المرحلة 1: تفعيل APIs المطلوبة
        self.enable_required_apis()
        
        # المرحلة 2: إنشاء VM للنماذج الأساسية
        self.create_basic_ollama_vm()
        
        # المرحلة 3: إعداد Cloud SQL
        self.setup_cloud_sql()
        
        # المرحلة 4: إعداد Cloud Storage
        self.setup_cloud_storage()
        
        # المرحلة 5: تحضير الكود للنشر
        self.prepare_code_for_deployment()
        
        # المرحلة 6: نشر التطبيق على Cloud Run
        self.deploy_to_cloud_run()
        
        # المرحلة 7: تكوين الشبكة والأمان
        self.configure_networking()
        
        # المرحلة 8: اختبار النظام
        self.test_deployment()
        
        print("\n🎉 تم نشر النظام المختلط بنجاح!")
        self.show_deployment_summary()
    
    def enable_required_apis(self):
        """تفعيل APIs المطلوبة"""
        print("\n🔧 تفعيل APIs المطلوبة...")
        
        apis = [
            'compute.googleapis.com',
            'run.googleapis.com',
            'sqladmin.googleapis.com',
            'storage.googleapis.com',
            'aiplatform.googleapis.com',
            'cloudbuild.googleapis.com',
            'containerregistry.googleapis.com'
        ]
        
        for api in apis:
            try:
                print(f"   🔄 تفعيل {api}...")
                subprocess.run(['gcloud', 'services', 'enable', api], 
                             check=True, shell=True, capture_output=True)
                print(f"   ✅ تم تفعيل: {api}")
            except subprocess.CalledProcessError:
                print(f"   ⚠️ {api} مفعل مسبقاً أو حدث خطأ")
    
    def create_basic_ollama_vm(self):
        """إنشاء VM للنماذج الأساسية"""
        print("\n🖥️ إنشاء VM للنماذج الأساسية...")
        
        vm_name = "anubis-ollama-basic"
        
        startup_script = """#!/bin/bash
# تحديث النظام
apt-get update
apt-get install -y curl wget git

# تثبيت Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
systemctl start docker
systemctl enable docker

# تثبيت Ollama
curl -fsSL https://ollama.ai/install.sh | sh
systemctl start ollama
systemctl enable ollama

# انتظار تشغيل Ollama
sleep 30

# تحميل النماذج الأساسية
ollama pull phi3:mini
ollama pull mistral:7b

# إنشاء خدمة للتأكد من تشغيل Ollama
cat > /etc/systemd/system/ollama-models.service << EOF
[Unit]
Description=Ollama Models Service
After=ollama.service

[Service]
Type=oneshot
ExecStart=/usr/local/bin/ollama pull phi3:mini
ExecStart=/usr/local/bin/ollama pull mistral:7b
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
EOF

systemctl enable ollama-models.service
systemctl start ollama-models.service

echo "Ollama setup completed" > /var/log/ollama-setup.log
"""
        
        cmd = [
            'gcloud', 'compute', 'instances', 'create', vm_name,
            '--zone', self.zone,
            '--machine-type', 'e2-standard-4',
            '--boot-disk-size', '50GB',
            '--boot-disk-type', 'pd-ssd',
            '--image-family', 'ubuntu-2204-lts',
            '--image-project', 'ubuntu-os-cloud',
            '--tags', 'ollama-basic,http-server',
            '--metadata', f'startup-script={startup_script}',
            '--scopes', 'https://www.googleapis.com/auth/cloud-platform'
        ]
        
        try:
            print(f"   🔄 إنشاء VM: {vm_name}...")
            subprocess.run(cmd, check=True, shell=True)
            print(f"   ✅ تم إنشاء VM: {vm_name}")
            print("   ⏳ انتظار تشغيل Ollama وتحميل النماذج (5 دقائق)...")
            time.sleep(300)  # انتظار 5 دقائق لتحميل النماذج
        except subprocess.CalledProcessError:
            print(f"   ⚠️ VM {vm_name} موجود مسبقاً أو حدث خطأ")
    
    def setup_cloud_sql(self):
        """إعداد Cloud SQL"""
        print("\n🗄️ إعداد Cloud SQL...")
        
        instance_name = "anubis-mysql-instance"
        
        cmd = [
            'gcloud', 'sql', 'instances', 'create', instance_name,
            '--database-version', 'MYSQL_8_0',
            '--tier', 'db-f1-micro',
            '--region', self.region,
            '--root-password', 'AnubisSecure2024!',
            '--storage-size', '20GB',
            '--storage-type', 'SSD'
        ]
        
        try:
            print(f"   🔄 إنشاء Cloud SQL: {instance_name}...")
            subprocess.run(cmd, check=True, shell=True)
            print(f"   ✅ تم إنشاء Cloud SQL: {instance_name}")
            
            # إنشاء قاعدة البيانات
            subprocess.run([
                'gcloud', 'sql', 'databases', 'create', 'anubis_system',
                '--instance', instance_name
            ], check=True, shell=True)
            print("   ✅ تم إنشاء قاعدة البيانات: anubis_system")
            
        except subprocess.CalledProcessError:
            print(f"   ⚠️ Cloud SQL {instance_name} موجود مسبقاً أو حدث خطأ")
    
    def setup_cloud_storage(self):
        """إعداد Cloud Storage"""
        print("\n💾 إعداد Cloud Storage...")
        
        bucket_name = f"anubis-storage-{self.project_id}"
        
        try:
            print(f"   🔄 إنشاء Storage Bucket: {bucket_name}...")
            subprocess.run([
                'gsutil', 'mb', '-l', self.region, f'gs://{bucket_name}'
            ], check=True, shell=True)
            print(f"   ✅ تم إنشاء Storage Bucket: {bucket_name}")
        except subprocess.CalledProcessError:
            print(f"   ⚠️ Storage Bucket {bucket_name} موجود مسبقاً أو حدث خطأ")
    
    def prepare_code_for_deployment(self):
        """تحضير الكود للنشر"""
        print("\n📦 تحضير الكود للنشر...")
        
        # استنساخ المشروع من GitHub
        if not os.path.exists("universal-ai-assistants-agent"):
            try:
                print("   🔄 استنساخ المشروع من GitHub...")
                subprocess.run([
                    'git', 'clone', self.github_repo, 'universal-ai-assistants-agent'
                ], check=True, shell=True)
                print("   ✅ تم استنساخ المشروع")
            except subprocess.CalledProcessError:
                print("   ⚠️ مشكلة في استنساخ المشروع")
        
        # إنشاء ملفات التكوين للنشر
        self.create_deployment_configs()
    
    def create_deployment_configs(self):
        """إنشاء ملفات التكوين للنشر"""
        print("   📝 إنشاء ملفات التكوين...")
        
        # إنشاء Dockerfile
        dockerfile_content = """FROM python:3.9-slim

WORKDIR /app

# تثبيت المتطلبات الأساسية
RUN apt-get update && apt-get install -y \\
    gcc \\
    g++ \\
    && rm -rf /var/lib/apt/lists/*

# نسخ ملفات المتطلبات
COPY SHARED_REQUIREMENTS/data/requirements_anubis_horus_unified.txt requirements.txt

# تثبيت المكتبات Python
RUN pip install --no-cache-dir -r requirements.txt

# نسخ الكود
COPY . .

# تعيين متغيرات البيئة
ENV PYTHONPATH=/app
ENV GOOGLE_CLOUD_PROJECT=anubis-467210
ENV OLLAMA_HOST=http://anubis-ollama-basic:11434

# فتح المنفذ
EXPOSE 8080

# تشغيل التطبيق
CMD ["python", "QUICK_START.py"]
"""
        
        with open("Dockerfile", "w") as f:
            f.write(dockerfile_content)
        
        # إنشاء .dockerignore
        dockerignore_content = """.git
.gitignore
README.md
Dockerfile
.dockerignore
.venv
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.DS_Store
.mypy_cache
.pytest_cache
.hypothesis
"""
        
        with open(".dockerignore", "w") as f:
            f.write(dockerignore_content)
        
        print("   ✅ تم إنشاء ملفات التكوين")
    
    def deploy_to_cloud_run(self):
        """نشر التطبيق على Cloud Run"""
        print("\n🚀 نشر التطبيق على Cloud Run...")
        
        service_name = "anubis-system"
        
        try:
            print(f"   🔄 نشر الخدمة: {service_name}...")
            
            # بناء ونشر التطبيق
            cmd = [
                'gcloud', 'run', 'deploy', service_name,
                '--source', '.',
                '--region', self.region,
                '--allow-unauthenticated',
                '--memory', '2Gi',
                '--cpu', '2',
                '--max-instances', '10',
                '--set-env-vars', f'GOOGLE_CLOUD_PROJECT={self.project_id}',
                '--set-env-vars', 'OLLAMA_HOST=http://anubis-ollama-basic:11434'
            ]
            
            subprocess.run(cmd, check=True, shell=True)
            print(f"   ✅ تم نشر الخدمة: {service_name}")
            
        except subprocess.CalledProcessError as e:
            print(f"   ❌ فشل في نشر الخدمة: {str(e)}")
    
    def configure_networking(self):
        """تكوين الشبكة والأمان"""
        print("\n🌐 تكوين الشبكة والأمان...")
        
        # إنشاء firewall rules للـ Ollama
        try:
            print("   🔄 تكوين Firewall للـ Ollama...")
            subprocess.run([
                'gcloud', 'compute', 'firewall-rules', 'create', 'allow-ollama-internal',
                '--allow', 'tcp:11434',
                '--source-ranges', '10.0.0.0/8',
                '--target-tags', 'ollama-basic',
                '--description', 'Allow internal access to Ollama'
            ], check=True, shell=True)
            print("   ✅ تم تكوين Firewall للـ Ollama")
        except subprocess.CalledProcessError:
            print("   ⚠️ Firewall rule موجود مسبقاً")
        
        # إنشاء firewall rule للـ HTTP
        try:
            subprocess.run([
                'gcloud', 'compute', 'firewall-rules', 'create', 'allow-http-8080',
                '--allow', 'tcp:8080',
                '--source-ranges', '0.0.0.0/0',
                '--target-tags', 'http-server',
                '--description', 'Allow HTTP traffic on port 8080'
            ], check=True, shell=True)
            print("   ✅ تم تكوين Firewall للـ HTTP")
        except subprocess.CalledProcessError:
            print("   ⚠️ HTTP Firewall rule موجود مسبقاً")
    
    def test_deployment(self):
        """اختبار النشر"""
        print("\n🧪 اختبار النشر...")
        
        try:
            # الحصول على URL الخدمة
            result = subprocess.run([
                'gcloud', 'run', 'services', 'describe', 'anubis-system',
                '--region', self.region,
                '--format', 'value(status.url)'
            ], capture_output=True, text=True, shell=True)
            
            if result.returncode == 0:
                service_url = result.stdout.strip()
                print(f"   ✅ URL الخدمة: {service_url}")
                
                # اختبار بسيط للخدمة
                import requests
                try:
                    response = requests.get(service_url, timeout=30)
                    if response.status_code == 200:
                        print("   ✅ الخدمة تستجيب بنجاح")
                    else:
                        print(f"   ⚠️ الخدمة ترد بكود: {response.status_code}")
                except:
                    print("   ⚠️ لا يمكن الوصول للخدمة حالياً (قد تحتاج وقت للتشغيل)")
            
        except Exception as e:
            print(f"   ⚠️ مشكلة في اختبار النشر: {str(e)}")
    
    def show_deployment_summary(self):
        """عرض ملخص النشر"""
        print("\n" + "=" * 70)
        print("🎉 ملخص النشر المكتمل")
        print("=" * 70)
        
        try:
            # الحصول على معلومات الخدمات
            service_result = subprocess.run([
                'gcloud', 'run', 'services', 'describe', 'anubis-system',
                '--region', self.region,
                '--format', 'value(status.url)'
            ], capture_output=True, text=True, shell=True)
            
            vm_result = subprocess.run([
                'gcloud', 'compute', 'instances', 'describe', 'anubis-ollama-basic',
                '--zone', self.zone,
                '--format', 'value(networkInterfaces[0].accessConfigs[0].natIP)'
            ], capture_output=True, text=True, shell=True)
            
            if service_result.returncode == 0:
                service_url = service_result.stdout.strip()
                print(f"🌐 التطبيق الرئيسي: {service_url}")
            
            if vm_result.returncode == 0:
                vm_ip = vm_result.stdout.strip()
                print(f"🤖 Ollama VM IP: {vm_ip}")
                print(f"🔗 Ollama API: http://{vm_ip}:11434")
            
            print(f"🗄️ قاعدة البيانات: anubis-mysql-instance")
            print(f"💾 التخزين: gs://anubis-storage-{self.project_id}")
            print(f"🆔 المشروع: {self.project_id}")
            print(f"🌍 المنطقة: {self.region}")
            
            print("\n📊 النماذج المتاحة:")
            print("   🏠 محلي على Ollama:")
            print("      • phi3:mini - للمهام السريعة")
            print("      • mistral:7b - للمهام العامة")
            print("   ☁️ على Vertex AI:")
            print("      • gemini-pro - للمهام المتقدمة")
            print("      • claude-3 - للتحليل الأخلاقي")
            
            print("\n💰 التكلفة المتوقعة: $200-350/شهر")
            print("⚡ الأداء: متوازن وفعال")
            
        except Exception as e:
            print(f"⚠️ مشكلة في عرض الملخص: {str(e)}")
        
        print("=" * 70)
        print("🎊 تم نشر النظام المختلط بنجاح!")
        print("=" * 70)

if __name__ == "__main__":
    deployer = HybridDeployer()
    deployer.main()
