#!/bin/bash
# 🏺 نظام فحص وتحليل نشر مشروع Universal AI Assistants
# Linux/Mac Shell Script for Deployment Analysis

# تعيين ترميز UTF-8
export LANG=en_US.UTF-8
export LC_ALL=en_US.UTF-8

# ألوان للنص
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# دالة عرض القائمة
show_menu() {
    clear
    echo -e "${CYAN}"
    echo "🏺 نظام فحص وتحليل نشر مشروع Universal AI Assistants"
    echo "============================================================"
    echo -e "${NC}"
    echo ""
    echo -e "${YELLOW}📋 اختر نوع التحليل المطلوب:${NC}"
    echo ""
    echo -e "${WHITE}1. 🔍 فحص متطلبات النظام${NC}"
    echo -e "${WHITE}2. 🏥 تحليل صحة المشروع${NC}"
    echo -e "${WHITE}3. 🐳 فحص حالة Docker${NC}"
    echo -e "${WHITE}4. 📦 تحليل التبعيات${NC}"
    echo -e "${WHITE}5. 🔒 فحص الأمان${NC}"
    echo -e "${WHITE}6. 📋 قائمة فحص النشر${NC}"
    echo -e "${GREEN}7. 🚀 التحليل الشامل (محلل النشر الرئيسي)${NC}"
    echo -e "${GREEN}8. 🎯 التحليل الكامل (جميع الأوامر)${NC}"
    echo -e "${WHITE}9. 📖 عرض الدليل${NC}"
    echo -e "${RED}0. 🚪 خروج${NC}"
    echo ""
}

# دالة فحص متطلبات النظام
run_requirements() {
    echo ""
    echo -e "${YELLOW}🔍 تشغيل فحص متطلبات النظام...${NC}"
    echo -e "${YELLOW}=====================================${NC}"
    python3 DEPLOYMENT_COMMANDS.py --command requirements
    echo ""
    read -p "اضغط Enter للمتابعة..."
}

# دالة تحليل صحة المشروع
run_health() {
    echo ""
    echo -e "${YELLOW}🏥 تشغيل تحليل صحة المشروع...${NC}"
    echo -e "${YELLOW}===============================${NC}"
    python3 DEPLOYMENT_COMMANDS.py --command health
    echo ""
    read -p "اضغط Enter للمتابعة..."
}

# دالة فحص Docker
run_docker() {
    echo ""
    echo -e "${YELLOW}🐳 تشغيل فحص حالة Docker...${NC}"
    echo -e "${YELLOW}=============================${NC}"
    python3 DEPLOYMENT_COMMANDS.py --command docker
    echo ""
    read -p "اضغط Enter للمتابعة..."
}

# دالة تحليل التبعيات
run_dependencies() {
    echo ""
    echo -e "${YELLOW}📦 تشغيل تحليل التبعيات...${NC}"
    echo -e "${YELLOW}===========================${NC}"
    python3 DEPLOYMENT_COMMANDS.py --command dependencies
    echo ""
    read -p "اضغط Enter للمتابعة..."
}

# دالة فحص الأمان
run_security() {
    echo ""
    echo -e "${YELLOW}🔒 تشغيل فحص الأمان...${NC}"
    echo -e "${YELLOW}======================${NC}"
    python3 DEPLOYMENT_COMMANDS.py --command security
    echo ""
    read -p "اضغط Enter للمتابعة..."
}

# دالة قائمة فحص النشر
run_checklist() {
    echo ""
    echo -e "${YELLOW}📋 توليد قائمة فحص النشر...${NC}"
    echo -e "${YELLOW}============================${NC}"
    python3 DEPLOYMENT_COMMANDS.py --command checklist
    echo ""
    read -p "اضغط Enter للمتابعة..."
}

# دالة المحلل الرئيسي
run_main_analyzer() {
    echo ""
    echo -e "${GREEN}🚀 تشغيل المحلل الرئيسي للنشر...${NC}"
    echo -e "${GREEN}=================================${NC}"
    python3 PROJECT_DEPLOYMENT_ANALYSIS.py
    echo ""
    read -p "اضغط Enter للمتابعة..."
}

# دالة التحليل الكامل
run_full_analysis() {
    echo ""
    echo -e "${GREEN}🎯 تشغيل التحليل الكامل...${NC}"
    echo -e "${GREEN}===========================${NC}"
    python3 DEPLOYMENT_COMMANDS.py --command full
    echo ""
    read -p "اضغط Enter للمتابعة..."
}

# دالة عرض الدليل
show_guide() {
    echo ""
    echo -e "${YELLOW}📖 عرض الدليل...${NC}"
    echo -e "${YELLOW}=================${NC}"
    
    if [ -f "DEPLOYMENT_ANALYSIS_GUIDE.md" ]; then
        if command -v less &> /dev/null; then
            less DEPLOYMENT_ANALYSIS_GUIDE.md
        elif command -v more &> /dev/null; then
            more DEPLOYMENT_ANALYSIS_GUIDE.md
        else
            cat DEPLOYMENT_ANALYSIS_GUIDE.md
        fi
    else
        echo -e "${RED}❌ ملف الدليل غير موجود${NC}"
    fi
    echo ""
    read -p "اضغط Enter للمتابعة..."
}

# فحص وجود Python
check_python() {
    if ! command -v python3 &> /dev/null; then
        echo -e "${RED}❌ Python3 غير مثبت. يرجى تثبيت Python3 أولاً.${NC}"
        echo ""
        echo "للتثبيت:"
        echo "Ubuntu/Debian: sudo apt update && sudo apt install python3 python3-pip"
        echo "CentOS/RHEL: sudo yum install python3 python3-pip"
        echo "macOS: brew install python3"
        exit 1
    fi
}

# فحص وجود الملفات المطلوبة
check_files() {
    if [ ! -f "PROJECT_DEPLOYMENT_ANALYSIS.py" ]; then
        echo -e "${RED}❌ ملف PROJECT_DEPLOYMENT_ANALYSIS.py غير موجود${NC}"
        exit 1
    fi
    
    if [ ! -f "DEPLOYMENT_COMMANDS.py" ]; then
        echo -e "${RED}❌ ملف DEPLOYMENT_COMMANDS.py غير موجود${NC}"
        exit 1
    fi
}

# الحلقة الرئيسية
main() {
    # فحص المتطلبات
    check_python
    check_files
    
    while true; do
        show_menu
        read -p "اختر رقم (0-9): " choice
        
        case $choice in
            1) run_requirements ;;
            2) run_health ;;
            3) run_docker ;;
            4) run_dependencies ;;
            5) run_security ;;
            6) run_checklist ;;
            7) run_main_analyzer ;;
            8) run_full_analysis ;;
            9) show_guide ;;
            0) 
                echo ""
                echo -e "${GREEN}👋 شكراً لاستخدام نظام فحص وتحليل النشر!${NC}"
                echo ""
                exit 0
                ;;
            *)
                echo -e "${RED}❌ اختيار غير صحيح، حاول مرة أخرى${NC}"
                sleep 2
                ;;
        esac
    done
}

# تشغيل البرنامج الرئيسي
main
